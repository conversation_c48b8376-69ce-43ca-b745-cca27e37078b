/**
 * 简化轮播图组件 - 替代Bootstrap Carousel
 * 纯JavaScript实现，无依赖
 */

class SimpleCarousel {
    constructor(element, options = {}) {
        this.element = element;
        this.options = {
            interval: options.interval || 4000,
            autoplay: options.autoplay !== false,
            indicators: options.indicators !== false,
            controls: options.controls !== false,
            ...options
        };
        
        this.currentIndex = 0;
        this.items = [];
        this.isPlaying = this.options.autoplay;
        this.intervalId = null;
        
        this.init();
    }
    
    init() {
        this.createStructure();
        this.bindEvents();
        
        if (this.isPlaying) {
            this.play();
        }
    }
    
    createStructure() {
        this.element.innerHTML = `
            <div class="carousel-container">
                <div class="carousel-inner" id="${this.element.id}Inner">
                    <!-- 动态生成 -->
                </div>
                ${this.options.indicators ? `<div class="carousel-indicators" id="${this.element.id}Indicators"></div>` : ''}
                ${this.options.controls ? `
                    <button class="carousel-control carousel-control-prev" id="${this.element.id}Prev">
                        <span class="carousel-control-icon carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control carousel-control-next" id="${this.element.id}Next">
                        <span class="carousel-control-icon carousel-control-next-icon"></span>
                    </button>
                ` : ''}
                <div class="carousel-loading" id="${this.element.id}Loading">
                    <div class="spinner"></div>
                    <p>正在加载轮播图...</p>
                </div>
                <div class="carousel-empty" id="${this.element.id}Empty" style="display: none;">
                    <div class="empty-icon">🖼</div>
                    <p>暂无轮播图内容</p>
                </div>
            </div>
        `;
        
        this.inner = document.getElementById(`${this.element.id}Inner`);
        this.indicators = document.getElementById(`${this.element.id}Indicators`);
        this.prevBtn = document.getElementById(`${this.element.id}Prev`);
        this.nextBtn = document.getElementById(`${this.element.id}Next`);
        this.loading = document.getElementById(`${this.element.id}Loading`);
        this.empty = document.getElementById(`${this.element.id}Empty`);
    }
    
    bindEvents() {
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prev());
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.next());
        }
        
        // 鼠标悬停暂停
        this.element.addEventListener('mouseenter', () => this.pause());
        this.element.addEventListener('mouseleave', () => {
            if (this.isPlaying) this.play();
        });
        
        // 触摸支持
        let startX = 0;
        let endX = 0;
        
        this.element.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });
        
        this.element.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            const diff = startX - endX;
            
            if (Math.abs(diff) > 50) {
                if (diff > 0) {
                    this.next();
                } else {
                    this.prev();
                }
            }
        });
    }
    
    loadData(data) {
        this.items = data;
        this.loading.style.display = 'none';
        
        if (data.length === 0) {
            this.empty.style.display = 'flex';
            return;
        }
        
        this.empty.style.display = 'none';
        this.renderItems();
        this.renderIndicators();
        this.showItem(0);
    }
    
    renderItems() {
        this.inner.innerHTML = this.items.map((item, index) => `
            <div class="carousel-item ${index === 0 ? 'active' : ''}" data-index="${index}">
                <img src="${item.image_path}" alt="${item.title}" loading="lazy">
                ${item.title || item.description ? `
                    <div class="carousel-caption">
                        ${item.title ? `<h5>${item.title}</h5>` : ''}
                        ${item.description ? `<p>${item.description}</p>` : ''}
                    </div>
                ` : ''}
            </div>
        `).join('');
    }
    
    renderIndicators() {
        if (!this.indicators) return;
        
        this.indicators.innerHTML = this.items.map((_, index) => `
            <button class="carousel-indicator ${index === 0 ? 'active' : ''}" 
                    data-index="${index}"></button>
        `).join('');
        
        // 绑定指示器点击事件
        this.indicators.addEventListener('click', (e) => {
            if (e.target.classList.contains('carousel-indicator')) {
                const index = parseInt(e.target.dataset.index);
                this.goTo(index);
            }
        });
    }
    
    showItem(index) {
        if (this.items.length === 0) return;
        
        // 更新当前索引
        this.currentIndex = index;
        
        // 更新轮播项
        const items = this.inner.querySelectorAll('.carousel-item');
        items.forEach((item, i) => {
            item.classList.toggle('active', i === index);
        });
        
        // 更新指示器
        if (this.indicators) {
            const indicators = this.indicators.querySelectorAll('.carousel-indicator');
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        }
    }
    
    next() {
        if (this.items.length === 0) return;
        const nextIndex = (this.currentIndex + 1) % this.items.length;
        this.goTo(nextIndex);
    }
    
    prev() {
        if (this.items.length === 0) return;
        const prevIndex = (this.currentIndex - 1 + this.items.length) % this.items.length;
        this.goTo(prevIndex);
    }
    
    goTo(index) {
        if (index >= 0 && index < this.items.length) {
            this.showItem(index);
        }
    }
    
    play() {
        if (this.items.length <= 1) return;
        
        this.pause();
        this.intervalId = setInterval(() => {
            this.next();
        }, this.options.interval);
    }
    
    pause() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    destroy() {
        this.pause();
        this.element.innerHTML = '';
    }
}

// 全局轮播图管理器
window.CarouselManager = {
    carousels: new Map(),
    
    create: function(elementId, options = {}) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(`Carousel element not found: ${elementId}`);
            return null;
        }
        
        const carousel = new SimpleCarousel(element, options);
        this.carousels.set(elementId, carousel);
        return carousel;
    },
    
    get: function(elementId) {
        return this.carousels.get(elementId);
    },
    
    destroy: function(elementId) {
        const carousel = this.carousels.get(elementId);
        if (carousel) {
            carousel.destroy();
            this.carousels.delete(elementId);
        }
    },
    
    destroyAll: function() {
        this.carousels.forEach((carousel, id) => {
            carousel.destroy();
        });
        this.carousels.clear();
    }
};

// 轮播图数据加载器
window.CarouselDataLoader = {
    async loadCarouselData() {
        try {
            const response = await fetch('/api/carousel/list');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            
            if (result.success) {
                return result.data || [];
            } else {
                console.error('轮播图数据加载失败:', result.message);
                return [];
            }
        } catch (error) {
            console.error('轮播图数据加载错误:', error);
            return [];
        }
    },
    
    async initHeroCarousel() {
        const carousel = CarouselManager.create('heroCarousel', {
            interval: 4000,
            autoplay: true,
            indicators: true,
            controls: true
        });
        
        if (carousel) {
            const data = await this.loadCarouselData();
            carousel.loadData(data);
        }
        
        return carousel;
    }
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化英雄区域轮播图
    CarouselDataLoader.initHeroCarousel();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    CarouselManager.destroyAll();
});
