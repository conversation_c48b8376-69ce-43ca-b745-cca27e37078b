<!-- 检查记录表格小组件，用于嵌入到其他页面 -->
<div class="inspection-table-widget" id="inspection-table-widget-{{ widget_id|default('default') }}">
    <style nonce="{{ csp_nonce }}">
        /* 检查表格样式 */
        .inspection-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
            border: 1px solid #e3e6f0;
            font-size: 0.9rem;
        }
        
        .inspection-table th {
            background-color: #f8f9fc;
            color: #4e73df;
            font-weight: 600;
            text-align: center;
            padding: 0.5rem;
            border: 1px solid #e3e6f0;
        }
        
        .inspection-table td {
            padding: 0.5rem;
            border: 1px solid #e3e6f0;
            vertical-align: top;
        }
        
        .inspection-table .time-cell {
            background-color: #f8f9fc;
            font-weight: 600;
            text-align: center;
            width: 60px;
        }
        
        /* 检查项目单元格样式 */
        .inspection-cell {
            background-color: #f9f3e8;
            min-height: 100px;
        }
        
        /* 照片展示样式 */
        .photo-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
            margin-top: 0.25rem;
            justify-content: center;
        }
        
        .photo-thumbnail {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 0.25rem;
            border: 1px solid #e3e6f0;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .photo-thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        /* 评分星星样式 */
        .rating {
            color: #f6c23e;
            font-size: 0.7rem;
            margin-top: 0.25rem;
            text-align: center;
        }
        
        /* 检查情况样式 */
        .inspection-info {
            margin-top: 0.25rem;
            font-size: 0.8rem;
        }
        
        .inspection-info .label {
            font-weight: 600;
            margin-bottom: 0.1rem;
        }
        
        /* 加载中样式 */
        .loading-placeholder {
            text-align: center;
            padding: 2rem 0;
        }
        
        /* 错误提示样式 */
        .error-message {
            padding: 1rem;
            background-color: #f8d7da;
            color: #721c24;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
        
        /* 无数据提示样式 */
        .no-data-message {
            padding: 1rem;
            background-color: #d1ecf1;
            color: #0c5460;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
    </style>
    
    <div class="widget-content">
        <!-- 数据加载中占位符 -->
        <div class="loading-placeholder">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载检查记录...</p>
        </div>
        
        <!-- 数据将通过 AJAX 加载到这里 -->
        <div class="inspection-data" style="display: none;"></div>
        
        <!-- 错误提示 -->
        <div class="error-message" style="display: none;">
            <i class="fas fa-exclamation-circle"></i> <span class="error-text"></span>
        </div>
        
        <!-- 无数据提示 -->
        <div class="no-data-message" style="display: none;">
            <i class="fas fa-info-circle"></i> 暂无检查记录
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化检查记录表格小组件
        initInspectionTableWidget('{{ widget_id|default("default") }}', {{ log_id|default('null') }}, '{{ date_str|default("") }}');
    });
    
    // 初始化检查记录表格小组件
    function initInspectionTableWidget(widgetId, logId, dateStr) {
        const widget = document.getElementById(`inspection-table-widget-${widgetId}`);
        if (!widget) return;
        
        const contentEl = widget.querySelector('.widget-content');
        const loadingEl = widget.querySelector('.loading-placeholder');
        const dataEl = widget.querySelector('.inspection-data');
        const errorEl = widget.querySelector('.error-message');
        const errorTextEl = widget.querySelector('.error-text');
        const noDataEl = widget.querySelector('.no-data-message');
        
        // 加载检查记录数据
        function loadInspectionData() {
            // 显示加载中
            loadingEl.style.display = 'block';
            dataEl.style.display = 'none';
            errorEl.style.display = 'none';
            noDataEl.style.display = 'none';
            
            let url = '';
            
            // 根据参数构建URL
            if (logId) {
                // 如果指定了日志ID，直接获取该日志的检查记录
                url = `/daily-management/inspections/table-html/${logId}`;
            } else if (dateStr) {
                // 如果指定了日期，通过日期获取检查记录
                url = `/daily-management/inspections/table-html-by-date/${dateStr}`;
            } else {
                // 如果没有指定日志ID和日期，获取今天的检查记录
                const today = new Date().toISOString().split('T')[0];
                url = `/daily-management/inspections/table-html-by-date/${today}`;
            }
            
            // 发送请求
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    // 隐藏加载中
                    loadingEl.style.display = 'none';
                    
                    // 检查是否有数据
                    if (html.trim() === '' || html.includes('暂无检查记录')) {
                        noDataEl.style.display = 'block';
                    } else {
                        // 显示数据
                        dataEl.innerHTML = html;
                        dataEl.style.display = 'block';
                        
                        // 初始化工具提示
                        if (typeof $ !== 'undefined' && typeof $.fn.tooltip !== 'undefined') {
                            $(dataEl).find('[data-bs-toggle="tooltip"]').tooltip();
                        }
                    }
                })
                .catch(error => {
                    // 显示错误信息
                    loadingEl.style.display = 'none';
                    errorTextEl.textContent = `加载失败: ${error.message}`;
                    errorEl.style.display = 'block';
                    console.error('加载检查记录数据失败:', error);
                });
        }
        
        // 初始加载数据
        loadInspectionData();
    }
    
    // 显示大图
    function showFullImage(imageSrc, rating) {
        // 检查是否有 SweetAlert2
        if (typeof Swal === 'undefined') {
            // 如果没有 SweetAlert2，则在新窗口打开图片
            window.open(imageSrc, '_blank');
            return;
        }
        
        // 构建星级评分HTML
        let ratingHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                ratingHtml += '<i class="fas fa-star text-warning"></i>';
            } else {
                ratingHtml += '<i class="far fa-star text-warning"></i>';
            }
        }
        
        // 使用SweetAlert2显示大图
        Swal.fire({
            imageUrl: imageSrc,
            imageAlt: '检查照片',
            html: `
                <div class="mt-3">
                    <div class="rating mb-2">${ratingHtml}</div>
                </div>
            `,
            showCloseButton: true,
            showConfirmButton: false,
            width: 'auto',
            padding: '1rem',
            background: '#fff',
            backdrop: 'rgba(0,0,0,0.8)'
        });
    }
</script>
