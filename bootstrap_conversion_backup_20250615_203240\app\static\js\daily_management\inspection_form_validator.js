/**
 * 检查记录表单验证脚本
 * 
 * 提供检查记录表单的客户端验证功能
 */

/**
 * 验证检查记录表单
 * @param {HTMLFormElement} form 表单元素
 * @returns {boolean} 验证结果
 */
function validateInspectionForm(form) {
    // 清除之前的错误提示
    clearFormErrors(form);
    
    // 获取表单字段
    const dailyLogId = form.querySelector('[name="daily_log_id"]');
    const inspectionType = form.querySelector('[name="inspection_type"]');
    const inspectionItem = form.querySelector('[name="inspection_item"]');
    const status = form.querySelector('[name="status"]');
    
    // 验证必填字段
    let isValid = true;
    
    if (!dailyLogId || !dailyLogId.value) {
        showFieldError(dailyLogId, '日志ID不能为空');
        isValid = false;
    }
    
    if (!inspectionType || !inspectionType.value) {
        showFieldError(inspectionType, '检查类型不能为空');
        isValid = false;
    } else if (!['morning', 'noon', 'evening'].includes(inspectionType.value)) {
        showFieldError(inspectionType, '检查类型无效，必须是 morning, noon 或 evening');
        isValid = false;
    }
    
    if (!inspectionItem || !inspectionItem.value) {
        showFieldError(inspectionItem, '检查项目不能为空');
        isValid = false;
    }
    
    if (status && status.value && !['normal', 'abnormal'].includes(status.value)) {
        showFieldError(status, '状态无效，必须是 normal 或 abnormal');
        isValid = false;
    }
    
    return isValid;
}

/**
 * 显示字段错误提示
 * @param {HTMLElement} field 表单字段元素
 * @param {string} message 错误消息
 */
function showFieldError(field, message) {
    if (!field) return;
    
    // 添加错误样式
    field.classList.add('is-invalid');
    
    // 创建错误提示元素
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    // 添加错误提示
    field.parentNode.appendChild(errorDiv);
}

/**
 * 清除表单错误提示
 * @param {HTMLFormElement} form 表单元素
 */
function clearFormErrors(form) {
    // 清除错误样式
    const invalidFields = form.querySelectorAll('.is-invalid');
    invalidFields.forEach(field => {
        field.classList.remove('is-invalid');
    });
    
    // 清除错误提示
    const errorMessages = form.querySelectorAll('.invalid-feedback');
    errorMessages.forEach(element => {
        element.remove();
    });
}

/**
 * 使用AJAX提交检查记录表单
 * @param {HTMLFormElement} form 表单元素
 * @param {Function} successCallback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function submitInspectionForm(form, successCallback, errorCallback) {
    // 验证表单
    if (!validateInspectionForm(form)) {
        if (errorCallback) {
            errorCallback('表单验证失败');
        }
        return;
    }
    
    // 获取表单数据
    const formData = new FormData(form);
    const jsonData = {};
    
    // 转换为JSON对象
    for (const [key, value] of formData.entries()) {
        jsonData[key] = value;
    }
    
    // 获取提交URL
    const url = form.getAttribute('action') || '/daily-management/api/inspections/';
    const method = form.getAttribute('method') || 'POST';
    
    // 发送AJAX请求
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(jsonData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (successCallback) {
                successCallback(data);
            }
        } else {
            if (errorCallback) {
                errorCallback(data.message || '提交失败');
            }
        }
    })
    .catch(error => {
        console.error('提交失败:', error);
        if (errorCallback) {
            errorCallback('提交失败: ' + error.message);
        }
    });
}

/**
 * 初始化检查记录表单验证
 */
function initInspectionFormValidation() {
    // 获取所有检查记录表单
    const forms = document.querySelectorAll('.inspection-form');
    
    forms.forEach(form => {
        // 添加提交事件监听器
        form.addEventListener('submit', function(event) {
            // 阻止表单默认提交
            event.preventDefault();
            
            // 提交表单
            submitInspectionForm(
                form,
                function(data) {
                    // 成功回调
                    showSuccessMessage(data.message || '提交成功');
                    
                    // 如果有回调函数，则调用
                    if (typeof window.onInspectionFormSuccess === 'function') {
                        window.onInspectionFormSuccess(data);
                    }
                },
                function(errorMessage) {
                    // 错误回调
                    showErrorMessage(errorMessage);
                    
                    // 如果有回调函数，则调用
                    if (typeof window.onInspectionFormError === 'function') {
                        window.onInspectionFormError(errorMessage);
                    }
                }
            );
        });
    });
}

/**
 * 显示成功消息
 * @param {string} message 消息内容
 */
function showSuccessMessage(message) {
    // 使用SweetAlert2显示成功消息
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '成功',
            text: message,
            icon: 'success',
            confirmButtonText: '确定'
        });
    } else {
        alert(message);
    }
}

/**
 * 显示错误消息
 * @param {string} message 消息内容
 */
function showErrorMessage(message) {
    // 使用SweetAlert2显示错误消息
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '错误',
            text: message,
            icon: 'error',
            confirmButtonText: '确定'
        });
    } else {
        alert('错误: ' + message);
    }
}

// 页面加载完成后初始化表单验证
document.addEventListener('DOMContentLoaded', initInspectionFormValidation);
