<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style nonce="{{ csp_nonce }}">
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* A4横向打印设置 */
        @page {
            size: A4 landscape;
            margin: 1cm 0.8cm;
        }

        html, body {
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            background: white;
            width: 100%;
            height: 100%;
        }

        .container {
            width: 100%;
            max-width: none;
            padding: 0;
            margin: 0;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #000;
        }

        .subtitle {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }

        .print-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 15px;
        }

        /* 菜单表格 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
        }

        .menu-table th,
        .menu-table td {
            border: 1px solid #000;
            padding: 8px 6px;
            vertical-align: top;
            word-wrap: break-word;
        }

        .menu-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            font-size: 13px;
        }

        /* 列宽设置 - 优化A4横向布局 */
        .date-col {
            width: 10%;
            min-width: 80px;
        }

        .meal-col {
            width: 30%;
            min-width: 180px;
        }

        /* 日期单元格 */
        .date-cell {
            text-align: center;
            font-weight: bold;
            font-size: 11px;
            background-color: #fafafa;
        }

        .date-cell .weekday {
            font-size: 12px;
            margin-bottom: 3px;
        }

        .date-cell .date {
            font-size: 10px;
            color: #666;
        }

        /* 餐次单元格 */
        .meal-cell {
            text-align: left;
            padding: 4px;
            vertical-align: top;
            min-height: 60px;
        }

        .meal-list {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 3px;
            align-content: flex-start;
        }

        .meal-list li {
            display: inline-block;
            background-color: #f8f9fa;
            border: 1px solid #ccc;
            border-radius: 2px;
            padding: 2px 5px;
            margin: 1px;
            font-size: 12px;
            line-height: 1.2;
            white-space: nowrap;
            flex: 0 0 auto;
            max-width: calc(33.33% - 4px);
            overflow: hidden;
            text-overflow: ellipsis;
            box-sizing: border-box;
        }

        .meal-list li:before {
            content: none;
        }

        .no-meal {
            color: #999;
            font-style: italic;
            font-size: 10px;
            padding: 10px;
            text-align: center;
        }

        /* 屏幕预览样式 */
        @d-flex screen {
            body {
                background: #f5f5f5;
                padding: 20px;
            }

            .container {
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                padding: 20px;
                margin: 0 auto;
                max-width: 1000px;
            }

            .action-buttons {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
            }

            .action-buttons .btn {
                margin-left: 5px;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                font-size: 12px;
            }

            .btn-primary {
                background: #007bff;
                color: white;
            }

            .btn-info {
                background: #17a2b8;
                color: white;
            }

            .btn-secondary {
                background: #6c757d;
                color: white;
            }

            .btn:hover {
                opacity: 0.8;
            }
        }

        @d-flex print {
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- 操作按钮（仅在屏幕上显示） -->
    <div class="action-buttons no-print">
        <a href="{{ url_for('weekly_menu_v2.print_menu', id=weekly_menu.id) }}" class="btn btn-primary" target="_blank">
            <i class="fas fa-print"></i> 打印菜单
        </a>
        {% if weekly_menu.status == '计划中' %}
            <a href="{{ url_for('weekly_menu_v2.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start) }}" class="btn btn-info">
                <i class="fas fa-edit"></i> 编辑菜单
            </a>
        {% endif %}
        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary">
            <i class="fas fa-list"></i> 返回列表
        </a>
    </div>

    <div class="container">
        <!-- 页眉 -->
        <div class="header">
            <div class="title">{{ weekly_menu.area.name }}周菜单计划表</div>
            <div class="subtitle">
                {{ weekly_menu.week_start.strftime('%Y年%m月%d日') }} 至 {{ weekly_menu.week_end.strftime('%Y年%m月%d日') }}
            </div>
            <div class="print-info">
                状态：{{ weekly_menu.status }} | 创建时间：{{ weekly_menu.created_at.strftime('%Y年%m月%d日') }}
            </div>
        </div>

        <!-- 菜单表格 -->
        <table class="menu-table">
            <thead>
                <tr>
                    <th class="date-col">日期</th>
                    <th class="meal-col">早餐</th>
                    <th class="meal-col">午餐</th>
                    <th class="meal-col">晚餐</th>
                </tr>
            </thead>
            <tbody>
                {% for date_str, day_data in week_data.items() %}
                <tr>
                    <td class="date-cell">
                        <div class="weekday">{{ day_data.weekday }}</div>
                        <div class="date">{{ date_str[5:] }}</div>
                    </td>
                    {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                    <td class="meal-cell">
                        {% if day_data.meals[meal_type] %}
                        <ul class="meal-list">
                            {% for recipe in day_data.meals[meal_type] %}
                            <li>{{ recipe.name }}</li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <div class="no-meal">未安排</div>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
</body>
</html>
