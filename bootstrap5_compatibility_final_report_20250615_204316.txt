Bootstrap 5 兼容性最终检查报告
==================================================

检查时间: 2025-06-15T20:43:16.503532
扫描文件: 403
发现问题: 155

Bootstrap 5不再依赖jQuery，但仍支持jQuery语法:
----------------------------------------
文件: app\templates\admin\data_management.html
行号: 456
匹配: $('#confirmModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\data_management.html
行号: 460
匹配: $('#confirmModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\users.html
行号: 436
匹配: $('#batchDeleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\users.html
行号: 441
匹配: $('#batchAssignRolesModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\users.html
行号: 498
匹配: $('#deleteUserModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\guide_management\demo_data.html
行号: 263
匹配: $('#demoDataModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\guide_management\scenarios.html
行号: 241
匹配: $('#scenarioPreviewModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\guide_management\scenarios.html
行号: 258
匹配: $('#scenarioEditModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\guide_management\scenarios.html
行号: 288
匹配: $('#scenarioPreviewModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\admin\guide_management\scenarios.html
行号: 294
匹配: $('#scenarioEditModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\fixed_inspection_qrcode.html
行号: 209
匹配: $('#successModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\inspection_templates.html
行号: 502
匹配: $('#createTemplateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\inspection_templates.html
行号: 615
匹配: $('#editTemplateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\inspection_templates.html
行号: 676
匹配: $('#editTemplateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\optimized_dashboard.html
行号: 761
匹配: $('#customDateRangeModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\optimized_dashboard.html
行号: 816
匹配: $('#customDateRangeModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\simplified_inspection.html
行号: 558
匹配: $(modal).modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\trainings.html
行号: 116
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\view_training.html
行号: 166
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\components\data_visualization.html
行号: 215
匹配: $('#customDateRangeModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\components\data_visualization.html
行号: 229
匹配: $('#customDateRangeModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\components\data_visualization.html
行号: 263
匹配: $('#customDateRangeModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\daily_management\widgets\image_widget.html
行号: 482
匹配: $(previewModal).modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\financial\accounting_subjects\form.html
行号: 778
匹配: $('#parentSubjectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\financial\accounting_subjects\form.html
行号: 826
匹配: $('#parentSubjectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\financial\vouchers\index.html
行号: 1079
匹配: $('#rejectVoucherModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\financial\vouchers\index.html
行号: 1137
匹配: $('#batchGenerateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\financial\vouchers\index.html
行号: 1291
匹配: $('#batchReviewModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\guide\scenario_selection.html
行号: 202
匹配: $('#scenarioSelectionModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\categories.html
行号: 226
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\categories.html
行号: 243
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\categories.html
行号: 247
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\index.html
行号: 236
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\index.html
行号: 253
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\index.html
行号: 257
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\index_category.html
行号: 245
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\index_category.html
行号: 262
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\ingredient\index_category.html
行号: 266
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\inspection\edit.html
行号: 335
匹配: $('#imageModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\inspection\view.html
行号: 282
匹配: $('#imageModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\inventory\statistics.html
行号: 625
匹配: $(modal).modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\main\canteen_dashboard_new.html
行号: 866
匹配: $('#scenarioSelectionModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\main\index.html
行号: 1804
匹配: $('#videoModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\create_form.html
行号: 764
匹配: $('#ingredientModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\create_form.html
行号: 872
匹配: $('#ingredientModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\create_from_menu.html
行号: 1057
匹配: $('#ingredientsPreviewModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 860
匹配: $('#confirmModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 890
匹配: $('#confirmModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 898
匹配: $('#cancelModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 938
匹配: $('#cancelModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 946
匹配: $('#deliverModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 981
匹配: $('#deliverModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 989
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 1017
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 1322
匹配: $('#confirmModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 1328
匹配: $('#cancelModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 1334
匹配: $('#deliverModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\index.html
行号: 1340
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 802
匹配: $('#confirmModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 816
匹配: $('#confirmModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 832
匹配: $('#cancelModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 854
匹配: $('#cancelModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 870
匹配: $('#deliverModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 887
匹配: $('#deliverModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 903
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\purchase_order\view.html
行号: 926
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\categories.html
行号: 82
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\categories.html
行号: 99
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\categories.html
行号: 103
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\form_simplified.html
行号: 368
匹配: $('#ingredientModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\index.html
行号: 535
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\index.html
行号: 568
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\index.html
行号: 583
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\view.html
行号: 395
匹配: $('#rateRecipeModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\recipe\view.html
行号: 439
匹配: $('#rateRecipeModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\batch_editor_simplified_scripts.html
行号: 534
匹配: $('#uploadDocumentModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\edit.html
行号: 1008
匹配: $('#purchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\edit.html
行号: 1105
匹配: $('#purchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\edit.html
行号: 1361
匹配: $('#documentUploadModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\edit.html
行号: 1380
匹配: $('#documentUploadModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\edit.html
行号: 1384
匹配: $('#associateItemsModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\edit.html
行号: 1417
匹配: $('#associateItemsModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\edit.html
行号: 1453
匹配: $('#inspectionModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\form.html
行号: 755
匹配: $('#orderPreviewModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\view.html
行号: 1050
匹配: $('#docItemsModal' + documentId).modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\view.html
行号: 1051
匹配: $('#associateItemsModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\view.html
行号: 1076
匹配: $('#associateItemsModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\view.html
行号: 1105
匹配: $('#uploadDocumentModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\wizard.html
行号: 741
匹配: $('#selectPurchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\wizard.html
行号: 761
匹配: $('#selectPurchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\wizard.html
行号: 958
匹配: $('#purchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\wizard.html
行号: 1163
匹配: $('#purchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\wizard_simple.html
行号: 107
匹配: $('#purchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\stock_in\wizard_simple.html
行号: 184
匹配: $('#purchaseOrderModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\category_index.html
行号: 103
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\category_index.html
行号: 120
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\category_index.html
行号: 124
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\certificate_index.html
行号: 216
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\certificate_index.html
行号: 233
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\certificate_index.html
行号: 237
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\certificate_view.html
行号: 163
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\certificate_view.html
行号: 180
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\certificate_view.html
行号: 184
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\certificate_view.html
行号: 195
匹配: $('#imageModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\index.html
行号: 347
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\index.html
行号: 364
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\index.html
行号: 368
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 416
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 433
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 437
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 527
匹配: $('#batchOperationModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 547
匹配: $('#batchOperationModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 567
匹配: $('#batchOperationModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 587
匹配: $('#batchOperationModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 604
匹配: $('#batchOperationModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 612
匹配: $('#rejectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 638
匹配: $('#rejectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 642
匹配: $('#rejectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 690
匹配: $('#batchOperationModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_index.html
行号: 694
匹配: $('#batchOperationModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 325
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 342
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 346
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 357
匹配: $('#deleteParamModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 374
匹配: $('#deleteParamModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 378
匹配: $('#deleteParamModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 455
匹配: $('#rejectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 481
匹配: $('#rejectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\product_view.html
行号: 485
匹配: $('#rejectModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 284
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 301
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 309
匹配: $('#deleteModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 320
匹配: $('#terminateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 337
匹配: $('#terminateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 345
匹配: $('#terminateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 356
匹配: $('#activateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 373
匹配: $('#activateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\supplier\school_index.html
行号: 381
匹配: $('#activateModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\templates\weekly_menu\plan.html
行号: 1678
匹配: $(modal).modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\image_uploader.js
行号: 669
匹配: $(previewModal).modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\mobile-dropdown-fix.js
行号: 168
匹配: $('.navbar-nav .dropdown-toggle').dropdown(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\mobile-enhancements.js
行号: 687
匹配: $(modal).modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\user_guide.js
行号: 111
匹配: $('#videoModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\user_guide.js
行号: 134
匹配: $('#guideStepModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\user_guide.js
行号: 386
匹配: $('#guideStepModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\user_guide.js
行号: 391
匹配: $('#guideStepModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\user_guide.js
行号: 402
匹配: $('#guideStepModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\user_guide.js
行号: 406
匹配: $('#guideStepModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\video-management.js
行号: 10
匹配: $('#uploadVideoModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\video-management.js
行号: 27
匹配: $('#videoPreviewModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\video-management.js
行号: 77
匹配: $('#uploadVideoModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\weekly_menu_modal.js
行号: 955
匹配: $('#menuModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\weekly_menu_modal.js
行号: 1218
匹配: $('#menuModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\weekly_menu_v2.js
行号: 958
匹配: $('#menuModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript

文件: app\static\js\weekly_menu_v2.js
行号: 1227
匹配: $('#menuModal').modal(
解决方案: 可以继续使用，但建议迁移到原生JavaScript


