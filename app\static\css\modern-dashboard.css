/* 现代化仪表板样式 - Bootstrap 5.3.6 增强 */

/* 简洁设计变量 */
:root {
  --clean-border-radius: 4px;
  --clean-border-radius-sm: 3px;
  --clean-border-radius-lg: 6px;
  --clean-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --clean-shadow-hover: 0 2px 6px rgba(0, 0, 0, 0.15);
  --clean-transition: all 0.2s ease;
  --clean-primary: #007bff;
  --clean-secondary: #6c757d;
  --clean-success: #28a745;
  --clean-warning: #ffc107;
  --clean-danger: #dc3545;
  --clean-info: #17a2b8;
}

/* 简洁卡片样式 */
.modern-card {
  border: 1px solid #dee2e6 !important;
  border-radius: var(--clean-border-radius) !important;
  box-shadow: none !important;
  transition: none !important;
  background: #fff;
}

.modern-card:hover {
  border-color: #adb5bd;
}

.modern-card-sm {
  border-radius: var(--clean-border-radius-sm) !important;
}

.modern-card-lg {
  border-radius: var(--clean-border-radius-lg) !important;
}

/* 简洁按钮样式 */
.modern-btn {
  border-radius: var(--clean-border-radius) !important;
  font-weight: normal;
  padding: 0.5rem 1rem;
  transition: none !important;
  box-shadow: none !important;
}

.modern-btn:hover {
  /* 移除所有悬停动画效果 */
}

.modern-btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.modern-btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* 简洁按钮颜色 */
.btn-gradient-1 {
  background: var(--clean-primary);
  color: white;
  border: 1px solid var(--clean-primary);
}

.btn-gradient-2 {
  background: var(--clean-success);
  color: white;
  border: 1px solid var(--clean-success);
}

.btn-gradient-3 {
  background: var(--clean-info);
  color: white;
  border: 1px solid var(--clean-info);
}

.btn-gradient-4 {
  background: var(--clean-warning);
  color: white;
  border: 1px solid var(--clean-warning);
}

/* 现代化表格样式 */
.modern-table {
  border-radius: var(--modern-border-radius) !important;
  overflow: hidden;
  box-shadow: var(--modern-shadow);
}

.modern-table .table {
  margin-bottom: 0;
}

.modern-table .table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  font-weight: 600;
  color: #495057;
  padding: 1rem;
}

.modern-table .table tbody td {
  border: none;
  padding: 1rem;
  vertical-align: middle;
}

.modern-table .table tbody tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--modern-transition);
}

.modern-table .table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.02);
  transform: scale(1.01);
}

/* 现代化表单样式 */
.modern-form .form-control,
.modern-form .form-select {
  border-radius: var(--modern-border-radius-sm) !important;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  transition: var(--modern-transition);
}

.modern-form .form-control:focus,
.modern-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
  transform: translateY(-1px);
}

.modern-form .form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

/* 现代化徽章样式 */
.modern-badge {
  border-radius: var(--modern-border-radius-sm) !important;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: 1px solid;
}

/* 现代化导航样式 */
.modern-nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--modern-border-radius);
  box-shadow: var(--modern-shadow);
}

.modern-nav .nav-link {
  border-radius: var(--modern-border-radius-sm);
  transition: var(--modern-transition);
  font-weight: 500;
}

.modern-nav .nav-link:hover {
  background-color: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.modern-nav .nav-link.active {
  background: var(--modern-gradient-1);
  color: white !important;
}

/* 现代化面包屑样式 */
.modern-breadcrumb {
  background: rgba(102, 126, 234, 0.05);
  border-radius: var(--modern-border-radius-sm);
  padding: 0.75rem 1rem;
}

.modern-breadcrumb .breadcrumb {
  margin-bottom: 0;
}

.modern-breadcrumb .breadcrumb-item a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.modern-breadcrumb .breadcrumb-item a:hover {
  color: #5a6fd8;
}

/* 现代化分页样式 */
.modern-pagination .page-link {
  border-radius: var(--modern-border-radius-sm) !important;
  border: none;
  margin: 0 0.25rem;
  font-weight: 500;
  transition: var(--modern-transition);
}

.modern-pagination .page-link:hover {
  background: var(--modern-gradient-1);
  color: white;
  transform: translateY(-2px);
}

.modern-pagination .page-item.active .page-link {
  background: var(--modern-gradient-1);
  border: none;
}

/* 现代化模态框样式 */
.modern-modal .modal-content {
  border: none;
  border-radius: var(--modern-border-radius) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.modern-modal .modal-header {
  background: var(--modern-gradient-1);
  color: white;
  border: none;
  border-radius: var(--modern-border-radius) var(--modern-border-radius) 0 0 !important;
}

.modern-modal .modal-footer {
  border: none;
  background: #f8f9fa;
}

/* 现代化提示框样式 */
.modern-alert {
  border: none;
  border-radius: var(--modern-border-radius) !important;
  border-left: 4px solid;
  box-shadow: var(--modern-shadow);
}

.modern-alert.alert-primary {
  border-left-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.modern-alert.alert-success {
  border-left-color: #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 134, 55, 0.05) 100%);
}

.modern-alert.alert-warning {
  border-left-color: #ffc107;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 173, 0, 0.05) 100%);
}

.modern-alert.alert-danger {
  border-left-color: #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(200, 35, 51, 0.05) 100%);
}

/* 现代化进度条样式 */
.modern-progress {
  height: 8px;
  border-radius: var(--modern-border-radius-sm);
  background: #e9ecef;
  overflow: hidden;
}

.modern-progress .progress-bar {
  background: var(--modern-gradient-1);
  border-radius: var(--modern-border-radius-sm);
  transition: var(--modern-transition);
}

/* 现代化加载动画 */
.modern-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: modernSpin 1s linear infinite;
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 现代化工具提示样式 */
.modern-tooltip .tooltip-inner {
  background: rgba(0, 0, 0, 0.9);
  border-radius: var(--modern-border-radius-sm);
  font-weight: 500;
  padding: 0.5rem 1rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .modern-card {
    border-radius: var(--modern-border-radius-sm) !important;
    margin-bottom: 1rem;
  }
  
  .modern-btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .modern-table {
    border-radius: var(--modern-border-radius-sm) !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --modern-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --modern-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.4);
  }
  
  .modern-card {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .modern-table .table thead th {
    background: #4a5568;
    color: #e2e8f0;
  }
  
  .modern-table .table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}

/* 动画增强 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* 实用类 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.text-gradient {
  background: var(--modern-gradient-1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}
