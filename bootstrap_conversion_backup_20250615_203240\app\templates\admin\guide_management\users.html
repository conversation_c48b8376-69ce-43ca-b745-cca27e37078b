{% extends "base.html" %}

{% block title %}用户引导状态管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-users me-2"></i>用户引导状态管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('system.index') }}">系统管理</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('guide_management.dashboard') }}">引导管理</a></li>
                    <li class="breadcrumb-item active">用户状态</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-warning" data-onclick="bulkResetGuides()">
                <i class="fas fa-redo me-1"></i>批量重置
            </button>
            <a href="{{ url_for('guide_management.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回
            </a>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="搜索用户名、姓名或学校名称" value="{{ search }}">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" name="status">
                                <option value="">所有状态</option>
                                <option value="not_started" {% if status_filter == 'not_started' %}selected{% endif %}>未开始</option>
                                <option value="in_progress" {% if status_filter == 'in_progress' %}selected{% endif %}>进行中</option>
                                <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>已完成</option>
                                <option value="skipped" {% if status_filter == 'skipped' %}selected{% endif %}>已跳过</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" name="school_type">
                                <option value="">所有学校类型</option>
                                <option value="primary" {% if school_type == 'primary' %}selected{% endif %}>小学</option>
                                <option value="middle" {% if school_type == 'middle' %}selected{% endif %}>中学</option>
                                <option value="high" {% if school_type == 'high' %}selected{% endif %}>高中</option>
                                <option value="vocational" {% if school_type == 'vocational' %}selected{% endif %}>职业学校</option>
                                <option value="university" {% if school_type == 'university' %}selected{% endif %}>大学</option>
                                <option value="rural" {% if school_type == 'rural' %}selected{% endif %}>乡村学校</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>搜索
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">用户引导状态列表</h5>
                </div>
                <div class="card-body">
                    {% if user_guide_data %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>用户信息</th>
                                    <th>学校类型</th>
                                    <th>引导状态</th>
                                    <th>当前步骤</th>
                                    <th>完成进度</th>
                                    <th>开始时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in user_guide_data %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox" value="{{ data.user.id }}">
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ data.user.real_name or data.user.username }}</strong>
                                            <br>
                                            <small class="text-muted">{{ data.user.username }}</small>
                                            {% if data.user.area %}
                                            <br>
                                            <small class="text-info">{{ data.user.area.name }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% set type_names = {
                                            'primary': '小学',
                                            'middle': '中学', 
                                            'high': '高中',
                                            'vocational': '职业学校',
                                            'university': '大学',
                                            'rural': '乡村学校'
                                        } %}
                                        <span class="badge badge-info">
                                            {{ type_names.get(data.school_type, '未知') }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if data.guide_status.is_active %}
                                            {% if data.guide_status.current_step == 'completed' %}
                                                <span class="badge badge-success">已完成</span>
                                            {% else %}
                                                <span class="badge badge-primary">进行中</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge badge-secondary">未开始</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if data.guide_status.current_step %}
                                            {% set step_names = {
                                                'welcome': '欢迎介绍',
                                                'daily_management': '日常管理',
                                                'suppliers': '供应商管理',
                                                'ingredients_recipes': '食材食谱',
                                                'weekly_menu': '周菜单',
                                                'purchase_order': '采购订单',
                                                'stock_in': '食材入库',
                                                'consumption_plan': '消耗计划',
                                                'stock_out': '食材出库',
                                                'traceability': '溯源管理',
                                                'food_samples': '留样记录',
                                                'completed': '已完成'
                                            } %}
                                            {{ step_names.get(data.guide_status.current_step, data.guide_status.current_step) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if data.guide_status.completed_steps %}
                                            {% set progress = (data.guide_status.completed_steps|length / 11 * 100)|round %}
                                            <div class="progress" style="width: 100px;">
                                                <div class="progress-bar" style="width: {{ progress }}%"></div>
                                            </div>
                                            <small>{{ progress }}%</small>
                                        {% else %}
                                            <div class="progress" style="width: 100px;">
                                                <div class="progress-bar" style="width: 0%"></div>
                                            </div>
                                            <small>0%</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if data.guide_status.started_at %}
                                            {{ data.guide_status.started_at[:19] }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-warning"
                                                    data-user-id="{{ data.user.id }}"
                                                    data-username="{{ data.user.username or data.user.real_name or 'ID:' + data.user.id|string }}"
                                                    onclick="resetUserGuide(this.getAttribute('data-user-id'), this.getAttribute('data-username'))"
                                                    title="重置引导">
                                                <i class="fas fa-redo"></i>
                                            </button>
                                            <button class="btn btn-outline-success"
                                                    data-user-id="{{ data.user.id }}"
                                                    data-username="{{ data.user.username or data.user.real_name or 'ID:' + data.user.id|string }}"
                                                    onclick="forceCompleteGuide(this.getAttribute('data-user-id'), this.getAttribute('data-username'))"
                                                    title="标记完成">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% if data.user.area %}
                                            <button class="btn btn-outline-danger"
                                                    data-area-id="{{ data.user.area.id }}"
                                                    data-area-name="{{ data.user.area.name }}"
                                                    onclick="cleanDemoData(this.getAttribute('data-area-id'), this.getAttribute('data-area-name'))"
                                                    title="清理演示数据">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if users.pages > 1 %}
                    <nav aria-label="用户列表分页">
                        <ul class="pagination justify-content-center">
                            {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('guide_management.user_guide_status', page=users.prev_num, search=search, status=status_filter, school_type=school_type) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in users.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != users.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('guide_management.user_guide_status', page=page_num, search=search, status=status_filter, school_type=school_type) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('guide_management.user_guide_status', page=users.next_num, search=search, status=status_filter, school_type=school_type) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h5>暂无用户数据</h5>
                        <p>没有找到符合条件的用户</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 全选功能
    $('#selectAll').change(function() {
        $('.user-checkbox').prop('checked', this.checked);
    });
});

// 重置用户引导
function resetUserGuide(userId, username) {
    if (confirm(`确定要重置用户 ${username} 的引导状态吗？`)) {
        $.ajax({
            url: `/admin/guide/api/reset-user-guide/${userId}`,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('重置失败：' + data.message);
                }
            },
            error: function() {
                alert('重置失败，请稍后重试');
            }
        });
    }
}

// 强制完成引导
function forceCompleteGuide(userId, username) {
    if (confirm(`确定要将用户 ${username} 的引导标记为完成吗？`)) {
        $.ajax({
            url: `/admin/guide/api/force-complete-guide/${userId}`,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('操作失败：' + data.message);
                }
            },
            error: function() {
                alert('操作失败，请稍后重试');
            }
        });
    }
}

// 清理演示数据
function cleanDemoData(areaId, areaName) {
    if (confirm(`确定要清理区域 ${areaName} 的演示数据吗？\n\n此操作将删除所有包含"演示"标记的数据，不可恢复！`)) {
        $.ajax({
            url: `/admin/guide/api/clean-demo-data/${areaId}`,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    alert(`${data.message}\n清理了 ${data.cleaned_count} 条记录`);
                } else {
                    alert('清理失败：' + data.message);
                }
            },
            error: function() {
                alert('清理失败，请稍后重试');
            }
        });
    }
}

// 批量重置引导
function bulkResetGuides() {
    const selectedUsers = $('.user-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedUsers.length === 0) {
        alert('请先选择要重置的用户');
        return;
    }
    
    if (confirm(`确定要重置选中的 ${selectedUsers.length} 个用户的引导状态吗？`)) {
        $.ajax({
            url: '/admin/guide/api/bulk-reset-guides',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                user_ids: selectedUsers
            }),
            success: function(data) {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('批量重置失败：' + data.message);
                }
            },
            error: function() {
                alert('批量重置失败，请稍后重试');
            }
        });
    }
}
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>