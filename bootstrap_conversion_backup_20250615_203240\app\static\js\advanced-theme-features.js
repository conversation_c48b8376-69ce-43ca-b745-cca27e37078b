/**
 * 高级主题功能 - 基于专业配色分析实现
 * 包含动态响应、无障碍支持、使用时间管理等功能
 */

class AdvancedThemeManager {
    constructor() {
        this.darkModeStartTime = null;
        this.usageTimer = null;
        this.environmentLightSensor = null;
        this.colorBlindMode = false;
        
        this.init();
    }

    init() {
        this.setupDarkModeTimer();
        this.setupEnvironmentLightDetection();
        this.setupAccessibilityFeatures();
        this.setupCulturalAdaptation();
        this.setupSeasonalFeedback();
        this.bindEvents();
    }

    // 深色模式使用时间管理
    setupDarkModeTimer() {
        const currentTheme = document.body.getAttribute('data-theme');
        
        if (currentTheme === 'dark') {
            this.startDarkModeTimer();
        }

        // 监听主题切换
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'data-theme') {
                    const newTheme = document.body.getAttribute('data-theme');
                    if (newTheme === 'dark') {
                        this.startDarkModeTimer();
                    } else {
                        this.stopDarkModeTimer();
                    }
                }
            });
        });

        observer.observe(document.body, { attributes: true });
    }

    startDarkModeTimer() {
        this.darkModeStartTime = Date.now();
        
        this.usageTimer = setInterval(() => {
            const usageHours = (Date.now() - this.darkModeStartTime) / (1000 * 60 * 60);
            
            if (usageHours >= 2) {
                this.showDarkModeWarning(Math.floor(usageHours));
            }
        }, 30 * 60 * 1000); // 每30分钟检查一次
    }

    stopDarkModeTimer() {
        if (this.usageTimer) {
            clearInterval(this.usageTimer);
            this.usageTimer = null;
        }
        this.darkModeStartTime = null;
        this.hideDarkModeWarning();
    }

    showDarkModeWarning(hours) {
        let warning = document.querySelector('.dark-mode-warning');
        
        if (!warning) {
            warning = document.createElement('div');
            warning.className = 'dark-mode-warning usage-timer';
            warning.innerHTML = `
                <div style="position: fixed; top: 20px; right: 20px; background: rgba(0, 243, 255, 0.1); 
                     color: #00F3FF; padding: 12px 16px; border-radius: 8px; font-size: 0.9rem; 
                     z-index: 9999; border: 1px solid #00F3FF; backdrop-filter: blur(10px);">
                    <i class="fas fa-moon"></i> 深色模式已使用 ${hours} 小时
                    <button onclick="this.parentElement.parentElement.remove()" 
                            style="background: none; border: none; color: #00F3FF; margin-left: 10px; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(warning);
        }
    }

    hideDarkModeWarning() {
        const warning = document.querySelector('.dark-mode-warning');
        if (warning) {
            warning.remove();
        }
    }

    // 环境光检测和自动调节
    setupEnvironmentLightDetection() {
        if ('AmbientLightSensor' in window) {
            try {
                this.environmentLightSensor = new AmbientLightSensor();
                this.environmentLightSensor.addEventListener('reading', () => {
                    this.adjustThemeByLight(this.environmentLightSensor.illuminance);
                });
                this.environmentLightSensor.start();
            } catch (error) {
                console.log('环境光传感器不可用，使用时间基础调节');
                this.setupTimeBasedAdjustment();
            }
        } else {
            this.setupTimeBasedAdjustment();
        }
    }

    setupTimeBasedAdjustment() {
        // 基于时间的自动调节（22:00后降低蓝光饱和度）
        const checkTime = () => {
            const hour = new Date().getHours();
            const isNightTime = hour >= 22 || hour <= 6;
            
            document.documentElement.style.setProperty(
                '--auto-saturation', 
                isNightTime ? '0.8' : '1.0'
            );
        };

        checkTime();
        setInterval(checkTime, 60 * 60 * 1000); // 每小时检查一次
    }

    adjustThemeByLight(illuminance) {
        // 根据环境光调节饱和度
        let saturation = 1.0;
        
        if (illuminance < 10) { // 很暗
            saturation = 0.7;
        } else if (illuminance < 50) { // 较暗
            saturation = 0.85;
        } else if (illuminance > 1000) { // 很亮
            saturation = 1.1;
        }

        document.documentElement.style.setProperty('--auto-saturation', saturation);
    }

    // 无障碍功能设置
    setupAccessibilityFeatures() {
        // 应急对比度切换按钮
        this.createEmergencyContrastButton();
        
        // 色弱用户增强模式
        this.setupColorBlindSupport();
        
        // 高对比度模式检测
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            document.body.classList.add('high-contrast-mode');
        }
    }

    createEmergencyContrastButton() {
        const button = document.createElement('button');
        button.innerHTML = '<i class="fas fa-adjust"></i>';
        button.title = '应急对比度切换';
        button.style.cssText = `
            position: fixed;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            background: #000;
            color: #fff;
            border: 2px solid #fff;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            z-index: 10000;
            cursor: pointer;
            display: none;
        `;
        
        button.addEventListener('click', () => {
            document.body.classList.toggle('emergency-contrast');
        });

        // 双击显示应急按钮
        let clickCount = 0;
        document.addEventListener('click', () => {
            clickCount++;
            setTimeout(() => { clickCount = 0; }, 500);
            
            if (clickCount === 3) { // 三击显示
                button.style.display = button.style.display === 'none' ? 'block' : 'none';
            }
        });

        document.body.appendChild(button);
    }

    setupColorBlindSupport() {
        // 为自然绿主题添加色弱增强模式
        const toggleColorBlindMode = () => {
            this.colorBlindMode = !this.colorBlindMode;
            
            if (this.colorBlindMode && document.body.getAttribute('data-theme') === 'success') {
                document.body.classList.add('colorblind-enhanced');
                this.showNotification('已启用色弱增强模式', 'success');
            } else {
                document.body.classList.remove('colorblind-enhanced');
            }
        };

        // Ctrl+Alt+C 快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.altKey && e.key === 'c') {
                toggleColorBlindMode();
            }
        });
    }

    // 文化适配
    setupCulturalAdaptation() {
        // 检测用户地区并应用文化安全配色
        const userLang = navigator.language || navigator.userLanguage;
        
        if (userLang.startsWith('zh') || userLang.startsWith('ja') || userLang.startsWith('ko')) {
            // 东亚市场 - 避免大面积黑白配
            document.body.classList.add('cultural-safe');
        }
    }

    // 季度色彩偏好调研
    setupSeasonalFeedback() {
        const lastFeedback = localStorage.getItem('lastThemeFeedback');
        const now = Date.now();
        const threeMonths = 90 * 24 * 60 * 60 * 1000;

        if (!lastFeedback || (now - parseInt(lastFeedback)) > threeMonths) {
            setTimeout(() => {
                this.showFeedbackPrompt();
            }, 30000); // 30秒后显示
        }
    }

    showFeedbackPrompt() {
        const prompt = document.createElement('div');
        prompt.className = 'theme-feedback-prompt';
        prompt.innerHTML = `
            <div>
                <i class="fas fa-palette"></i> 
                您对当前主题配色满意吗？
                <div style="margin-top: 8px;">
                    <button onclick="this.parentElement.parentElement.remove(); advancedThemeManager.recordFeedback('satisfied')" 
                            style="background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 4px; margin-right: 5px;">
                        满意
                    </button>
                    <button onclick="this.parentElement.parentElement.remove(); advancedThemeManager.recordFeedback('unsatisfied')" 
                            style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; margin-right: 5px;">
                        不满意
                    </button>
                    <button onclick="this.parentElement.parentElement.remove()" 
                            style="background: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 4px;">
                        稍后
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(prompt);
    }

    recordFeedback(type) {
        localStorage.setItem('lastThemeFeedback', Date.now().toString());
        localStorage.setItem('themeFeedbackType', type);
        
        // 发送反馈到服务器（如果需要）
        if (typeof fetch !== 'undefined') {
            fetch('/api/theme-feedback', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    feedback: type,
                    theme: document.body.getAttribute('data-theme'),
                    timestamp: Date.now()
                })
            }).catch(() => {}); // 静默失败
        }

        this.showNotification(
            type === 'satisfied' ? '感谢您的反馈！' : '我们会持续改进主题体验',
            'info'
        );
    }

    // 绑定事件
    bindEvents() {
        // 移动端拇指热区优化
        if (window.innerWidth <= 768) {
            this.optimizeForMobile();
        }

        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                this.optimizeForMobile();
            }
        });

        // 监听主题切换事件
        window.addEventListener('themeChanged', (e) => {
            this.onThemeChanged(e.detail.theme, e.detail.isPreview);
        });

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.onPageHidden();
            } else {
                this.onPageVisible();
            }
        });

        // 系统主题偏好变化监听
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addListener((e) => {
                if (this.autoThemeEnabled) {
                    this.handleSystemThemeChange(e.matches);
                }
            });
        }
    }

    optimizeForMobile() {
        // 确保按钮符合44px最小触控标准
        const buttons = document.querySelectorAll('.btn, .nav-link, .dropdown-item');
        buttons.forEach(btn => {
            const rect = btn.getBoundingClientRect();
            if (rect.height < 44) {
                btn.style.minHeight = '44px';
                btn.style.display = 'flex';
                btn.style.alignItems = 'center';
            }
        });
    }

    // 主题切换事件处理
    onThemeChanged(theme, isPreview) {
        if (!isPreview) {
            // 记录主题使用统计
            this.recordThemeUsage(theme);

            // 检查主题兼容性
            this.checkThemeCompatibility(theme);

            // 更新页面性能优化
            this.optimizeForTheme(theme);
        }
    }

    // 页面隐藏时的处理
    onPageHidden() {
        // 暂停不必要的动画和定时器
        if (this.usageTimer) {
            clearInterval(this.usageTimer);
        }
    }

    // 页面可见时的处理
    onPageVisible() {
        // 恢复定时器
        const currentTheme = document.body.getAttribute('data-theme');
        if (currentTheme === 'dark' || currentTheme === 'dark-neon') {
            this.startDarkModeTimer();
        }
    }

    // 系统主题变化处理
    handleSystemThemeChange(isDark) {
        if (this.autoThemeEnabled) {
            const newTheme = isDark ? 'dark-neon' : 'minimal-dawn';
            if (window.themeSwitcher) {
                window.themeSwitcher.switchTheme(newTheme);
                this.showNotification(`已根据系统设置切换到 ${isDark ? '暗夜' : '明亮'} 主题`, 'info');
            }
        }
    }

    // 记录主题使用统计
    recordThemeUsage(theme) {
        const usage = JSON.parse(localStorage.getItem('theme-usage') || '{}');
        usage[theme] = (usage[theme] || 0) + 1;
        localStorage.setItem('theme-usage', JSON.stringify(usage));
    }

    // 检查主题兼容性
    checkThemeCompatibility(theme) {
        // 检查浏览器对某些CSS特性的支持
        const features = {
            'backdrop-filter': CSS.supports('backdrop-filter', 'blur(10px)'),
            'color-mix': CSS.supports('color', 'color-mix(in srgb, red 50%, blue 50%)'),
            'container-queries': CSS.supports('container-type', 'inline-size')
        };

        // 如果不支持某些特性，提供降级方案
        if (!features['backdrop-filter'] && (theme === 'soft-morandi' || theme === 'minimal-dawn')) {
            this.showNotification('您的浏览器不支持某些视觉效果，建议升级浏览器以获得最佳体验', 'warning');
        }
    }

    // 为特定主题优化性能
    optimizeForTheme(theme) {
        // 暗色主题减少动画以节省电量
        if (theme === 'dark-neon' || theme === 'dark') {
            document.documentElement.style.setProperty('--animation-duration', '0.2s');
        } else {
            document.documentElement.style.setProperty('--animation-duration', '0.3s');
        }

        // 高对比度主题禁用某些视觉效果
        if (theme === 'classic-neutral' || theme === 'modern-neutral') {
            document.documentElement.style.setProperty('--shadow-intensity', '0.1');
        } else {
            document.documentElement.style.setProperty('--shadow-intensity', '0.2');
        }
    }

    // 获取主题使用统计
    getThemeUsageStats() {
        const usage = JSON.parse(localStorage.getItem('theme-usage') || '{}');
        const total = Object.values(usage).reduce((sum, count) => sum + count, 0);

        return Object.entries(usage).map(([theme, count]) => ({
            theme,
            count,
            percentage: total > 0 ? Math.round((count / total) * 100) : 0
        })).sort((a, b) => b.count - a.count);
    }

    // 推荐主题
    recommendTheme() {
        const stats = this.getThemeUsageStats();
        const hour = new Date().getHours();

        // 基于使用习惯和时间推荐
        if (stats.length > 0) {
            const favoriteTheme = stats[0].theme;

            // 如果是夜间时间，推荐暗色主题
            if (hour >= 22 || hour <= 6) {
                const darkThemes = ['dark-neon', 'dark', 'deep-sea-tech'];
                const recommendedDark = darkThemes.find(theme =>
                    stats.some(stat => stat.theme === theme)
                ) || 'dark-neon';

                return recommendedDark;
            }

            return favoriteTheme;
        }

        // 默认推荐
        return hour >= 22 || hour <= 6 ? 'dark-neon' : 'primary';
    }

    // 通知显示
    showNotification(message, type = 'info') {
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            // 简单的原生通知
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--theme-primary);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                z-index: 9999;
                animation: slideInUp 0.5s ease-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }
    }

    // 导出主题设置
    exportThemeSettings() {
        const settings = {
            currentTheme: this.currentTheme,
            favoriteThemes: this.favoriteThemes,
            autoThemeEnabled: this.autoThemeEnabled,
            themeUsage: JSON.parse(localStorage.getItem('theme-usage') || '{}'),
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'theme-settings.json';
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('主题设置已导出', 'success');
    }

    // 导入主题设置
    importThemeSettings(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const settings = JSON.parse(e.target.result);

                if (settings.currentTheme) {
                    this.currentTheme = settings.currentTheme;
                    localStorage.setItem('user-theme', settings.currentTheme);
                }

                if (settings.favoriteThemes) {
                    this.favoriteThemes = settings.favoriteThemes;
                    localStorage.setItem('favorite-themes', JSON.stringify(settings.favoriteThemes));
                }

                if (typeof settings.autoThemeEnabled === 'boolean') {
                    this.autoThemeEnabled = settings.autoThemeEnabled;
                    localStorage.setItem('auto-theme', settings.autoThemeEnabled.toString());
                }

                if (settings.themeUsage) {
                    localStorage.setItem('theme-usage', JSON.stringify(settings.themeUsage));
                }

                // 重新初始化
                this.init();
                this.showNotification('主题设置已导入', 'success');

            } catch (error) {
                this.showNotification('导入失败：文件格式错误', 'error');
            }
        };
        reader.readAsText(file);
    }
}

// 初始化高级主题管理器
let advancedThemeManager;

document.addEventListener('DOMContentLoaded', () => {
    advancedThemeManager = new AdvancedThemeManager();
});

// 导出供全局使用
window.AdvancedThemeManager = AdvancedThemeManager;
