#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 5 兼容性最终检查工具
专门检查真正的Bootstrap 5不兼容问题
"""

import os
import re
from pathlib import Path
from datetime import datetime

class Bootstrap5CompatibilityChecker:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.issues = []
        self.files_checked = 0
        
        # 真正的Bootstrap 5不兼容问题
        self.incompatible_patterns = [
            # 已移除的组件
            {
                "pattern": r'<div[^>]*class="[^"]*jumbotron[^"]*"',
                "issue": "Jumbotron组件已在Bootstrap 5中移除",
                "solution": "使用自定义CSS或hero section替代"
            },
            {
                "pattern": r'<div[^>]*class="[^"]*card-deck[^"]*"',
                "issue": "Card deck已在Bootstrap 5中移除",
                "solution": "使用row和col-*类替代"
            },
            {
                "pattern": r'<div[^>]*class="[^"]*card-columns[^"]*"',
                "issue": "Card columns已在Bootstrap 5中移除", 
                "solution": "使用CSS Grid或Masonry替代"
            },
            {
                "pattern": r'<div[^>]*class="[^"]*media[^"]*"',
                "issue": "Media object已在Bootstrap 5中移除",
                "solution": "使用flexbox utilities替代"
            },
            
            # 已移除的类名
            {
                "pattern": r'class="[^"]*\bform-group\b[^"]*"',
                "issue": "form-group类已在Bootstrap 5中移除",
                "solution": "使用mb-3或其他margin utilities替代"
            },
            {
                "pattern": r'class="[^"]*\bform-row\b[^"]*"',
                "issue": "form-row类已在Bootstrap 5中移除",
                "solution": "使用row类替代"
            },
            {
                "pattern": r'class="[^"]*\binput-group-prepend\b[^"]*"',
                "issue": "input-group-prepend已在Bootstrap 5中移除",
                "solution": "直接将内容放在input-group中"
            },
            {
                "pattern": r'class="[^"]*\binput-group-append\b[^"]*"',
                "issue": "input-group-append已在Bootstrap 5中移除",
                "solution": "直接将内容放在input-group中"
            },
            
            # 已更改的属性
            {
                "pattern": r'data-toggle="',
                "issue": "data-toggle属性已更改为data-bs-toggle",
                "solution": "将data-toggle改为data-bs-toggle"
            },
            {
                "pattern": r'data-target="',
                "issue": "data-target属性已更改为data-bs-target",
                "solution": "将data-target改为data-bs-target"
            },
            {
                "pattern": r'data-dismiss="',
                "issue": "data-dismiss属性已更改为data-bs-dismiss",
                "solution": "将data-dismiss改为data-bs-dismiss"
            },
            
            # 关闭按钮结构
            {
                "pattern": r'<button[^>]*class="[^"]*close[^"]*"[^>]*>\s*<span[^>]*>&times;</span>',
                "issue": "关闭按钮结构已在Bootstrap 5中更改",
                "solution": "使用<button class=\"btn-close\" data-bs-dismiss=\"modal\"></button>"
            },
            
            # jQuery依赖
            {
                "pattern": r'\$\([^)]*\)\.modal\(',
                "issue": "Bootstrap 5不再依赖jQuery，但仍支持jQuery语法",
                "solution": "可以继续使用，但建议迁移到原生JavaScript"
            },
            {
                "pattern": r'\$\([^)]*\)\.dropdown\(',
                "issue": "Bootstrap 5不再依赖jQuery，但仍支持jQuery语法",
                "solution": "可以继续使用，但建议迁移到原生JavaScript"
            }
        ]

    def check_file(self, file_path):
        """检查单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            file_issues = []
            
            for check in self.incompatible_patterns:
                matches = re.finditer(check["pattern"], content, re.IGNORECASE)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    file_issues.append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "line": line_num,
                        "match": match.group()[:100] + "..." if len(match.group()) > 100 else match.group(),
                        "issue": check["issue"],
                        "solution": check["solution"]
                    })
            
            if file_issues:
                self.issues.extend(file_issues)
                print(f"⚠️  {file_path.relative_to(self.project_root)} - 发现 {len(file_issues)} 个兼容性问题")
                
            self.files_checked += 1
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")

    def scan_project(self):
        """扫描项目文件"""
        print("🔍 检查Bootstrap 5兼容性问题...")
        
        # 扫描目录
        scan_dirs = [
            self.project_root / "app" / "templates",
            self.project_root / "app" / "static" / "js"
        ]
        
        for scan_dir in scan_dirs:
            if not scan_dir.exists():
                continue
                
            print(f"📁 扫描目录: {scan_dir.relative_to(self.project_root)}")
            
            # 扫描HTML和JS文件
            for pattern in ["*.html", "*.js"]:
                for file_path in scan_dir.rglob(pattern):
                    self.check_file(file_path)

    def generate_report(self):
        """生成检查报告"""
        print(f"\n📊 兼容性检查完成:")
        print(f"   扫描文件: {self.files_checked}")
        print(f"   发现问题: {len(self.issues)}")
        
        if self.issues:
            print(f"\n⚠️  发现的兼容性问题:")
            
            # 按问题类型分组
            issues_by_type = {}
            for issue in self.issues:
                issue_type = issue["issue"]
                if issue_type not in issues_by_type:
                    issues_by_type[issue_type] = []
                issues_by_type[issue_type].append(issue)
            
            for issue_type, issues in issues_by_type.items():
                print(f"\n🔸 {issue_type} ({len(issues)} 处):")
                for issue in issues[:3]:  # 只显示前3个
                    print(f"   📄 {issue['file']}:{issue['line']}")
                    print(f"   💡 解决方案: {issue['solution']}")
                if len(issues) > 3:
                    print(f"   ... 还有 {len(issues) - 3} 处类似问题")
            
            # 生成详细报告文件
            report_file = self.project_root / f"bootstrap5_compatibility_final_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("Bootstrap 5 兼容性最终检查报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"检查时间: {datetime.now().isoformat()}\n")
                f.write(f"扫描文件: {self.files_checked}\n")
                f.write(f"发现问题: {len(self.issues)}\n\n")
                
                for issue_type, issues in issues_by_type.items():
                    f.write(f"{issue_type}:\n")
                    f.write("-" * 40 + "\n")
                    for issue in issues:
                        f.write(f"文件: {issue['file']}\n")
                        f.write(f"行号: {issue['line']}\n")
                        f.write(f"匹配: {issue['match']}\n")
                        f.write(f"解决方案: {issue['solution']}\n")
                        f.write("\n")
                    f.write("\n")
            
            print(f"\n📄 详细报告已生成: {report_file}")
        else:
            print(f"\n✅ 恭喜！没有发现Bootstrap 5兼容性问题")
            print(f"🎉 您的项目已完全兼容Bootstrap 5.3.6！")

    def run(self):
        """运行检查工具"""
        print("🚀 Bootstrap 5 兼容性最终检查工具启动")
        print("=" * 50)
        print("专门检查真正的Bootstrap 5不兼容问题")
        print("排除误报和正常的CSS/JS语法")
        print()
        
        self.scan_project()
        self.generate_report()

if __name__ == "__main__":
    checker = Bootstrap5CompatibilityChecker()
    checker.run()
