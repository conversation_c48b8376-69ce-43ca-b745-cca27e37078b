{% extends 'base.html' %}
{% from 'components/page_header.html' import filter_page_header %}
{% from 'components/cards.html' import table_card, data_card, compact_card_grid %}

{% block title %}仓库管理 - {{ super() }}{% endblock %}

{% block content %}
<!-- 页面头部和筛选 -->
{% set filter_options = [
  {
    'name': 'area_id',
    'label': '选择区域',
    'options': [{'value': area.id, 'text': area.name} for area in areas]
  },
  {
    'name': 'status',
    'label': '选择状态',
    'options': [
      {'value': '正常', 'text': '正常'},
      {'value': '维护中', 'text': '维护中'},
      {'value': '已关闭', 'text': '已关闭'}
    ]
  }
] %}

{% set actions = [
  {'url': url_for('warehouse.create'), 'text': '创建仓库', 'icon': 'fas fa-plus', 'class': 'btn-primary'}
] %}

<form method="get" action="{{ url_for('warehouse.index') }}">
{{ filter_page_header(
  title="仓库管理",
  search_form=True,
  filter_options=filter_options,
  actions=actions
) }}
</form>

<!-- 统计卡片 -->
{% if warehouse_stats %}
{% set stats_cards = [
  {
    'type': 'data',
    'title': '总仓库数',
    'value': warehouse_stats.total,
    'icon': 'fas fa-warehouse',
    'color': 'primary'
  },
  {
    'type': 'data',
    'title': '正常运行',
    'value': warehouse_stats.normal,
    'icon': 'fas fa-check-circle',
    'color': 'success'
  },
  {
    'type': 'data',
    'title': '维护中',
    'value': warehouse_stats.maintenance,
    'icon': 'fas fa-tools',
    'color': 'warning'
  },
  {
    'type': 'data',
    'title': '已关闭',
    'value': warehouse_stats.closed,
    'icon': 'fas fa-times-circle',
    'color': 'danger'
  }
] %}

<div class="mb-4">
  {{ compact_card_grid(stats_cards, "lg-3 md-6") }}
</div>
{% endif %}

<!-- 仓库列表表格 -->
{% set headers = [
  {'text': '仓库名称', 'class': ''},
  {'text': '所属区域', 'class': ''},
  {'text': '位置', 'class': ''},
  {'text': '管理员', 'class': ''},
  {'text': '容量', 'class': 'text-center'},
  {'text': '状态', 'class': 'text-center'},
  {'text': '操作', 'class': 'text-center'}
] %}

{% set rows = [] %}
{% for warehouse in warehouses %}
  {% set status_color = 'success' if warehouse.status == '正常' else 'warning' if warehouse.status == '维护中' else 'danger' %}
  {% set row = [
    {'text': warehouse.name, 'type': 'text'},
    {'text': warehouse.area.name, 'type': 'text'},
    {'text': warehouse.location or '未设置', 'type': 'text'},
    {'text': warehouse.manager.real_name or warehouse.manager.username if warehouse.manager else '未分配', 'type': 'text'},
    {'text': (warehouse.capacity|string + ' ' + warehouse.capacity_unit) if warehouse.capacity else '未设置', 'type': 'text', 'class': 'text-center'},
    {'text': warehouse.status, 'type': 'badge', 'color': status_color, 'class': 'text-center'},
    {
      'type': 'actions',
      'actions': [
        {'url': url_for('warehouse.view', id=warehouse.id), 'icon': 'fas fa-eye', 'title': '查看详情'},
        {'url': url_for('warehouse.edit', id=warehouse.id), 'icon': 'fas fa-edit', 'title': '编辑仓库'}
      ],
      'class': 'text-center'
    }
  ] %}
  {% set rows = rows + [row] %}
{% endfor %}

{{ table_card(
  title="仓库列表 (" + warehouses|length|string + "个)",
  headers=headers,
  rows=rows,
  pagination=pagination_html() if pagination and pagination.pages > 1 else None
) }}

{% macro pagination_html() %}
{% if pagination and pagination.pages > 1 %}
<nav aria-label="仓库列表分页" class="d-flex justify-content-center">
  <ul class="pagination pagination-sm mb-0">
    <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
      <a class="page-link" href="{{ url_for('warehouse.index', page=pagination.prev_num, area_id=request.args.get('area_id'), status=request.args.get('status'), search=request.args.get('search')) if pagination.has_prev else '#' }}">
        <i class="fas fa-chevron-left"></i>
      </a>
    </li>

    {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
      {% if page_num %}
        {% if page_num == pagination.page %}
        <li class="page-item active">
          <a class="page-link" href="#">{{ page_num }}</a>
        </li>
        {% else %}
        <li class="page-item">
          <a class="page-link" href="{{ url_for('warehouse.index', page=page_num, area_id=request.args.get('area_id'), status=request.args.get('status'), search=request.args.get('search')) }}">{{ page_num }}</a>
        </li>
        {% endif %}
      {% else %}
      <li class="page-item disabled">
        <a class="page-link" href="#">...</a>
      </li>
      {% endif %}
    {% endfor %}

    <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
      <a class="page-link" href="{{ url_for('warehouse.index', page=pagination.next_num, area_id=request.args.get('area_id'), status=request.args.get('status'), search=request.args.get('search')) if pagination.has_next else '#' }}">
        <i class="fas fa-chevron-right"></i>
      </a>
    </li>
  </ul>
</nav>
{% endif %}
{% endmacro %}

<!-- 移动端卡片视图 -->
<div class="d-lg-none mt-3">
  <div class="row g-2">
    {% for warehouse in warehouses %}
    <div class="col-12">
      <div class="card card-sm">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <h6 class="card-title mb-0">{{ warehouse.name }}</h6>
            {% set status_color = 'success' if warehouse.status == '正常' else 'warning' if warehouse.status == '维护中' else 'danger' %}
            <span class="badge bg-{{ status_color }}">{{ warehouse.status }}</span>
          </div>

          <div class="row g-2 text-sm">
            <div class="col-6">
              <small class="text-muted">区域:</small><br>
              <span>{{ warehouse.area.name }}</span>
            </div>
            <div class="col-6">
              <small class="text-muted">位置:</small><br>
              <span>{{ warehouse.location or '未设置' }}</span>
            </div>
            <div class="col-6">
              <small class="text-muted">管理员:</small><br>
              <span>{{ warehouse.manager.real_name or warehouse.manager.username if warehouse.manager else '未分配' }}</span>
            </div>
            <div class="col-6">
              <small class="text-muted">容量:</small><br>
              <span>{{ (warehouse.capacity|string + ' ' + warehouse.capacity_unit) if warehouse.capacity else '未设置' }}</span>
            </div>
          </div>

          <div class="mt-3 d-flex gap-2">
            <a href="{{ url_for('warehouse.view', id=warehouse.id) }}" class="btn btn-outline-primary btn-sm flex-fill">
              <i class="fas fa-eye me-1"></i>查看
            </a>
            <a href="{{ url_for('warehouse.edit', id=warehouse.id) }}" class="btn btn-outline-secondary btn-sm flex-fill">
              <i class="fas fa-edit me-1"></i>编辑
            </a>
          </div>
        </div>
      </div>
    </div>
    {% else %}
    <div class="col-12">
      <div class="card border-0 bg-light">
        <div class="card-body text-center py-4">
          <i class="fas fa-warehouse fs-1 text-muted opacity-50 mb-3"></i>
          <p class="text-muted mb-0">暂无仓库数据</p>
          <a href="{{ url_for('warehouse.create') }}" class="btn btn-primary btn-sm mt-2">
            <i class="fas fa-plus me-1"></i>创建第一个仓库
          </a>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  <!-- 移动端分页 -->
  {% if pagination and pagination.pages > 1 %}
  <div class="mt-3">
    {{ pagination_html() }}
  </div>
  {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    // 表格排序功能
    const table = document.querySelector('.table');
    if (table) {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            if (index < headers.length - 1) { // 排除操作列
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    // 这里可以添加排序逻辑
                    console.log('排序列:', index);
                });
            }
        });
    }

    // 搜索框自动提交
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
    }
});
</script>
{% endblock %}
