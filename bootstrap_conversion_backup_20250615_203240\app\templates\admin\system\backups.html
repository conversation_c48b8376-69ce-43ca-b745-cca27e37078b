{% extends 'base.html' %}

{% block title %}数据库备份 - {{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .backup-card {
        margin-bottom: 20px;
    }
    .backup-card .card-header {
        font-weight: bold;
    }
    .backup-table th {
        background-color: #f8f9fa;
    }
    .backup-size {
        font-family: monospace;
    }
    .backup-status-success {
        color: #28a745;
    }
    .backup-status-failed {
        color: #dc3545;
    }
    .backup-missing {
        color: #dc3545;
        font-style: italic;
    }
</style>

{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>数据库备份</h2>
        <p class="text-muted">管理系统数据库的备份和恢复</p>
    </div>
    <div class="col-md-4 text-end">
        <form method="post" action="{{ url_for('system.create_backup') }}" class="d-inline" novalidate novalidate>
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus"></i> 创建备份
            </button>
        </form>
        <a href="{{ url_for('system.settings') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回设置
        </a>
    </div>
</div>

<div class="card backup-card">
    <div class="card-header bg-primary text-white">
        备份列表
    </div>
    <div class="card-body">
        {% if backups %}
        <div class="table-responsive">
            <table class="table table-hover backup-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>文件名</th>
                        <th>类型</th>
                        <th>大小</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for backup in backups %}
                    <tr>
                        <td>{{ backup.id }}</td>
                        <td>
                            {{ backup.filename }}
                            {% if not backup.file_exists %}
                            <span class="backup-missing">(文件不存在)</span>
                            {% endif %}
                        </td>
                        <td>{{ backup.backup_type }}</td>
                        <td class="backup-size">
                            {% if backup.size %}
                            {% if backup.size > 1024 * 1024 * 1024 %}
                            {{ (backup.size / (1024 * 1024 * 1024))|round(2) }} GB
                            {% elif backup.size > 1024 * 1024 %}
                            {{ (backup.size / (1024 * 1024))|round(2) }} MB
                            {% elif backup.size > 1024 %}
                            {{ (backup.size / 1024)|round(2) }} KB
                            {% else %}
                            {{ backup.size }} B
                            {% endif %}
                            {% else %}
                            未知
                            {% endif %}
                        </td>
                        <td>
                            {% if backup.status == '成功' %}
                            <span class="backup-status-success">
                                <i class="fas fa-check-circle"></i> {{ backup.status }}
                            </span>
                            {% else %}
                            <span class="backup-status-failed">
                                <i class="fas fa-times-circle"></i> {{ backup.status }}
                            </span>
                            {% endif %}
                        </td>
                        <td>{{ backup.created_at }}</td>
                        <td>
                            {% if backup.file_exists %}
                            <a href="{{ url_for('system.download_backup', id=backup.id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-download"></i> 下载
                            </a>
                            {% endif %}
                            <form method="post" action="{{ url_for('system.delete_backup', id=backup.id) }}" class="d-inline" novalidate novalidate>
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm btn-danger"
                                        data-action="critical-confirm" data-original-data-onclick="return confirm('确定要删除这个备份吗？')" style="cursor: pointer;">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 暂无备份记录
        </div>
        {% endif %}
    </div>
</div>

<div class="card backup-card mt-4">
    <div class="card-header bg-info text-white">
        备份说明
    </div>
    <div class="card-body">
        <h5>备份类型</h5>
        <ul>
            <li><strong>完整备份</strong>：包含数据库的所有数据</li>
            <li><strong>差异备份</strong>：包含自上次完整备份以来的所有变更</li>
            <li><strong>日志备份</strong>：包含事务日志</li>
        </ul>

        <h5>备份建议</h5>
        <ul>
            <li>建议每天进行一次完整备份</li>
            <li>在进行重要数据修改前进行备份</li>
            <li>定期检查备份文件的完整性</li>
            <li>将备份文件保存在不同的物理位置</li>
        </ul>

        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>注意</strong>：备份文件包含敏感数据，请确保其安全性。
        </div>
    </div>
</div>

{% endblock %}
