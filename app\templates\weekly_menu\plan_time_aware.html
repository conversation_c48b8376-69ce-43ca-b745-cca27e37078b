{% extends 'base.html' %}

{% block title %}周菜单计划 - {{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/weekly_menu_modal.css') }}">
<style nonce="{{ csp_nonce }}">
  /* 加载状态样式 */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 保存状态提示 */
  .save-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    border-radius: 4px;
    display: none;
    z-index: 9998;
  }

  .save-status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .save-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  /* 页面特定样式 */
  .table th, .table td {
    vertical-align: middle;
    text-align: center;
  }

  .menu-input {
    cursor: pointer;
    background-color: #fff;
    transition: all 0.3s;
  }

  .menu-input:hover {
    background-color: #f8f9fa;
  }

  .menu-input.selected {
    background-color: #e8f4e8;
    border-color: #28a745;
  }

  .menu-input.readonly {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }

  /* 周选择器样式 */
  .week-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .week-selector .week-item {
    padding: 10px 20px;
    margin: 0 5px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .week-selector .week-item:hover {
    background-color: #f8f9fa;
  }

  .week-selector .week-item.active {
    background-color: #e8f4e8;
    border-color: #28a745;
    color: #28a745;
    font-weight: bold;
  }

  .week-selector .week-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f8f9fa;
  }

  /* 状态标签 */
  .status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.875rem;
    margin-left: 10px;
  }

  .status-badge.editable {
    background-color: #e8f4e8;
    color: #28a745;
    border: 1px solid #28a745;
  }

  .status-badge.readonly {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .status-badge.published {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #b8daff;
  }

  /* 菜品卡片样式 */
  .recipe-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    background-color: #fff;
  }

  .recipe-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #adb5bd;
  }

  .recipe-card.selected {
    background-color: #e8f4e8;
    border-color: #28a745;
  }

  /* 已选菜品标签样式 */
  .selected-recipe-tag {
    display: inline-flex;
    align-items: center;
    margin: 0.25rem;
    padding: 0.35rem 0.75rem;
    background-color: #e8f4e8;
    border: 1px solid #28a745;
    border-radius: 20px;
    font-size: 0.875rem;
  }

  .selected-recipe-tag .remove-btn {
    margin-left: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
    line-height: 1;
    color: #dc3545;
  }

  .selected-recipe-tag .remove-btn:hover {
    color: #c82333;
  }
</style>

{% endblock %}

{% block content %}
<div class=\"container-fluid'>
  <!-- 加载状态遮罩 -->
  <div class='loading-overlay\">
    <div class=\"loading-spinner'></div>
  </div>

  <!-- 保存状态提示 -->
  <div class='save-status\"></div>

  <div class=\"row mb-4'>
    <div class='col-md-8\">
      <h2>
        {{ area.name }}周菜单计划
        {% if is_editable %}
          <span class=\"status-badge editable'>可编辑</span>
        {% elif existing_menu and existing_menu.status == '已发布' %}
          <span class='status-badge published\">已发布</span>
        {% else %}
          <span class=\"status-badge readonly'>只读</span>
        {% endif %}
      </h2>
    </div>
    <div class='col-md-4 text-end\">
      <a href="{{ url_for('weekly_menu_v2.index') }}" class=\"btn btn-secondary'>
        <i class='fas fa-arrow-left\"></i> 返回列表
      </a>
      {% if is_editable and existing_menu and existing_menu.status != '已发布' %}
        <button type="button" class=\"btn btn-success' id='saveMenuBtn\">
          <i class=\"fas fa-save'></i> 保存菜单
        </button>
      {% endif %}
    </div>
  </div>

  <!-- 周选择器 -->
  <div class='week-selector\">
    {% for week_info in available_weeks %}
      <div class=\"week-item {% if week_info.start_date == week_start %}active{% endif %}{% if not week_info.is_editable and week_info.is_viewable % %} disabled{% endif %}'
           data-week='{{ week_info.start_date }}\"
           data-editable="{{ week_info.is_editable|lower }}"
           data-status="{{ week_info.status }}">
        {{ week_info.display_text }}
      </div>
    {% endfor %}
  </div>

  <div class=\"row'>
    <div class='col-md-12\">
      <div class=\"card'>
        <div class='card-body\">
          <form id="menuForm" method="post" novalidate novalidate>
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="menu_data" id="menuData" value="">
            <table class=\"table table-bordered'>
              <thead>
                <tr>
                  <th class='w-15\">日期</th>
                  <th class=\"w-30'>早餐</th>
                  <th class='w-30\">午餐</th>
                  <th class=\"w-30'>晚餐</th>
                </tr>
              </thead>
              <tbody>
                {% for date_str, day_data in week_dates.items() %}
                <tr>
                  <td class='date-column\">
                    <div class=\"fw-bold'>{{ day_data.weekday }}</div>
                    <div>{{ date_str }}</div>
                  </td>
                  {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                  <td>
                    <div class='mb-3 mb-0\">
                      <textarea class=\"form-control menu-input {% if not is_editable %}readonly{% endif %}'
                               data-date='{{ date_str }}\"
                               data-meal="{{ meal_type }}"
                               {% if not is_editable %}readonly{% endif %}>{{ menu_data.get(date_str, {}).get(meal_type, []) | map(attribute='name') | join(', ') }}</textarea>
                    </div>
                  </td>
                  {% endfor %}
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 菜单编辑模态框 -->
<div class=\"modal fade' id='menuModal\" tabindex="-1" role="dialog" aria-labelledby="modalTitle" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="true">
  <div class=\"modal-dialog modal-lg' role='document\">
    <div class=\"modal-content'>
      <div class='modal-header\">
        <h5 class=\"modal-title' id='modalTitle\"></h5>
        <button type="button" class=\"btn-close' data-bs-dismiss='modal\" aria-label="Close"></button>
      </div>
      <div class=\"modal-body'>
        <!-- 已选菜品 -->
        <div class='mb-4\">
          <h6>已选菜品：</h6>
          <div id="selectedDishes" class=\"d-flex flex-wrap'></div>
        </div>

        <!-- 自定义菜品输入 -->
        <div class='mb-4\">
          <div class=\"input-group'>
            <input type='text\" class=\"form-control' id='customDishInput\" placeholder="输入自定义菜品名称">
            <div >
              <button class=\"btn btn-outline-secondary' type='button\" id="addCustomDishBtn">添加</button>
            </div>
          </div>
        </div>

        <!-- 分类导航 -->
        <ul class=\"nav nav-tabs mb-3' id='recipeCategories\" role="tablist">
          <li class=\"nav-item'>
            <a class='nav-link active\" id="all-tab" data-bs-toggle="tab" href="#all" role="tab"
               data-category="all" aria-selected="true">全部</a>
          </li>
          {% for category in recipes_by_category.keys() %}
          <li class=\"nav-item'>
            <a class='nav-link\" id="{{ category }}-tab" data-bs-toggle="tab" href="#{{ category }}"
               data-category="{{ category }}" role="tab" aria-selected="false">{{ category }}</a>
          </li>
          {% endfor %}
        </ul>

        <!-- 系统菜品列表 -->
        <div class=\"tab-content recipe-list' id='recipeCategoriesContent\">
          <!-- 全部分类 -->
          <div class=\"tab-pane fade show active' id='all\" role="tabpanel">
            <div class=\"row'>
              {% for category, recipes in recipes_by_category.items() %}
                {% for recipe in recipes %}
                <div class='col-md-4 col-sm-6 mb-3\">
                  <div class=\"recipe-card card h-100'
                       data-category='{{ category }}\"
                       data-id="{{ recipe.id }}"
                       data-name="{{ recipe.name }}"
                       data-onclick="selectDishFromCard(this)">
                    {% if recipe.image_path %}
                    <img src="{{ url_for('static', filename=recipe.image_path.replace('\\', '/')) }}"
                         class=\"card-img-top recipe-img'
                         alt='{{ recipe.name }}\">
                    {% endif %}
                    <div class=\"card-body p-2 text-center'>
                      {{ recipe.name }}
                    </div>
                  </div>
                </div>
                {% endfor %}
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
      <div class='modal-footer\">
        <button type="button" class=\"btn btn-secondary' data-bs-dismiss='modal\" id="cancelSelectionBtn">取消</button>
        <button type="button" class=\"btn btn-primary' id='saveSelectionBtn\">确定</button>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 全局变量
let menuModal;
const selectedDishes = new Map();
let isEditable = {{ is_editable|tojson }};
let menuData = {{ menu_data|tojson }};

// 确保在文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 初始化模态框
  const modalElement = document.getElementById('menuModal');
  if (!modalElement) {
    console.error('未找到模态框元素');
    return;
  }

  // 初始化 Bootstrap 模态框
  menuModal = new bootstrap.Modal(modalElement, {
    backdrop: 'static',
    keyboard: true
  });

  // 初始化事件监听器
  initializeEventListeners();
});

// 初始化事件监听器
function initializeEventListeners() {
  // 菜单输入框点击事件
  document.querySelectorAll('.menu-input').forEach(input => {
    input.addEventListener('click', function() {
      if (!this.classList.contains('readonly') && isEditable) {
        // 标记当前选中的输入框
        document.querySelectorAll('.menu-input').forEach(i => i.classList.remove('selected'));
        this.classList.add('selected');
        showModal(this);
      }
    });
  });

  // 自定义菜品输入框回车事件
  const customDishInput = document.getElementById('customDishInput');
  if (customDishInput) {
    customDishInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        addCustomDish();
      }
    });
  }

  // 添加自定义菜品按钮点击事件
  const addCustomDishBtn = document.getElementById('addCustomDishBtn');
  if (addCustomDishBtn) {
    addCustomDishBtn.addEventListener('click', addCustomDish);
  }

  // 保存按钮点击事件
  const saveSelectionBtn = document.getElementById('saveSelectionBtn');
  if (saveSelectionBtn) {
    saveSelectionBtn.addEventListener('click', saveSelection);
  }

  // 取消按钮点击事件
  const cancelSelectionBtn = document.getElementById('cancelSelectionBtn');
  if (cancelSelectionBtn) {
    cancelSelectionBtn.addEventListener('click', function() {
      menuModal.hide();
    });
  }

  // 保存菜单按钮点击事件
  const saveMenuBtn = document.getElementById('saveMenuBtn');
  if (saveMenuBtn) {
    saveMenuBtn.addEventListener('click', function() {
      saveMenu();
    });
  }

  // 周选择器点击事件
  document.querySelectorAll('.week-item').forEach(item => {
    item.addEventListener('click', function() {
      if (!this.classList.contains('disabled')) {
        const weekStart = this.dataset.week;
        const isEditable = this.dataset.editable === 'true';

        // 如果当前有未保存的更改，提示用户
        if (hasUnsavedChanges()) {
          if (confirm('当前菜单有未保存的更改，切换周将丢失这些更改。是否继续？')) {
            navigateToWeek(weekStart);
          }
        } else {
          navigateToWeek(weekStart);
        }
      }
    });
  });
}

// 显示模态框
function showModal(input) {
  if (!menuModal) {
    console.error('模态框未初始化');
    return;
  }

  const date = input.dataset.date;
  const meal = input.dataset.meal;

  // 更新模态框标题
  const titleElement = document.getElementById('modalTitle');
  if (titleElement) {
    titleElement.textContent = `${date} ${meal}`;
  }

  // 清空已选菜品
  selectedDishes.clear();

  // 加载当前已选菜品
  if (menuData[date] && menuData[date][meal]) {
    menuData[date][meal].forEach(dish => {
      selectedDishes.set(dish.id, dish);
    });
  }

  // 更新已选菜品显示
  updateSelectedDishesList();

  // 显示模态框
  try {
    menuModal.show();

    // 聚焦到自定义菜品输入框
    setTimeout(() => {
      const customDishInput = document.getElementById('customDishInput');
      if (customDishInput) {
        customDishInput.focus();
      }
    }, 300);
  } catch (error) {
    console.error('显示模态框失败:', error);
  }
}

// 更新已选菜品列表
function updateSelectedDishesList() {
  const container = document.getElementById('selectedDishes');
  if (!container) return;

  // 清空容器
  while (container.firstChild) {
    container.removeChild(container.firstChild);
  }

  // 添加每个已选菜品
  selectedDishes.forEach(dish => {
    const tag = document.createElement('div');
    tag.className = 'selected-recipe-tag';

    // 添加菜品名称
    const nameText = document.createTextNode(dish.name);
    tag.appendChild(nameText);

    // 添加删除按钮
    const removeBtn = document.createElement('span');
    removeBtn.className = 'remove-btn';
    removeBtn.textContent = '×'; // 使用文本内容而不是HTML实体

    // 使用闭包保存dish.id
    const dishId = dish.id;
    removeBtn.onclick = function() {
      removeDish(dishId);
    };

    tag.appendChild(removeBtn);
    container.appendChild(tag);
  });
}

// 从卡片元素中选择菜品
function selectDishFromCard(cardElement) {
  const id = cardElement.dataset.id;
  const name = cardElement.dataset.name;

  selectDish(id, name, cardElement);
}

// 选择菜品
function selectDish(id, name, card = null) {
  if (!card) {
    // 使用更安全的方式查找卡片
    const cards = document.querySelectorAll('.recipe-card');
    for (const c of cards) {
      if (c.dataset.id === id) {
        card = c;
        break;
      }
    }
  }

  if (selectedDishes.has(id)) {
    removeDish(id);
    card?.classList.remove('selected');
  } else {
    selectedDishes.set(id, { id, name });
    card?.classList.add('selected');
    updateSelectedDishesList();
  }
}

// 移除菜品
function removeDish(id) {
  selectedDishes.delete(id);
  updateSelectedDishesList();
}

// 添加自定义菜品
function addCustomDish() {
  const input = document.getElementById('customDishInput');
  const dishName = input.value.trim();

  if (!dishName) return;

  const customId = `custom_${Date.now()}`;
  selectedDishes.set(customId, {
    id: customId,
    name: dishName
  });

  updateSelectedDishesList();
  input.value = '';
  input.focus();
}

// 保存选择
function saveSelection() {
  const currentInput = document.querySelector('.menu-input.selected');
  if (!currentInput) return;

  const date = currentInput.dataset.date;
  const meal = currentInput.dataset.meal;

  // 确保数据结构存在
  if (!menuData[date]) menuData[date] = {};

  // 保存选择的菜品
  menuData[date][meal] = Array.from(selectedDishes.values());

  // 更新输入框显示
  currentInput.value = Array.from(selectedDishes.values())
    .map(d => d.name)
    .join(', ');

  // 关闭模态框
  menuModal.hide();
}

// 保存菜单
function saveMenu() {
  // 显示加载状态
  document.querySelector('.loading-overlay').style.display = 'flex';

  // 准备表单数据
  document.getElementById('menuData').value = JSON.stringify(menuData);

  // 提交表单
  document.getElementById('menuForm').submit();
}

// 检查是否有未保存的更改
function hasUnsavedChanges() {
  // 这里可以实现更复杂的逻辑来检测未保存的更改
  // 简单实现：如果用户修改了任何输入框，则认为有未保存的更改
  return document.querySelector('.menu-input.selected') !== null;
}

// 导航到指定周
function navigateToWeek(weekStart) {
  // 使用 URLSearchParams 构建查询参数，更安全
  const url = new URL('{{ url_for('weekly_menu.plan_time_aware', _external=True) }}');
  url.searchParams.set('area_id', '{{ area_id }}');
  url.searchParams.set('week_start', weekStart);
  window.location.href = url.toString();
}
</script>

{% endblock %}