#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jinja2模板语法检查工具
检查和修复常见的Jinja2语法错误
"""

import os
import re
from pathlib import Path
from datetime import datetime

class Jinja2SyntaxChecker:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.issues = []
        self.fixes_applied = 0
        self.files_checked = 0
        
        # 常见的Jinja2语法错误模式
        self.syntax_patterns = [
            {
                "name": "嵌套if语句错误",
                "pattern": r'{% if[^}]*%}{% if[^}]*%}',
                "description": "检测到可能的嵌套if语句语法错误"
            },
            {
                "name": "不完整的if语句",
                "pattern": r'{% if[^}]*not in[^}]*%}[^{]*{% endif %}{% if',
                "description": "检测到不完整的if语句结构"
            },
            {
                "name": "错误的变量赋值",
                "pattern": r'{% if[^}]*set _[^}]*%}',
                "description": "检测到在if语句中的错误变量赋值"
            },
            {
                "name": "缺失的endif",
                "pattern": r'{% if[^}]*%}(?!.*{% endif %})',
                "description": "检测到可能缺失的endif标签"
            },
            {
                "name": "错误的操作符使用",
                "pattern": r'{% if[^}]*not in[^}]*%}[^{]*{% endif %}[^{]*{% if[^}]*{% if',
                "description": "检测到错误的操作符使用"
            },
            {
                "name": "语句块未正确关闭",
                "pattern": r'{% if[^}]*%}[^{]*{% if[^}]*set[^}]*%}',
                "description": "检测到语句块未正确关闭"
            }
        ]

    def check_file(self, file_path):
        """检查单个文件的语法"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            file_issues = []
            
            # 检查各种语法错误模式
            for pattern_info in self.syntax_patterns:
                matches = re.finditer(pattern_info["pattern"], content, re.MULTILINE | re.DOTALL)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    file_issues.append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "line": line_num,
                        "type": pattern_info["name"],
                        "description": pattern_info["description"],
                        "match": match.group()[:100] + "..." if len(match.group()) > 100 else match.group()
                    })
            
            # 检查特定的问题模式
            self.check_specific_issues(file_path, content, file_issues)
            
            if file_issues:
                self.issues.extend(file_issues)
                print(f"⚠️  {file_path.relative_to(self.project_root)} - 发现 {len(file_issues)} 个语法问题")
                
            self.files_checked += 1
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")

    def check_specific_issues(self, file_path, content, file_issues):
        """检查特定的语法问题"""
        
        # 检查类似 "{% if not in %}" 的错误
        pattern1 = r'{% if[^}]*not in[^}]*%}'
        matches = re.finditer(pattern1, content)
        for match in matches:
            line_num = content[:match.start()].count('\n') + 1
            file_issues.append({
                "file": str(file_path.relative_to(self.project_root)),
                "line": line_num,
                "type": "错误的not in语法",
                "description": "检测到错误的'not in'语法使用",
                "match": match.group()
            })
        
        # 检查未配对的语句块
        if_count = len(re.findall(r'{% if[^}]*%}', content))
        endif_count = len(re.findall(r'{% endif %}', content))
        
        if if_count != endif_count:
            file_issues.append({
                "file": str(file_path.relative_to(self.project_root)),
                "line": 1,
                "type": "未配对的if/endif",
                "description": f"if语句数量({if_count})与endif数量({endif_count})不匹配",
                "match": f"if: {if_count}, endif: {endif_count}"
            })
        
        # 检查for循环配对
        for_count = len(re.findall(r'{% for[^}]*%}', content))
        endfor_count = len(re.findall(r'{% endfor %}', content))
        
        if for_count != endfor_count:
            file_issues.append({
                "file": str(file_path.relative_to(self.project_root)),
                "line": 1,
                "type": "未配对的for/endfor",
                "description": f"for循环数量({for_count})与endfor数量({endfor_count})不匹配",
                "match": f"for: {for_count}, endfor: {endfor_count}"
            })

    def scan_templates(self):
        """扫描所有模板文件"""
        template_dir = self.project_root / "app" / "templates"
        
        if not template_dir.exists():
            print("❌ 模板目录不存在")
            return
            
        print("🔍 检查Jinja2模板语法...")
        
        for file_path in template_dir.rglob("*.html"):
            self.check_file(file_path)

    def generate_report(self):
        """生成检查报告"""
        print(f"\n📊 语法检查完成:")
        print(f"   扫描文件: {self.files_checked}")
        print(f"   发现问题: {len(self.issues)}")
        
        if self.issues:
            print(f"\n⚠️  发现的语法问题:")
            
            # 按问题类型分组
            issues_by_type = {}
            for issue in self.issues:
                issue_type = issue["type"]
                if issue_type not in issues_by_type:
                    issues_by_type[issue_type] = []
                issues_by_type[issue_type].append(issue)
            
            for issue_type, issues in issues_by_type.items():
                print(f"\n🔸 {issue_type} ({len(issues)} 处):")
                for issue in issues[:5]:  # 只显示前5个
                    print(f"   📄 {issue['file']}:{issue['line']}")
                    print(f"   📝 {issue['description']}")
                    print(f"   🔍 匹配: {issue['match'][:80]}...")
                if len(issues) > 5:
                    print(f"   ... 还有 {len(issues) - 5} 处类似问题")
            
            # 生成详细报告文件
            report_file = self.project_root / f"jinja2_syntax_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("Jinja2模板语法检查报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"检查时间: {datetime.now().isoformat()}\n")
                f.write(f"扫描文件: {self.files_checked}\n")
                f.write(f"发现问题: {len(self.issues)}\n\n")
                
                for issue_type, issues in issues_by_type.items():
                    f.write(f"{issue_type}:\n")
                    f.write("-" * 40 + "\n")
                    for issue in issues:
                        f.write(f"文件: {issue['file']}\n")
                        f.write(f"行号: {issue['line']}\n")
                        f.write(f"描述: {issue['description']}\n")
                        f.write(f"匹配: {issue['match']}\n")
                        f.write("\n")
                    f.write("\n")
            
            print(f"\n📄 详细报告已生成: {report_file}")
        else:
            print(f"\n✅ 恭喜！没有发现Jinja2语法错误")
            print(f"🎉 所有模板文件语法正确！")

    def run(self):
        """运行检查工具"""
        print("🚀 Jinja2模板语法检查工具启动")
        print("=" * 50)
        
        self.scan_templates()
        self.generate_report()

if __name__ == "__main__":
    checker = Jinja2SyntaxChecker()
    checker.run()
