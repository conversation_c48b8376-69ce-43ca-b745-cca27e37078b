{% extends 'base.html' %}

{% block title %}添加问题记录{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">添加问题记录 - {{ log.log_date.strftime('%Y-%m-%d') }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">问题信息</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" novalidate novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="issue_type">问题类型 <span class="text-danger">*</span></label>
                            <select class="form-control" id="issue_type" name="issue_type" required>
                                <option value="">请选择问题类型</option>
                                <option value="设备故障">设备故障</option>
                                <option value="卫生问题">卫生问题</option>
                                <option value="食材问题">食材问题</option>
                                <option value="人员问题">人员问题</option>
                                <option value="安全隐患">安全隐患</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="found_date">发现日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="found_date" name="found_date" value="{{ log.log_date.strftime('%Y-%m-%d') }}" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="found_time">发现时间 <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="found_time" name="found_time" value="{{ now.strftime('%H:%M') if now else '12:00' }}" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description">问题描述 <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                </div>

                <div class="mb-3">
                    <label for="responsible_person">责任人</label>
                    <input type="text" class="form-control" id="responsible_person" name="responsible_person">
                </div>

                <div class="mb-3">
                    <label for="photos">照片上传</label>
                    <input type="file" class="form-control-file" id="photos" name="photos" multiple accept="image/*">
                    <small class="form-text text-muted">可以选择多张照片上传，支持jpg、jpeg、png格式</small>
                    <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
                </div>

                <div class="mb-3 mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('daily_management.issues', log_id=log.id) }}" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 照片预览
    document.getElementById('photos').addEventListener('change', function(e) {
        const previewsDiv = document.getElementById('photo-previews');
        previewsDiv.innerHTML = '';

        for (const file of this.files) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = document.createElement('img');
                img.src = event.target.result;
                img.className = 'photo-preview me-2 mb-2';
                previewsDiv.appendChild(img);
            }
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
