2025-06-15 20:27:48,658 INFO: 应用启动 - PID: 14692 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-15 20:27:57,119 INFO: 使用消耗计划的区域ID: 42 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-15 20:27:57,119 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-03, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-15 20:27:57,119 INFO: 查询周菜单：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-15 20:27:57,121 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-15 20:27:57,122 INFO:   - 周菜单ID: 37, 开始日期: 2025-06-02, 结束日期: 2025-06-08, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-15 20:27:57,124 INFO: 周菜单 37 总共有 77 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-15 20:27:57,124 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,124 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,124 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,124 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,124 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,125 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,126 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,127 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,128 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,129 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,130 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,130 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,130 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,130 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,130 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,130 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,130 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,131 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,131 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,131 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,131 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,131 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,131 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,131 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,132 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,132 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,132 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:57,136 INFO: 匹配条件 day_of_week=2, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-15 20:27:57,136 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-15 20:27:57,136 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-15 20:27:57,140 INFO: 步骤1: 读取消耗日期: 2025-06-03, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-15 20:27:57,143 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-15 20:27:57,145 INFO: 步骤2: 为出库食材 '鸡肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,146 INFO: 步骤2: 为出库食材 '羊肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,147 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,147 INFO: 步骤2: 为出库食材 '莲藕' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,149 INFO: 步骤2: 为出库食材 '韭菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,151 INFO: 步骤2: 为出库食材 '木耳' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,153 INFO: 步骤2: 为出库食材 '蒜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,154 INFO: 步骤2: 为出库食材 '吐司' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,155 INFO: 步骤2: 为出库食材 '芹菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,156 INFO: 步骤2: 为出库食材 '南瓜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,156 INFO: 步骤2: 为出库食材 '黄瓜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,157 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,157 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:57,157 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-15 20:31:45,945 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-15 20:31:45,962 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
