{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">设置通用属性</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 offset-md-2">
                            <!-- 步骤进度条 -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">创建产品批次流程</small>
                                    <small class="text-muted">步骤 3/5</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" class="w-60" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <small class="text-success">1. 基本信息</small>
                                    <small class="text-success">2. 选择食材</small>
                                    <small class="text-success fw-bold">3. 设置属性</small>
                                    <small class="text-muted">4. 个性调整</small>
                                    <small class="text-muted">5. 确认创建</small>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 批次信息</h5>
                                <p>批次名称：{{ batch.name }}</p>
                                <p>分类：{{ batch.category.name if batch.category else '' }}</p>
                                <p>供应商：{{ batch.supplier.name if batch.supplier else '' }}</p>
                            </div>

                            <form method="post" novalidate novalidate>
                                {{ form.csrf_token }}
                                {{ form.batch_id }}

                                <div class="mb-3">
                                    {{ form.price_strategy.label }}
                                    {{ form.price_strategy(class="form-control") }}
                                    {% if form.price_strategy.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.price_strategy.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3" id="fixedPriceGroup">
                                    {{ form.fixed_price.label }}
                                    <div class="input-group">
                                        {{ form.fixed_price(class="form-control") }}
                                        <div >
                                            <span class="input-group-text">元</span>
                                        </div>
                                    </div>
                                    {% if form.fixed_price.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.fixed_price.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.quality_cert.label }}
                                    {{ form.quality_cert(class="form-control") }}
                                    {% if form.quality_cert.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quality_cert.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.quality_standard.label }}
                                    {{ form.quality_standard(class="form-control") }}
                                    {% if form.quality_standard.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quality_standard.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.lead_time.label }}
                                    <div class="input-group">
                                        {{ form.lead_time(class="form-control") }}
                                        <div >
                                            <span class="input-group-text">天</span>
                                        </div>
                                    </div>
                                    {% if form.lead_time.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.lead_time.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.min_order_quantity.label }}
                                    {{ form.min_order_quantity(class="form-control") }}
                                    {% if form.min_order_quantity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.min_order_quantity.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.default_unit_id.label }}
                                    {{ form.default_unit_id(class="form-control") }}
                                    {% if form.default_unit_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.default_unit_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">选择默认的规格单位，如：千克、瓶等</small>
                                </div>

                                <div class="mb-3 text-center">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('product_batch.select_ingredients', id=batch.id) }}" class="btn btn-default">返回上一步</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 根据价格策略显示/隐藏固定价格输入框
    $('#price_strategy').change(function() {
        if ($(this).val() === 'fixed') {
            $('#fixedPriceGroup').show();
        } else {
            $('#fixedPriceGroup').hide();
        }
    });

    // 初始化
    $('#price_strategy').trigger('change');
});
</script>
{% endblock %}
