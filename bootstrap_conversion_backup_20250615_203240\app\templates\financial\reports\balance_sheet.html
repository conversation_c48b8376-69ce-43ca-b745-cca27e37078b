{% extends "financial/base.html" %}

{% block page_title %}资产负债表{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">资产负债表</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.reports_index') }}" class="uf-btn">
    <i class="fas fa-arrow-left uf-icon"></i> 返回报表列表
</a>
<button class="uf-btn uf-btn-success" onclick="exportReport()">
    <i class="fas fa-download uf-icon"></i> 导出Excel
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友财务软件风格报表参数 -->
<div style="background: #f8f9fa; border: 1px solid #c0c0c0; padding: 8px; margin-bottom: 8px; border-radius: 1px;">
    <form method="GET" style="margin: 0;">
        <div style="display: flex; align-items: center; gap: 12px;">
            <label style="font-size: 11px; color: #333; white-space: nowrap;">报表日期：</label>
            <input type="date" id="balance_date" name="balance_date" value="{{ balance_date }}"
                   onchange="this.form.submit()"
                   style="font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            <button type="submit" class="uf-btn uf-btn-primary uf-btn-sm">
                <i class="fas fa-sync" style="font-size: 10px;"></i> 刷新报表
            </button>
        </div>
    </form>
</div>

<!-- 用友财务软件专业资产负债表 -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
    <!-- 资产部分 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <i class="fas fa-coins uf-icon"></i> 资产
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <table class="uf-table" style="margin: 0;">
                <thead>
                    <tr>
                        <th style="width: 65%;">项目</th>
                        <th style="width: 35%;">金额</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 流动资产 -->
                    <tr style="background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%); font-weight: 600;">
                        <td style="font-size: 11px;"><strong>流动资产：</strong></td>
                        <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(assets.current_assets_total) }}</td>
                    </tr>
                    {% for item in assets.current_assets %}
                    <tr>
                        <td style="padding-left: 16px; font-size: 11px;">{{ item.name }}</td>
                        <td class="uf-amount-col">{{ "%.2f"|format(item.amount) }}</td>
                    </tr>
                    {% endfor %}

                    <!-- 非流动资产 -->
                    <tr style="background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%); font-weight: 600;">
                        <td style="font-size: 11px;"><strong>非流动资产：</strong></td>
                        <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(assets.non_current_assets_total) }}</td>
                    </tr>
                    {% for item in assets.non_current_assets %}
                    <tr>
                        <td style="padding-left: 16px; font-size: 11px;">{{ item.name }}</td>
                        <td class="uf-amount-col">{{ "%.2f"|format(item.amount) }}</td>
                    </tr>
                    {% endfor %}

                    <!-- 资产总计 -->
                    <tr style="background: linear-gradient(to bottom, #d4edda 0%, #c3e6cb 100%); font-weight: 700; border-top: 2px solid var(--uf-success);">
                        <td style="font-size: 11px;"><strong>资产总计</strong></td>
                        <td class="uf-amount-col" style="font-weight: 700; font-size: 12px;">{{ "%.2f"|format(assets.total_assets) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 负债和所有者权益部分 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <i class="fas fa-balance-scale uf-icon"></i> 负债和所有者权益
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <table class="uf-table" style="margin: 0;">
                <thead>
                    <tr>
                        <th style="width: 65%;">项目</th>
                        <th style="width: 35%;">金额</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 流动负债 -->
                    <tr style="background: linear-gradient(to bottom, #fff3cd 0%, #ffeaa7 100%); font-weight: 600;">
                        <td style="font-size: 11px;"><strong>流动负债：</strong></td>
                        <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(liabilities.current_liabilities_total) }}</td>
                    </tr>
                    {% for item in liabilities.current_liabilities %}
                    <tr>
                        <td style="padding-left: 16px; font-size: 11px;">{{ item.name }}</td>
                        <td class="uf-amount-col">{{ "%.2f"|format(item.amount) }}</td>
                    </tr>
                    {% endfor %}

                    <!-- 非流动负债 -->
                    <tr style="background: linear-gradient(to bottom, #fff3cd 0%, #ffeaa7 100%); font-weight: 600;">
                        <td style="font-size: 11px;"><strong>非流动负债：</strong></td>
                        <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(liabilities.non_current_liabilities_total) }}</td>
                    </tr>
                    {% for item in liabilities.non_current_liabilities %}
                    <tr>
                        <td style="padding-left: 16px; font-size: 11px;">{{ item.name }}</td>
                        <td class="uf-amount-col">{{ "%.2f"|format(item.amount) }}</td>
                    </tr>
                    {% endfor %}

                    <!-- 负债合计 -->
                    <tr style="background: linear-gradient(to bottom, #e2e3e5 0%, #d6d8db 100%); font-weight: 600;">
                        <td style="font-size: 11px;"><strong>负债合计</strong></td>
                        <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(liabilities.total_liabilities) }}</td>
                    </tr>

                    <!-- 所有者权益 -->
                    <tr style="background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%); font-weight: 600;">
                        <td style="font-size: 11px;"><strong>所有者权益：</strong></td>
                        <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(equity.total_equity) }}</td>
                    </tr>
                    {% for item in equity.equity_items %}
                    <tr>
                        <td style="padding-left: 16px; font-size: 11px;">{{ item.name }}</td>
                        <td class="uf-amount-col">{{ "%.2f"|format(item.amount) }}</td>
                    </tr>
                    {% endfor %}

                    <!-- 负债和所有者权益总计 -->
                    <tr style="background: linear-gradient(to bottom, #d4edda 0%, #c3e6cb 100%); font-weight: 700; border-top: 2px solid var(--uf-success);">
                        <td style="font-size: 11px;"><strong>负债和所有者权益总计</strong></td>
                        <td class="uf-amount-col" style="font-weight: 700; font-size: 12px;">{{ "%.2f"|format(liabilities.total_liabilities + equity.total_equity) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 用友风格报表说明 -->
<div class="uf-card" style="margin-top: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-info-circle uf-icon"></i> 报表说明
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; font-size: 12px;">
            <div>
                <strong>报表日期：</strong>{{ balance_date }}
            </div>
            <div>
                <strong>编制单位：</strong>{{ user_area.name }}
            </div>
            <div>
                <strong>金额单位：</strong>人民币元
            </div>
            <div>
                <strong>平衡检查：</strong>
                {% set balance_check = assets.total_assets - (liabilities.total_liabilities + equity.total_equity) %}
                {% if balance_check == 0 %}
                <span style="color: var(--uf-success);">平衡</span>
                {% else %}
                <span style="color: var(--uf-danger);">不平衡 (差额: ¥{{ "%.2f"|format(balance_check) }})</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
function exportReport() {
    const balanceDate = document.getElementById('balance_date').value;
    const url = `{{ url_for('financial.export_report', report_type='balance_sheet') }}?balance_date=${balanceDate}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
