<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务凭证打印 - {{ voucher.voucher_number }}</title>
    
    <!-- 引入打印样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='financial/css/print-styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
    
    <style>
        /* 页面特定样式 */
        body {
            font-family: 'SimSun', '宋体', serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .voucher-container {
            max-width: 297mm;
            margin: 0 auto;
            background: white;
            padding: 15mm;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .voucher-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }
        
        .voucher-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .voucher-info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .voucher-info-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .voucher-info-item label {
            font-weight: bold;
            min-width: 80px;
        }
        
        .voucher-info-item .value {
            border-bottom: 1px solid #000;
            min-width: 120px;
            padding: 2px 5px;
        }
        
        .voucher-details {
            margin: 20px 0;
        }
        
        .voucher-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 12px;
        }
        
        .voucher-table th,
        .voucher-table td {
            border: 1px solid #000;
            padding: 8px 6px;
            text-align: left;
            vertical-align: middle;
        }
        
        .voucher-table th {
            background: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        
        .voucher-table .amount {
            text-align: right;
            font-family: 'Arial', 'Times New Roman', monospace;
        }
        
        .voucher-table .total-row {
            background: #f0f0f0;
            font-weight: bold;
        }
        
        .voucher-signature {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ccc;
        }
        
        .signature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            width: 100px;
            height: 20px;
            display: inline-block;
        }
        
        .print-controls {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .print-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        
        .print-btn:hover {
            background: #0056b3;
        }
        
        .print-btn.landscape {
            background: #28a745;
        }
        
        .print-btn.landscape:hover {
            background: #1e7e34;
        }
        
        /* 打印时隐藏控制按钮 */
        @d-flex print {
            .print-controls {
                display: none !important;
            }
            
            .voucher-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
                max-width: none;
            }
            
            @page {
                size: A4 landscape;
                margin: 15mm;
            }
        }
    </style>
</head>
<body class="voucher-print">
    <!-- 打印控制按钮 -->
    <div class="print-controls no-print">
        <button class="print-btn landscape" onclick="window.print()">
            <i class="fas fa-print"></i> 打印凭证（A4横向）
        </button>
        <button class="print-btn" onclick="window.close()">
            <i class="fas fa-times"></i> 关闭
        </button>
        <a href="{{ url_for('financial.export_voucher_pdf', voucher_id=voucher.id) }}" class="print-btn" style="text-decoration: none;">
            <i class="fas fa-file-pdf"></i> 导出PDF
        </a>
        <a href="{{ url_for('financial.export_voucher_excel', voucher_id=voucher.id) }}" class="print-btn" style="text-decoration: none;">
            <i class="fas fa-file-excel"></i> 导出Excel
        </a>
    </div>

    <!-- 凭证内容 -->
    <div class="voucher-container">
        <!-- 凭证标题 -->
        <div class="voucher-header">
            <div class="voucher-title">记账凭证</div>
            <div style="font-size: 14px; color: #666;">{{ user_area.name }}</div>
        </div>

        <!-- 凭证基本信息 -->
        <div class="voucher-info-row">
            <div class="voucher-info-item">
                <label>凭证字号:</label>
                <span class="value">{{ voucher.voucher_type }} 第{{ voucher.voucher_number }}号</span>
            </div>
            <div class="voucher-info-item">
                <label>日期:</label>
                <span class="value">{{ voucher.voucher_date.strftime('%Y年%m月%d日') }}</span>
            </div>
        </div>

        <!-- 凭证明细表格 -->
        <div class="voucher-details">
            <table class="voucher-table">
                <thead>
                    <tr>
                        <th style="width: 25%;">摘要</th>
                        <th style="width: 35%;">会计科目</th>
                        <th style="width: 20%;">借方金额</th>
                        <th style="width: 20%;">贷方金额</th>
                    </tr>
                </thead>
                <tbody>
                    {% set total_debit = 0 %}
                    {% set total_credit = 0 %}
                    {% for detail in details %}
                    <tr>
                        <td>{{ detail.summary or voucher.summary }}</td>
                        <td>{{ detail.subject.code }} {{ detail.subject.name }}</td>
                        <td class="amount">
                            {% if detail.debit_amount > 0 %}
                                {{ "%.2f"|format(detail.debit_amount|float) }}
                                {% set total_debit = total_debit + detail.debit_amount %}
                            {% endif %}
                        </td>
                        <td class="amount">
                            {% if detail.credit_amount > 0 %}
                                {{ "%.2f"|format(detail.credit_amount|float) }}
                                {% set total_credit = total_credit + detail.credit_amount %}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    
                    <!-- 合计行 -->
                    <tr class="total-row">
                        <td><strong>合计</strong></td>
                        <td></td>
                        <td class="amount"><strong>{{ "%.2f"|format(total_debit|float) }}</strong></td>
                        <td class="amount"><strong>{{ "%.2f"|format(total_credit|float) }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 金额大写 -->
        <div style="margin: 15px 0; font-size: 12px;">
            <strong>金额大写：</strong>
            <span style="border-bottom: 1px solid #000; padding: 2px 10px; margin-left: 10px;">
                人民币{{ "%.2f"|format(total_debit|float) }}元整
            </span>
        </div>

        <!-- 附件张数 -->
        <div style="margin: 15px 0; font-size: 12px;">
            <strong>附件张数：</strong>
            <span style="border-bottom: 1px solid #000; padding: 2px 20px; margin-left: 10px;">
                {{ voucher.attachment_count or 0 }}
            </span>
            <span style="margin-left: 10px;">张</span>
        </div>

        <!-- 签名栏 -->
        <div class="voucher-signature">
            <div class="signature-item">
                <label>制单:</label>
                <span class="signature-line"></span>
            </div>
            <div class="signature-item">
                <label>审核:</label>
                <span class="signature-line"></span>
            </div>
            <div class="signature-item">
                <label>记账:</label>
                <span class="signature-line"></span>
            </div>
            <div class="signature-item">
                <label>出纳:</label>
                <span class="signature-line"></span>
            </div>
        </div>

        <!-- 打印信息 -->
        <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666;">
            打印时间：{{ moment().format('YYYY-MM-DD HH:mm:ss') }}
        </div>
    </div>

    <script>
        // 自动聚焦到打印按钮
        document.addEventListener('DOMContentLoaded', function() {
            // 如果是通过打印链接打开的，自动打印
            if (window.location.search.includes('auto_print=1')) {
                setTimeout(function() {
                    window.print();
                }, 500);
            }
        });
        
        // 打印后关闭窗口
        window.addEventListener('afterprint', function() {
            if (window.location.search.includes('auto_print=1')) {
                setTimeout(function() {
                    window.close();
                }, 1000);
            }
        });
    </script>
</body>
</html>
