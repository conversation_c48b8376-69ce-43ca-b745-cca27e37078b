#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板语法修复工具
检查和修复Jinja2模板中的语法错误
"""

import os
import re
from pathlib import Path

class TemplateSyntaxFixer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.fixes_applied = 0
        self.files_checked = 0
        
    def check_and_fix_template(self, file_path):
        """检查并修复单个模板文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_content = content
            
            # 修复常见的Jinja2语法错误
            
            # 1. 修复不完整的条件语句
            # 例如: {% if condition %} not other_condition %}
            content = re.sub(
                r'(\{%\s*if\s+[^}]+\s+)not\s+([^}]+)\s*%\}([^{]*)\{%',
                r'\1%}{% if not \2 %}\3{% endif %}{% if \1',
                content
            )
            
            # 2. 修复错误的class属性拼接
            # 例如: class="a {% if b %}c{% endif %} not d %}e{%"
            content = re.sub(
                r'class="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"',
                r'class="\1{% if not \2 %} \3{% endif %}"',
                content
            )
            
            # 3. 修复不匹配的引号
            content = re.sub(
                r'class="([^"]*)\{%"',
                r'class="\1"',
                content
            )
            
            # 4. 修复空的div标签
            content = re.sub(
                r'<div\s*>\s*</div>',
                r'<div class="input-group-text"></div>',
                content
            )
            
            # 5. 修复Bootstrap 4到5的遗留问题
            fixes = [
                # 修复badge类名
                (r'badge-(\w+)', r'bg-\1'),
                # 修复按钮类名
                (r'btn-default', r'btn-secondary'),
                # 修复关闭按钮
                (r'<button[^>]*class="[^"]*close[^"]*"[^>]*>\s*<span[^>]*>&times;</span>\s*</button>',
                 r'<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>'),
                # 修复模态框属性
                (r'data-dismiss="modal"', r'data-bs-dismiss="modal"'),
                (r'data-toggle="modal"', r'data-bs-toggle="modal"'),
                (r'data-target="([^"]*)"', r'data-bs-target="\1"'),
            ]
            
            for pattern, replacement in fixes:
                content = re.sub(pattern, replacement, content)
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.fixes_applied += 1
                print(f"✅ 修复了 {file_path.relative_to(self.project_root)}")
                
            self.files_checked += 1
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
    
    def scan_templates(self):
        """扫描所有模板文件"""
        template_dir = self.project_root / "app" / "templates"
        
        if not template_dir.exists():
            print("❌ 模板目录不存在")
            return
            
        print("🔍 扫描模板文件...")
        
        for file_path in template_dir.rglob("*.html"):
            self.check_and_fix_template(file_path)
    
    def run(self):
        """运行修复工具"""
        print("🚀 模板语法修复工具启动")
        print("=" * 40)
        
        self.scan_templates()
        
        print(f"\n📊 修复完成:")
        print(f"   检查文件: {self.files_checked}")
        print(f"   修复文件: {self.fixes_applied}")

if __name__ == "__main__":
    fixer = TemplateSyntaxFixer()
    fixer.run()
