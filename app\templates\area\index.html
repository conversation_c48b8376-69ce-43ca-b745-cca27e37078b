{% extends 'base.html' %}
{% from 'components/page_header.html' import page_header, stats_page_header %}
{% from 'components/cards.html' import info_card, list_card, table_card %}
{% from 'components/area_macros.html' import area_tree_content, current_area_info, area_stats %}

{% block title %}区域管理 - {{ super() }}{% endblock %}

{% block content %}
<!-- 页面头部 -->
{% set breadcrumbs = [{'title': '首页', 'url': url_for('main.index')}, {'title': '区域管理'}] %}
{% if current_area %}
  {% for area in area_path %}
    {% set breadcrumbs = breadcrumbs + [{'title': area.name, 'url': url_for('area.view_area', id=area.id) if not loop.last else None}] %}
  {% endfor %}
{% endif %}

{% set actions = [] %}
{% if current_user.is_admin() %}
  {% set actions = [{'url': url_for('area.add_area'), 'text': '添加区域', 'icon': 'fas fa-plus', 'class': 'btn-primary'}] %}
{% endif %}

<!-- 现代化页面头部 -->
<div class="d-flex justify-content-between align-items-start mb-4">
  <div class="flex-grow-1">
    <h1 class="h3 mb-2 fw-bold text-dark">区域管理</h1>
    <p class="text-muted mb-3 fs-6">管理系统中的区域层级结构，包括县市区、乡镇、学校和食堂</p>

    {% if breadcrumbs %}
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb breadcrumb-sm mb-0">
        {% for crumb in breadcrumbs %}
        <li class="breadcrumb-item {% if loop.last %}active{% endif %}">
          {% if not loop.last and crumb.url %}
          <a href="{{ crumb.url }}" class="text-decoration-none">{{ crumb.title }}</a>
          {% else %}
          {{ crumb.title }}
          {% endif %}
        </li>
        {% endfor %}
      </ol>
    </nav>
    {% endif %}
  </div>

  {% if actions %}
  <div class="flex-shrink-0 ms-3">
    {% for action in actions %}
    <a href="{{ action.url }}"
       class="btn btn-primary shadow-sm"
       style="border-radius: 12px;">
      <i class="{{ action.icon }} me-2"></i>{{ action.text }}
    </a>
    {% endfor %}
  </div>
  {% endif %}
</div>

<!-- 主要内容区域 -->
<div class="row g-3">
    <!-- 左侧：区域树形结构 -->
    <div class="col-lg-4">
        {{ info_card(
          title="区域树形结构",
          icon="fas fa-sitemap",
          color="primary",
          content=area_tree_content(top_level_areas, current_user)
        ) }}
    </div>

    <!-- 右侧：区域详情和操作 -->
    <div class="col-lg-8">
        {% if current_area %}
        <!-- 当前区域信息卡片 -->
        <div class="row g-3 mb-3">
            <div class="col-md-8">
                {{ info_card(
                  title="当前区域: " + current_area.name,
                  icon="fas fa-map-marker-alt",
                  color="primary",
                  content=current_area_info(current_area, current_user)
                ) }}
            </div>
            <div class="col-md-4">
                {{ info_card(
                  title="区域统计",
                  icon="fas fa-chart-bar",
                  color="info",
                  content=area_stats(current_area)
                ) }}
            </div>
        </div>

        <!-- 下级区域列表 -->
        {% if current_area.children %}
        {% set child_headers = [
          {'text': '名称', 'class': ''},
          {'text': '代码', 'class': ''},
          {'text': '级别', 'class': ''},
          {'text': '操作', 'class': 'text-center'}
        ] %}

        {% set child_rows = [] %}
        {% for child in current_area.children %}
          {% set row = [
            {
              'text': child.name + (' <span class="badge bg-primary ms-1">乡镇学校</span>' if child.level == 3 and child.is_township_school else ''),
              'type': 'html'
            },
            {'text': child.code},
            {'text': '<span class="badge bg-' + ('success' if child.level == 2 else 'warning' if child.level == 3 else 'info') + '">' + child.get_level_name() + '</span>', 'type': 'html'},
            {
              'type': 'actions',
              'actions': [
                {'url': url_for('area.view_area', id=child.id), 'icon': 'fas fa-eye', 'title': '查看详情'},
                {'url': url_for('area.switch_area', id=child.id), 'icon': 'fas fa-exchange-alt', 'title': '切换到此区域'}
              ]
            }
          ] %}
          {% set child_rows = child_rows + [row] %}
        {% endfor %}

        {{ table_card(
          title="下级区域 (" + current_area.children|length|string + "个)",
          headers=child_headers,
          rows=child_rows
        ) }}
        {% endif %}
        {% else %}
        <!-- 欢迎信息 -->
        <div class="card border-0 bg-light">
            <div class="card-body text-center py-5">
                <i class="fas fa-map-marked-alt fs-1 text-primary mb-3"></i>
                <h4 class="text-primary mb-3">欢迎使用区域管理!</h4>
                <p class="text-muted mb-4">
                    请从左侧区域树中选择一个区域，或者点击"添加区域"按钮创建新区域。<br>
                    区域管理是系统的核心功能，用于管理县市区/乡镇/学校/食堂的层级结构。
                </p>
                {% if current_user.is_admin() %}
                <a href="{{ url_for('area.add_area') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>开始创建区域
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 使用原生JavaScript替代jQuery
document.addEventListener('DOMContentLoaded', function() {
    // 自动展开当前区域的父级节点
    {% if current_area %}
    {% for area in area_path %}
    const areaElement = document.getElementById('area-{{ area.id }}');
    if (areaElement) {
        areaElement.classList.add('show');
    }
    {% endfor %}
    {% endif %}

    // 添加树形结构的交互效果
    const toggleButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const target = document.querySelector(this.getAttribute('data-bs-target'));

            // 切换图标
            setTimeout(() => {
                if (target.classList.contains('show')) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }, 150);
        });
    });
});
</script>
{% endblock %}
