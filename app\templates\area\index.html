{% extends 'base.html' %}

{% block title %}区域管理 - {{ super() }}{% endblock %}

{% block content %}
<!-- 页面头部 -->
{% set breadcrumbs = [{'title': '首页', 'url': url_for('main.index')}, {'title': '区域管理'}] %}
{% if current_area %}
  {% for area in area_path %}
    {% set breadcrumbs = breadcrumbs + [{'title': area.name, 'url': url_for('area.view_area', id=area.id) if not loop.last else None}] %}
  {% endfor %}
{% endif %}

{% set actions = [] %}
{% if current_user.is_admin() %}
  {% set actions = [{'url': url_for('area.add_area'), 'text': '添加区域', 'icon': 'fas fa-plus', 'class': 'btn-primary'}] %}
{% endif %}

<!-- 现代化页面头部 -->
<div class="d-flex justify-content-between align-items-start mb-4">
  <div class="flex-grow-1">
    <h1 class="h3 mb-2 fw-bold text-dark">区域管理</h1>
    <p class="text-muted mb-3 fs-6">管理系统中的区域层级结构，包括县市区、乡镇、学校和食堂</p>

    {% if breadcrumbs %}
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb breadcrumb-sm mb-0">
        {% for crumb in breadcrumbs %}
        <li class="breadcrumb-item {% if loop.last %}active{% endif %}">
          {% if not loop.last and crumb.url %}
          <a href="{{ crumb.url }}" class="text-decoration-none">{{ crumb.title }}</a>
          {% else %}
          {{ crumb.title }}
          {% endif %}
        </li>
        {% endfor %}
      </ol>
    </nav>
    {% endif %}
  </div>

  {% if actions %}
  <div class="flex-shrink-0 ms-3">
    {% for action in actions %}
    <a href="{{ action.url }}"
       class="btn btn-primary shadow-sm"
       style="border-radius: 12px;">
      <i class="{{ action.icon }} me-2"></i>{{ action.text }}
    </a>
    {% endfor %}
  </div>
  {% endif %}
</div>

<!-- 主要内容区域 -->
<div class="row g-3">
    <!-- 左侧：区域树形结构 -->
    <div class="col-lg-4">
        <div class="card h-100">
          <div class="card-header bg-primary text-white py-2">
            <h6 class="card-title mb-0 d-flex align-items-center">
              <i class="fas fa-sitemap me-2"></i>区域树形结构
            </h6>
          </div>
          <div class="card-body">
            <div id="area-tree" class="area-tree">
                {% if top_level_areas %}
                <div class="list-group list-group-flush">
                    {% for area in top_level_areas %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ url_for('area.view_area', id=area.id) }}"
                               class="text-decoration-none d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <span>{{ area.name }}</span>
                                <span class="badge bg-info ms-2">{{ area.get_level_name() }}</span>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-map-marked-alt fs-1 text-muted opacity-50 mb-3"></i>
                    <p class="text-muted mb-0">暂无区域数据</p>
                    {% if current_user.is_admin() %}
                    <a href="{{ url_for('area.add_area') }}" class="btn btn-primary btn-sm mt-2">
                        <i class="fas fa-plus me-1"></i>添加第一个区域
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧：区域详情和操作 -->
    <div class="col-lg-8">
        {% if current_area %}
        <!-- 当前区域信息卡片 -->
        <div class="row g-3 mb-3">
            <div class="col-md-8">
                <div class="card h-100">
                  <div class="card-header bg-primary text-white py-2">
                    <h6 class="card-title mb-0 d-flex align-items-center">
                      <i class="fas fa-map-marker-alt me-2"></i>当前区域: {{ current_area.name }}
                    </h6>
                  </div>
                  <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">区域名称</dt>
                        <dd class="col-sm-8">{{ current_area.name }}</dd>
                        <dt class="col-sm-4">区域代码</dt>
                        <dd class="col-sm-8"><code>{{ current_area.code }}</code></dd>
                        <dt class="col-sm-4">区域级别</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-info">{{ current_area.get_level_name() }}</span>
                        </dd>
                    </dl>
                  </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                  <div class="card-header bg-info text-white py-2">
                    <h6 class="card-title mb-0 d-flex align-items-center">
                      <i class="fas fa-chart-bar me-2"></i>区域统计
                    </h6>
                  </div>
                  <div class="card-body text-center">
                    <div class="p-2 bg-light rounded">
                        <div class="fs-4 fw-bold text-primary">{{ current_area.children|length }}</div>
                        <small class="text-muted">下级区域</small>
                    </div>
                  </div>
                </div>
            </div>
        </div>

        <!-- 下级区域列表 -->
        {% if current_area.children %}
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">下级区域 ({{ current_area.children|length }}个)</h6>
          </div>
          <div class="table-responsive">
            <table class="table table-hover table-sm mb-0">
              <thead class="table-light">
                <tr>
                  <th>名称</th>
                  <th>代码</th>
                  <th>级别</th>
                  <th class="text-center">操作</th>
                </tr>
              </thead>
              <tbody>
                {% for child in current_area.children %}
                <tr>
                  <td>
                    {{ child.name }}
                    {% if child.level == 3 and child.is_township_school %}
                    <span class="badge bg-primary ms-1">乡镇学校</span>
                    {% endif %}
                  </td>
                  <td>{{ child.code }}</td>
                  <td>
                    <span class="badge bg-{% if child.level == 2 %}success{% elif child.level == 3 %}warning{% else %}info{% endif %}">
                      {{ child.get_level_name() }}
                    </span>
                  </td>
                  <td class="text-center">
                    <div class="btn-group btn-group-sm">
                      <a href="{{ url_for('area.view_area', id=child.id) }}"
                         class="btn btn-outline-secondary btn-sm" title="查看详情">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{{ url_for('area.switch_area', id=child.id) }}"
                         class="btn btn-outline-secondary btn-sm" title="切换到此区域">
                        <i class="fas fa-exchange-alt"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
        {% endif %}
        {% else %}
        <!-- 欢迎信息 -->
        <div class="card border-0 bg-light">
            <div class="card-body text-center py-5">
                <i class="fas fa-map-marked-alt fs-1 text-primary mb-3"></i>
                <h4 class="text-primary mb-3">欢迎使用区域管理!</h4>
                <p class="text-muted mb-4">
                    请从左侧区域树中选择一个区域，或者点击"添加区域"按钮创建新区域。<br>
                    区域管理是系统的核心功能，用于管理县市区/乡镇/学校/食堂的层级结构。
                </p>
                {% if current_user.is_admin() %}
                <a href="{{ url_for('area.add_area') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>开始创建区域
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 使用原生JavaScript替代jQuery
document.addEventListener('DOMContentLoaded', function() {
    // 自动展开当前区域的父级节点
    {% if current_area %}
    {% for area in area_path %}
    const areaElement = document.getElementById('area-{{ area.id }}');
    if (areaElement) {
        areaElement.classList.add('show');
    }
    {% endfor %}
    {% endif %}

    // 添加树形结构的交互效果
    const toggleButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const target = document.querySelector(this.getAttribute('data-bs-target'));

            // 切换图标
            setTimeout(() => {
                if (target.classList.contains('show')) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }, 150);
        });
    });
});
</script>
{% endblock %}
