{% extends 'admin/base_admin.html' %}

{% block page_title %}
{% if user %}编辑用户{% else %}添加用户{% endif %}
{% endblock %}

{% block toolbar_actions %}
<a href="{{ url_for('system.users') }}" class="btn btn-secondary">
    <i class="fas fa-arrow-left"></i> 返回列表
</a>
{% endblock %}

{% block content_body %}
<div class="card">
    <div class="card-body">
        <form id="userForm" method="post" class="needs-validation" novalidate>
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <div class="row g-3">
                <!-- 基本信息 -->
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header py-2">
                            <h6 class="mb-0">基本信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control form-control-sm" id="username" name="username"
                                       value="{{ user.username if user else '' }}" required
                                       {% if user %}readonly{% endif %}>
                                <div class="invalid-feedback">请输入用户名</div>
                            </div>

                            <div class="mb-3">
                                <label for="real_name" class="form-label">真实姓名</label>
                                <input type="text" class="form-control form-control-sm" id="real_name" name="real_name"
                                       value="{{ user.real_name if user else '' }}">
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">电子邮箱</label>
                                <input type="email" class="form-control form-control-sm" id="email" name="email"
                                       value="{{ user.email if user else '' }}" required>
                                <div class="invalid-feedback">请输入有效的电子邮箱</div>
                            </div>

                            {% if not user %}
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control form-control-sm" id="password" name="password"
                                       required minlength="6">
                                <div class="invalid-feedback">密码长度至少为6位</div>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">确认密码</label>
                                <input type="password" class="form-control form-control-sm" id="confirm_password"
                                       name="confirm_password" required>
                                <div class="invalid-feedback">两次输入的密码不一致</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- 权限设置 -->
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header py-2">
                            <h6 class="mb-0">权限设置</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="area_id" class="form-label">所属区域</label>
                                <select class="form-select form-select-sm" id="area_id" name="area_id">
                                    <option value="">-- 请选择区域 --</option>
                                    {% for area in areas %}
                                    <option value="{{ area.id }}" {% if user and user.area_id == area.id %}selected{% endif %}>
                                        {{ area.get_level_name() }} - {{ area.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">角色</label>
                                <div class="row g-2">
                                    {% for role in roles %}
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="role-{{ role.id }}"
                                                   name="roles" value="{{ role.id }}"
                                                   {% if user and role in user.roles %}checked{% endif %}>
                                            <label class="form-check-label" for="role-{{ role.id }}">
                                                {{ role.name }}
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select form-select-sm" id="status" name="status">
                                    <option value="1" {% if user and user.status == 1 %}selected{% endif %}>启用</option>
                                    <option value="0" {% if user and user.status == 0 %}selected{% endif %}>禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header py-2">
                            <h6 class="mb-0">其他信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="notes" class="form-label">备注</label>
                                <textarea class="form-control form-control-sm" id="notes" name="notes" rows="3">{{ user.notes if user else '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <a href="{{ url_for('system.users') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 取消
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('userForm');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');

    // 密码确认验证
    if (confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            if (this.value !== password.value) {
                this.setCustomValidity('两次输入的密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });

        password.addEventListener('input', function() {
            if (confirmPassword.value) {
                confirmPassword.dispatchEvent(new Event('input'));
            }
        });
    }

    // 表单提交验证
    form.addEventListener('submit', function(event) {
        if (!validateForm('userForm')) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
});
</script>
{% endblock %}
