{% extends 'base.html' %}

{% block title %}出库明细详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">出库明细详情</h3>
                    <div class="card-tools">
                        {# Link back to the related Stock Out detail page #}
                        <a href="{{ url_for('stock_out.view', id=stock_out.id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回出库单详情
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    {# Stock Out Item Details #}
                    <div class="card mb-3">
                        <div class="card-header">出库明细信息</div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">出库明细ID</th>
                                    <td>{{ item.id }}</td>
                                </tr>
                                <tr>
                                    <th>食材名称</th>
                                    <td>{{ ingredient.name if ingredient else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>出库数量</th>
                                    <td>{{ item.quantity }}</td>
                                </tr>
                                <tr>
                                    <th>单位</th>
                                    <td>{{ item.unit }}</td>
                                </tr>
                                <tr>
                                    <th>批次号</th>
                                    <td>{{ item.batch_number }}</td>
                                </tr>
                                {% if item.notes %}
                                <tr>
                                    <th>备注</th>
                                    <td>{{ item.notes }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ item.created_at|format_datetime() }}</td>
                                </tr>
                                <tr>
                                    <th>更新时间</th>
                                    <td>{{ item.updated_at|format_datetime() }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {# Related Stock Out Information #}
                    <div class="card mb-3">
                        <div class="card-header">关联出库单信息</div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">出库单号</th>
                                    <td>
                                        <a href="{{ url_for('stock_out.view', id=stock_out.id) }}">
                                            {{ stock_out.stock_out_number }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>仓库</th>
                                    <td>{{ stock_out.warehouse.name if stock_out and stock_out.warehouse else '-' }}</td>
                                </tr>
                                {% if stock_out and stock_out.warehouse and stock_out.warehouse.area %}
                                <tr>
                                    <th>学校/区域</th>
                                    <td>{{ stock_out.warehouse.area.name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>出库日期</th>
                                    <td>{{ stock_out.stock_out_date|format_datetime('%Y-%m-%d') if stock_out and stock_out.stock_out_date else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>出库类型</th>
                                    <td>{{ stock_out.stock_out_type if stock_out else '-' }}</td>
                                </tr>
                                {# Link to related Consumption Plan if exists #}
                                {% if stock_out and stock_out.consumption_plan_id %}
                                <tr>
                                    <th>关联消耗计划ID</th>
                                    <td>
                                         <a href="{{ url_for('consumption_plan.view', id=stock_out.consumption_plan_id) }}">
                                            {{ stock_out.consumption_plan_id }}
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    {# Related Inventory Batch Information #}
                    {% if inventory %}
                    <div class="card mb-3">
                        <div class="card-header">关联库存批次信息</div>
                        <div class="card-body">
                             <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">库存ID</th>
                                    <td>
                                        {# Assuming there is an inventory detail page #}
                                        {# <a href="{{ url_for('inventory.detail', id=inventory.id) }}">
                                            {{ inventory.id }}
                                        </a> #}
                                        {{ inventory.id }}
                                    </td>
                                </tr>
                                 <tr>
                                    <th>批次号</th>
                                    <td>{{ inventory.batch_number }}</td>
                                </tr>
                                <tr>
                                    <th>当前数量</th>
                                    <td>{{ inventory.quantity }}</td>
                                </tr>
                                <tr>
                                    <th>总入库数量</th>
                                    <td>{{ inventory.initial_quantity }}</td>
                                </tr>
                                <tr>
                                    <th>单位</th>
                                    <td>{{ inventory.unit }}</td>
                                </tr>
                                <tr>
                                    <th>生产日期</th>
                                    <td>{{ inventory.production_date|format_datetime('%Y-%m-%d') if inventory.production_date else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>有效期至</th>
                                    <td>{{ inventory.expiry_date|format_datetime('%Y-%m-%d') if inventory.expiry_date else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>入库日期</th>
                                    <td>{{ inventory.stock_in_date|format_datetime('%Y-%m-%d') if inventory.stock_in_date else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>{{ inventory.status }}</td>
                                </tr>
                                <tr>
                                    <th>存放位置</th>
                                    <td>{{ inventory.storage_location.name if inventory.storage_location else '-' }}</td>
                                </tr>
                                {% if inventory.notes %}
                                <tr>
                                    <th>备注</th>
                                    <td>{{ inventory.notes }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    {# Traced Original Stock In Information #}
                    {% if original_stock_in %}
                    <div class="card mb-3">
                        <div class="card-header">追溯入库单信息 (此批次首次入库)</div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">入库单号</th>
                                    <td>
                                        {# Assuming there is a stock in detail page #}
                                        {# <a href="{{ url_for('stock_in.view', id=original_stock_in.id) }}">
                                            {{ original_stock_in.stock_in_number }}
                                        </a> #}
                                        {{ original_stock_in.stock_in_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>入库日期</th>
                                    <td>{{ original_stock_in.stock_in_date|format_datetime('%Y-%m-%d') if original_stock_in.stock_in_date else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>供应商</th>
                                    <td>{{ original_stock_in.supplier.name if original_stock_in.supplier else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>操作人</th>
                                    <td>{{ original_stock_in.operator.real_name or original_stock_in.operator.username if original_stock_in.operator else '-' }}</td>
                                </tr>
                                {% if original_stock_in_item %}
                                <tr>
                                     <th>入库数量 (此批次)</th>
                                     <td>{{ original_stock_in_item.quantity }} {{ original_stock_in_item.unit }}</td>
                                </tr>
                                {% endif %}
                                {% if original_stock_in.notes %}
                                <tr>
                                    <th>备注</th>
                                    <td>{{ original_stock_in.notes }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                    {% endif %}


                </div> {# card-body #}
            </div> {# card #}
        </div> {# col #}
    </div> {# row #}
</div> {# container-fluid #}
{% endblock %} 