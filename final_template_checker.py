#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终模板语法检查工具
专门检查和修复常见的Jinja2语法错误
"""

import os
import re
from pathlib import Path

class FinalTemplateChecker:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.fixes_applied = 0
        self.files_fixed = 0
        self.files_checked = 0

    def fix_file(self, file_path):
        """修复单个文件的语法错误"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_content = content
            changes_made = 0
            
            # 修复1: 错误的elif语法 - 缺少unified_status ==
            # 例如: {% elif '已确认' %} -> {% elif unified_status == '已确认' %}
            pattern1 = r"{% elif\s+'([^']+)'\s*%}"
            def fix_elif_status(match):
                status = match.group(1)
                return f"{{% elif unified_status == '{status}' %}}"
            
            new_content = re.sub(pattern1, fix_elif_status, content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复2: 错误的if语法 - 缺少变量名
            # 例如: {% if not in list %} -> {% if variable not in list %}
            pattern2 = r"{% if\s+not in\s+([^}]+)\s*%}"
            def fix_not_in(match):
                list_name = match.group(1)
                return f"{{% if variable not in {list_name} %}}"
            
            new_content = re.sub(pattern2, fix_not_in, content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复3: 嵌套的if语句错误
            # 例如: {% if condition %}{% if not in list %} -> {% if condition and variable not in list %}
            pattern3 = r"{% if\s+([^}]+)\s*%}{% if\s+not in\s+([^}]+)\s*%}"
            def fix_nested_if(match):
                condition = match.group(1)
                list_name = match.group(2)
                return f"{{% if {condition} and variable not in {list_name} %}}"
            
            new_content = re.sub(pattern3, fix_nested_if, content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复4: 错误的变量赋值在if语句中
            # 例如: {% if {% if condition set _ = list.append(item) %} -> {% if condition %}{% set _ = list.append(item) %}
            pattern4 = r"{% if\s+{% if\s+([^}]+)\s+set\s+([^}]+)\s*%}"
            def fix_nested_set(match):
                condition = match.group(1)
                assignment = match.group(2)
                return f"{{% if {condition} %}}{{% set {assignment} %}}"
            
            new_content = re.sub(pattern4, fix_nested_set, content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复5: 特定的角色名称检查错误
            pattern5 = r"{% if\s+not in\s+\['系统管理员',\s*'管理员',\s*'超级管理员'\]"
            new_content = re.sub(pattern5, "{% if role.name not in ['系统管理员', '管理员', '超级管理员']", content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复6: 权限检查错误
            pattern6 = r"{% if\s+not in\s+all_permissions\s*%}"
            new_content = re.sub(pattern6, "{% if module not in all_permissions %}", content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复7: 模块权限检查错误
            pattern7 = r"{% if\s+not in\s+all_permissions\[module\]\s*%}"
            new_content = re.sub(pattern7, "{% if action not in all_permissions[module] %}", content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复8: 类型统计错误
            pattern8 = r"{% if\s+not in\s+type_stats\s*%}"
            new_content = re.sub(pattern8, "{% if item.subject_type not in type_stats %}", content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 修复9: 类型汇总错误
            pattern9 = r"{% if\s+not in\s+type_summary\s*%}"
            new_content = re.sub(pattern9, "{% if item.subject_type not in type_summary %}", content)
            if new_content != content:
                content = new_content
                changes_made += 1
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.files_fixed += 1
                self.fixes_applied += changes_made
                print(f"✅ 修复了 {file_path.relative_to(self.project_root)} - {changes_made} 处修改")
                
            self.files_checked += 1
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")

    def scan_and_fix_templates(self):
        """扫描并修复所有模板文件"""
        template_dir = self.project_root / "app" / "templates"
        
        if not template_dir.exists():
            print("❌ 模板目录不存在")
            return
            
        print("🔧 最终检查和修复Jinja2模板语法错误...")
        
        for file_path in template_dir.rglob("*.html"):
            self.fix_file(file_path)

    def run(self):
        """运行修复工具"""
        print("🚀 最终模板语法检查和修复工具启动")
        print("=" * 50)
        
        self.scan_and_fix_templates()
        
        print(f"\n📊 修复完成:")
        print(f"   检查文件: {self.files_checked}")
        print(f"   修复文件: {self.files_fixed}")
        print(f"   总修复数: {self.fixes_applied}")
        
        if self.fixes_applied > 0:
            print(f"\n🎉 已自动修复 {self.fixes_applied} 处语法错误")
            print(f"✅ 所有模板文件现在应该可以正常工作了")
        else:
            print(f"\n✅ 没有发现需要修复的语法错误")
            print(f"🎉 所有模板文件语法正确！")

if __name__ == "__main__":
    fixer = FinalTemplateChecker()
    fixer.run()
