{% extends 'base.html' %}

{% block title %}入库向导{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .wizard-progress {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
    }

    .step {
        flex: 1;
        text-align: center;
        position: relative;
    }

    .step:not(:last-child):after {
        content: '';
        position: absolute;
        top: 25px;
        left: 60%;
        width: 80%;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .step.active:not(:last-child):after,
    .step.completed:not(:last-child):after {
        background-color: #007bff;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        position: relative;
        z-index: 2;
    }

    .step.active .step-icon {
        background-color: #007bff;
        color: white;
    }

    .step.completed .step-icon {
        background-color: #28a745;
        color: white;
    }

    .step-label {
        margin-top: 0.5rem;
        font-weight: 500;
    }

    .step.active .step-label {
        color: #007bff;
    }

    .step.completed .step-label {
        color: #28a745;
    }

    .create-option {
        transition: transform 0.3s;
        cursor: pointer;
    }

    .create-option:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .wizard-content {
        min-height: 400px;
    }

    .item-card {
        margin-bottom: 1rem;
        border-start: 4px solid #6c757d;
    }

    .item-card.needs-inspection {
        border-start-color: #ffc107;
    }

    .hidden-step {
        display: none;
    }

    .batch-settings {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }

    .summary-table th {
        width: 30%;
    }

    .document-preview {
        height: 150px;
        object-fit: cover;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 学校信息显示 -->
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-school"></i> 当前学校：{{ user_area.name }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-1"><strong>默认仓库：</strong>{{ default_warehouse.name }}</p>
                    <p class="mb-1"><strong>仓库地址：</strong>{{ default_warehouse.location or '未设置' }}</p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>可用存储位置：</strong>{{ storage_locations|length }} 个</p>
                    <p class="mb-1"><strong>合作供应商：</strong>{{ suppliers|length }} 个</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col">
            <h2>入库向导</h2>
            <p class="text-muted">按照步骤完成入库操作，提高工作效率</p>
        </div>
    </div>

    <!-- 进度指示器 -->
    <div class="wizard-progress mb-4">
        <div class="step active" id="step1-indicator">
            <div class="step-icon"><i class="fas fa-file-alt"></i></div>
            <div class="step-label">基础信息</div>
        </div>
        <div class="step" id="step2-indicator">
            <div class="step-icon"><i class="fas fa-list"></i></div>
            <div class="step-label">添加食材</div>
        </div>
        <div class="step" id="step3-indicator">
            <div class="step-icon"><i class="fas fa-clipboard-check"></i></div>
            <div class="step-label">检验与文档</div>
        </div>
        <div class="step" id="step4-indicator">
            <div class="step-icon"><i class="fas fa-check-circle"></i></div>
            <div class="step-label">确认提交</div>
        </div>
    </div>

    <!-- 步骤1：基础信息 -->
    <div id="step1" class="wizard-step">
        <!-- 入库方式选择卡片 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100 border-primary create-option" id="fromPurchaseCard">
                    <div class="card-body text-center">
                        <i class="fas fa-file-import fa-4x text-primary mb-3"></i>
                        <h4>从采购计划创建</h4>
                        <p>自动导入采购计划中的食材信息，快速完成入库</p>
                        <button class="btn btn-primary btn-lg mt-3" id="createFromPurchaseBtn">
                            <i class="fas fa-arrow-right"></i> 选择采购计划
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100 border-success create-option" id="manualCreateCard">
                    <div class="card-body text-center">
                        <i class="fas fa-edit fa-4x text-success mb-3"></i>
                        <h4>手动创建入库单</h4>
                        <p>手动填写入库信息，适用于无采购计划的入库场景</p>
                        <button class="btn btn-success btn-lg mt-3" id="createManuallyBtn">
                            <i class="fas fa-arrow-right"></i> 开始创建
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 基础信息表单 -->
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title mb-0">入库单基础信息</h3>
            </div>
            <div class="card-body">
                <form id="stockInBasicForm">
                    <input type="hidden" id="stock_in_id" name="stock_in_id" value="{{ stock_in.id if stock_in else '' }}">
                    <input type="hidden" id="warehouse_id" name="warehouse_id" value="{{ default_warehouse.id }}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_storage_location">默认存储位置 <span class="text-danger">*</span></label>
                                <select class="form-control" id="default_storage_location" name="default_storage_location" required>
                                    <option value="">-- 请选择存储位置 --</option>
                                    {% for location in storage_locations %}
                                    <option value="{{ location.id }}">{{ location.name }} ({{ location.location_code }})</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">选择食材的默认存储位置，可在后续步骤中单独调整</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stock_in_type">入库类型 <span class="text-danger">*</span></label>
                                <select class="form-control" id="stock_in_type" name="stock_in_type" required>
                                    <option value="采购入库" {% if stock_in and stock_in.stock_in_type == '采购入库' %}selected{% endif %}>采购入库</option>
                                    <option value="调拨入库" {% if stock_in and stock_in.stock_in_type == '调拨入库' %}selected{% endif %}>调拨入库</option>
                                    <option value="退货入库" {% if stock_in and stock_in.stock_in_type == '退货入库' %}selected{% endif %}>退货入库</option>
                                </select>
                                <small class="text-muted">选择本次入库的类型</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stock_in_date">入库日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="stock_in_date" name="stock_in_date"
                                       value="{{ stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in and stock_in.stock_in_date else today }}" required>
                                <small class="text-muted">选择实际入库的日期</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notes">备注</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ stock_in.notes if stock_in else '' }}</textarea>
                                <small class="text-muted">可选，添加关于本次入库的备注信息</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card-footer text-end">
                <a href="{{ url_for('stock_in.index') }}" class="btn btn-secondary">取消</a>
                <button type="button" class="btn btn-primary" id="step1NextBtn">
                    下一步：添加食材 <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 步骤2：添加食材 -->
    <div id="step2" class="wizard-step hidden-step">
        <div class="card mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h3 class="card-title mb-0">添加入库食材</h3>
                <div>
                    <button type="button" class="btn btn-success" id="addIngredientBtn">
                        <i class="fas fa-plus"></i> 添加食材
                    </button>
                    <button type="button" class="btn btn-primary" id="importFromPurchaseBtn">
                        <i class="fas fa-file-import"></i> 从采购计划导入
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 批量设置区域 -->
                <div class="batch-settings mb-4">
                    <h5><i class="fas fa-sliders-h"></i> 批量设置</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_storage_location">存储位置</label>
                                <select class="form-control" id="batch_storage_location">
                                    <option value="">-- 请选择 --</option>
                                    {% for location in storage_locations %}
                                    <option value="{{ location.id }}">{{ location.name }} ({{ location.location_code }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_production_date">生产日期</label>
                                <input type="date" class="form-control" id="batch_production_date">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_expiry_date">过期日期</label>
                                <input type="date" class="form-control" id="batch_expiry_date">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_supplier">供应商</label>
                                <select class="form-control" id="batch_supplier">
                                    <option value="">-- 请选择 --</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-primary" id="applyBatchSettingsBtn">
                            <i class="fas fa-check"></i> 应用到所有食材
                        </button>
                    </div>
                </div>

                <!-- 食材列表 -->
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3"><i class="fas fa-exclamation-triangle text-warning"></i> 需要检验的食材</h5>
                        <div id="inspectionItemsContainer">
                            <!-- 需要检验的食材将在这里显示 -->
                            <div class="alert alert-info text-center" id="noInspectionItemsMsg">
                                <i class="fas fa-info-circle"></i> 没有需要检验的食材
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="mb-3"><i class="fas fa-check-circle text-success"></i> 普通食材</h5>
                        <div id="normalItemsContainer">
                            <!-- 普通食材将在这里显示 -->
                            <div class="alert alert-info text-center" id="noNormalItemsMsg">
                                <i class="fas fa-info-circle"></i> 没有普通食材
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 食材卡片模板 -->
                <template id="itemCardTemplate">
                    <div class="card item-card mb-3" data-id="">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 item-name"></h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-danger remove-item-btn">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label>存储位置 <span class="text-danger">*</span></label>
                                        <select class="form-control item-storage-location" required>
                                            <option value="">-- 请选择 --</option>
                                            {% for location in storage_locations %}
                                            <option value="{{ location.id }}">{{ location.name }} ({{ location.location_code }})</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label>批次号 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control item-batch" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label>数量 <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control item-quantity" min="0" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label>单位</label>
                                        <input type="text" class="form-control item-unit" readonly>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label>生产日期 <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control item-production-date" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label>过期日期 <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control item-expiry-date" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label>单价</label>
                                        <input type="number" class="form-control item-price" min="0" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label>供应商</label>
                                        <select class="form-control item-supplier">
                                            <option value="">-- 请选择 --</option>
                                            {% for supplier in suppliers %}
                                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" id="step2PrevBtn">
                    <i class="fas fa-arrow-left"></i> 上一步：基础信息
                </button>
                <button type="button" class="btn btn-primary" id="step2NextBtn">
                    下一步：检验与文档 <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 步骤3：检验与文档 -->
    <div id="step3" class="wizard-step hidden-step">
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h3 class="card-title mb-0">食材检验与文档上传</h3>
            </div>
            <div class="card-body">
                <!-- 检验区域 -->
                <div class="mb-4" id="inspectionSection">
                    <h5 class="mb-3"><i class="fas fa-clipboard-check"></i> 食材检验</h5>
                    <div class="alert alert-info" id="noInspectionNeededMsg" style="display: none;">
                        <i class="fas fa-info-circle"></i> 本次入库的食材不需要进行检验
                    </div>
                    <div id="inspectionItemsList">
                        <!-- 需要检验的食材列表将在这里显示 -->
                    </div>
                </div>

                <!-- 文档上传区域 -->
                <div class="mb-4">
                    <h5 class="mb-3"><i class="fas fa-file-upload"></i> 文档上传</h5>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_type">文档类型 <span class="text-danger">*</span></label>
                                <select class="form-control" id="document_type">
                                    <option value="送货单">送货单</option>
                                    <option value="检验检疫证明">检验检疫证明</option>
                                    <option value="质量检测报告">质量检测报告</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_supplier">相关供应商</label>
                                <select class="form-control" id="document_supplier">
                                    <option value="">-- 请选择 --</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="document_notes">文档备注</label>
                        <textarea class="form-control" id="document_notes" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label>上传文件 <span class="text-danger">*</span></label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="document_file">
                            <label class="custom-file-label" for="document_file">选择文件</label>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-primary" id="uploadDocumentBtn">
                            <i class="fas fa-upload"></i> 上传文档
                        </button>
                    </div>

                    <!-- 已上传文档列表 -->
                    <div class="mt-4">
                        <h6>已上传文档</h6>
                        <div class="row" id="documentsList">
                            <!-- 已上传的文档将在这里显示 -->
                            <div class="col-12 text-center text-muted" id="noDocumentsMsg">
                                <p>暂无上传的文档</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" id="step3PrevBtn">
                    <i class="fas fa-arrow-left"></i> 上一步：添加食材
                </button>
                <button type="button" class="btn btn-primary" id="step3NextBtn">
                    下一步：确认提交 <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 步骤4：确认提交 -->
    <div id="step4" class="wizard-step hidden-step">
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h3 class="card-title mb-0">确认入库信息</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 请仔细核对以下入库信息，确认无误后提交
                </div>

                <!-- 入库单基本信息 -->
                <h5 class="mb-3"><i class="fas fa-file-alt"></i> 基本信息</h5>
                <table class="table table-bordered summary-table">
                    <tbody id="basicInfoSummary">
                        <!-- 基本信息将在这里显示 -->
                    </tbody>
                </table>

                <!-- 食材明细 -->
                <h5 class="mb-3"><i class="fas fa-list"></i> 食材明细</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>食材名称</th>
                                <th>数量</th>
                                <th>单位</th>
                                <th>存储位置</th>
                                <th>批次号</th>
                                <th>生产日期</th>
                                <th>过期日期</th>
                                <th>单价</th>
                                <th>供应商</th>
                            </tr>
                        </thead>
                        <tbody id="itemsSummary">
                            <!-- 食材明细将在这里显示 -->
                        </tbody>
                    </table>
                </div>

                <!-- 检验信息 -->
                <div id="inspectionSummarySection">
                    <h5 class="mb-3"><i class="fas fa-clipboard-check"></i> 检验信息</h5>
                    <div id="inspectionSummary">
                        <!-- 检验信息将在这里显示 -->
                    </div>
                </div>

                <!-- 文档信息 -->
                <h5 class="mb-3"><i class="fas fa-file-upload"></i> 上传文档</h5>
                <div id="documentsSummary">
                    <!-- 文档信息将在这里显示 -->
                </div>

                <!-- 提交按钮 -->
                <div class="text-center mt-4">
                    <button type="button" class="btn btn-success btn-lg" id="submitStockInBtn">
                        <i class="fas fa-check-circle"></i> 确认提交入库单
                    </button>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" id="step4PrevBtn">
                    <i class="fas fa-arrow-left"></i> 上一步：检验与文档
                </button>
                <a href="#" class="btn btn-info" id="previewReportBtn" target="_blank">
                    <i class="fas fa-file-pdf"></i> 预览入库报告
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 采购计划选择模态框 -->
<div class="modal fade" id="purchaseOrderModal" tabindex="-1" role="dialog" aria-labelledby="purchaseOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="purchaseOrderModalLabel">选择采购计划</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>采购单号</th>
                                <th>创建日期</th>
                                <th>供应商</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="purchaseOrdersTable">
                            <!-- 采购计划列表将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>

    <!-- 选择采购订单模态框 -->
    <div class="modal fade" id="selectPurchaseOrderModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-import"></i> 选择采购订单
                    </h5>
                    <button type="button" class="close" data-bs-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        选择已确认的采购订单来创建入库单，系统将自动导入订单中的食材信息
                    </div>

                    {% if pending_purchase_orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>供应商</th>
                                    <th>菜谱信息</th>
                                    <th>订单金额</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in pending_purchase_orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_number }}</strong>
                                        <br>
                                        <small class="text-muted">{{ order.status }}</small>
                                    </td>
                                    <td>{{ order.supplier.name if order.supplier else '自购' }}</td>
                                    <td>
                                        <div class="text-primary">
                                            <i class="fas fa-utensils"></i> {{ order.menu_info.description }}
                                        </div>
                                        {% if order.menu_info.dates %}
                                        <small class="text-muted">
                                            日期：{{ order.menu_info.dates|join(', ') }}
                                        </small>
                                        <br>
                                        {% endif %}
                                        {% if order.menu_info.meals %}
                                        <small class="text-muted">
                                            餐次：{{ order.menu_info.meals|join('、') }}
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.total_amount %}
                                        ¥{{ "%.2f"|format(order.total_amount) }}
                                        {% else %}
                                        <span class="text-muted">未计算</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm select-order-btn"
                                                data-order-id="{{ order.id }}"
                                                data-order-number="{{ order.order_number }}">
                                            <i class="fas fa-check"></i> 选择
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无准备入库的采购订单</h5>
                        <p class="text-muted">当前学校没有状态为"已确认"的采购订单</p>
                        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 去创建采购订单
                        </a>
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 将服务器端数据传递给JavaScript
    const availablePurchaseOrders = {{ available_purchase_orders | tojson }};

    $(document).ready(function() {
        console.log('页面加载完成');
        console.log('可用采购订单数据:', availablePurchaseOrders);

        // 初始化
        let stockInId = $('#stock_in_id').val();
        let currentStep = 1;

        // 从采购计划创建按钮点击事件
        $('#createFromPurchaseBtn').on('click', function() {
            console.log('点击了从采购计划创建按钮');
            // 显示采购订单选择模态框
            $('#selectPurchaseOrderModal').modal('show');
        });

        // 选择采购订单事件（使用事件委托）
        $(document).on('click', '.select-order-btn', function() {
            const orderId = $(this).data('order-id');
            const orderNumber = $(this).data('order-number');

            console.log('选择了采购订单:', orderId, orderNumber);

            // 验证表单是否填写完整
            if (!$('#default_storage_location').val()) {
                alert('请先选择默认存储位置');
                return;
            }

            // 保存基础信息并关联采购订单
            saveBasicInfoWithPurchase(orderId);

            // 关闭模态框
            $('#selectPurchaseOrderModal').modal('hide');
        });

        // 手动创建按钮点击事件
        $('#createManuallyBtn').on('click', function() {
            // 保存基础信息
            saveBasicInfo();
        });

        // 下一步按钮点击事件
        $('#step1NextBtn').on('click', function() {
            saveBasicInfo();
        });

        // 保存基础信息
        function saveBasicInfo() {
            // 验证表单
            if (!$('#stockInBasicForm')[0].checkValidity()) {
                $('#stockInBasicForm')[0].reportValidity();
                return;
            }

            // 收集表单数据
            const formData = {
                warehouse_id: $('#warehouse_id').val(),
                stock_in_type: $('#stock_in_type').val(),
                stock_in_date: $('#stock_in_date').val(),
                notes: $('#notes').val(),
                default_storage_location: $('#default_storage_location').val()
            };

            // 发送请求创建入库单
            $.ajax({
                url: '/stock-in/create',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(response) {
                    if (response.success) {
                        stockInId = response.stock_in_id;
                        // 更新隐藏字段
                        $('#stock_in_id').val(stockInId);
                        // 进入下一步
                        goToStep(2);
                    } else {
                        alert('保存基础信息失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('保存基础信息失败:', error);
                    alert('服务器错误，请稍后重试');
                }
            });
        }

        // 保存基础信息并关联采购订单
        function saveBasicInfoWithPurchase(purchaseOrderId) {
            console.log('开始保存基础信息并关联采购订单:', purchaseOrderId);

            // 验证表单
            if (!$('#stockInBasicForm')[0].checkValidity()) {
                console.log('表单验证失败');
                $('#stockInBasicForm')[0].reportValidity();
                return;
            }

            // 收集表单数据
            const formData = {
                warehouse_id: $('#warehouse_id').val(),
                stock_in_type: $('#stock_in_type').val(),
                stock_in_date: $('#stock_in_date').val(),
                notes: $('#notes').val(),
                default_storage_location: $('#default_storage_location').val(),
                purchase_order_id: purchaseOrderId
            };

            console.log('发送的表单数据:', formData);

            // 显示加载提示
            const loadingBtn = $('<button class="btn btn-primary" disabled><i class="fas fa-spinner fa-spin"></i> 正在创建...</button>');
            $('.select-order-btn').prop('disabled', true);

            // 发送请求
            $.ajax({
                url: '/stock-in/create-from-purchase',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                timeout: 30000, // 30秒超时
                success: function(response) {
                    console.log('服务器响应:', response);
                    $('.select-order-btn').prop('disabled', false);

                    if (response.success) {
                        stockInId = response.stock_in_id;
                        // 更新隐藏字段
                        $('#stock_in_id').val(stockInId);
                        // 显示成功消息
                        alert('已从采购订单创建入库单，正在加载食材信息...');
                        // 进入下一步
                        goToStep(2);
                    } else {
                        alert('从采购订单创建入库单失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        readyState: xhr.readyState
                    });
                    $('.select-order-btn').prop('disabled', false);

                    if (status === 'timeout') {
                        alert('请求超时，请检查网络连接后重试');
                    } else {
                        alert('服务器错误，请稍后重试。错误信息: ' + error);
                    }
                }
            });
        }

        // 切换到指定步骤
        function goToStep(step) {
            // 隐藏所有步骤
            $('.wizard-step').addClass('hidden-step');
            // 显示目标步骤
            $(`#step${step}`).removeClass('hidden-step');

            // 更新进度指示器
            $('.step').removeClass('active completed');
            for (let i = 1; i <= 4; i++) {
                if (i < step) {
                    $(`#step${i}-indicator`).addClass('completed');
                } else if (i === step) {
                    $(`#step${i}-indicator`).addClass('active');
                }
            }

            // 更新当前步骤
            currentStep = step;

            // 如果进入第二步，加载食材列表
            if (step === 2 && stockInId) {
                loadIngredients();
            }
        }

        // 加载食材列表
        function loadIngredients() {
            // 如果没有stockInId，显示提示信息
            if (!stockInId) {
                $('#inspectionItemsContainer').html('<div class="alert alert-info">请先保存基础信息</div>');
                $('#normalItemsContainer').html('<div class="alert alert-info">请先保存基础信息</div>');
                return;
            }

            // 显示加载提示
            $('#inspectionItemsContainer').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> 正在加载食材信息...</div>');
            $('#normalItemsContainer').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> 正在加载食材信息...</div>');

            // 模拟加载食材数据（实际应该从服务器获取）
            setTimeout(function() {
                // 清空容器
                $('#inspectionItemsContainer').html('');
                $('#normalItemsContainer').html('');

                // 显示提示信息
                $('#noInspectionItemsMsg').show();
                $('#noNormalItemsMsg').show();

                // 设置默认日期
                const today = new Date();
                const todayStr = today.toISOString().split('T')[0];

                // 默认过期日期为30天后
                const expiryDate = new Date(today);
                expiryDate.setDate(today.getDate() + 30);
                const expiryDateStr = expiryDate.toISOString().split('T')[0];

                $('#batch_production_date').val(todayStr);
                $('#batch_expiry_date').val(expiryDateStr);

                // 如果有默认存储位置，应用到批量设置
                const defaultStorageLocation = $('#default_storage_location').val();
                if (defaultStorageLocation) {
                    $('#batch_storage_location').val(defaultStorageLocation);
                }
            }, 1000);
        }

        // 从采购计划导入食材按钮点击事件
        $('#importFromPurchaseBtn').on('click', function() {
            console.log('点击了从采购计划导入食材按钮');

            // 直接显示模态框，数据已经在页面加载时准备好了
            $('#purchaseOrderModal').modal('show');

            // 检查是否有可用的采购订单数据
            if (typeof availablePurchaseOrders !== 'undefined' && availablePurchaseOrders.length > 0) {
                console.log('使用服务器端数据，找到', availablePurchaseOrders.length, '个采购订单');
                renderPurchaseOrdersList(availablePurchaseOrders);
            } else {
                console.log('没有可用的采购订单数据');
                showNoPurchaseOrdersMessage();
            }
        });

        // 加载采购计划列表
        function loadPurchaseOrders() {
            console.log('开始加载采购计划列表');

            try {
                // 显示加载提示
                $('#purchaseOrdersTable').html(`
                    <tr>
                        <td colspan="5" class="text-center">
                            <i class="fas fa-spinner fa-spin"></i> 正在加载采购计划...
                        </td>
                    </tr>
                `);

                // 发送AJAX请求获取采购计划列表
                $.ajax({
                    url: '/api/purchase-orders/available',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 10000, // 10秒超时
                    beforeSend: function(xhr) {
                        console.log('发送AJAX请求...');
                    },
                    success: function(response) {
                        console.log('采购计划列表响应:', response);
                        try {
                            if (response && response.success && response.data && response.data.length > 0) {
                                renderPurchaseOrdersList(response.data);
                            } else {
                                showNoPurchaseOrdersMessage();
                            }
                        } catch (e) {
                            console.error('处理响应数据时出错:', e);
                            showErrorMessage('数据处理失败，请重试');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('加载采购计划失败:', {
                            status: status,
                            error: error,
                            responseText: xhr.responseText,
                            readyState: xhr.readyState
                        });

                        if (status === 'timeout') {
                            showErrorMessage('请求超时，请检查网络连接后重试');
                        } else if (status === 'parsererror') {
                            showErrorMessage('数据格式错误，请联系管理员');
                        } else {
                            showErrorMessage('加载采购计划失败，请稍后重试');
                        }
                    },
                    complete: function() {
                        console.log('AJAX请求完成');
                    }
                });
            } catch (e) {
                console.error('loadPurchaseOrders函数执行出错:', e);
                showErrorMessage('系统错误，请刷新页面重试');
            }
        }

        // 渲染采购计划列表
        function renderPurchaseOrdersList(orders) {
            console.log('开始渲染采购计划列表，订单数量:', orders.length);
            let html = '';
            orders.forEach(order => {
                // 处理服务器端数据格式
                const orderNumber = order.order_number;
                const status = order.status;
                const supplierName = order.supplier ? order.supplier.name : '自购';
                const createdAt = order.created_at || order.order_date;

                html += `
                    <tr>
                        <td>
                            <strong>${orderNumber}</strong>
                            <br>
                            <small class="text-muted">${status}</small>
                        </td>
                        <td>
                            <small>${createdAt}</small>
                        </td>
                        <td>
                            ${supplierName}
                        </td>
                        <td>
                            <span class="badge badge-${getStatusBadgeClass(status)}">${status}</span>
                        </td>
                        <td>
                            <button type="button" class="btn btn-primary btn-sm import-purchase-btn"
                                    data-order-id="${order.id}"
                                    data-order-number="${orderNumber}">
                                <i class="fas fa-file-import"></i> 导入
                            </button>
                        </td>
                    </tr>
                `;
            });
            $('#purchaseOrdersTable').html(html);
            console.log('采购计划列表渲染完成');
        }

        // 显示无采购计划消息
        function showNoPurchaseOrdersMessage() {
            $('#purchaseOrdersTable').html(`
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无可用的采购计划</h5>
                        <p class="text-muted">当前学校没有状态为"已确认"且未入库的采购计划</p>
                        <div class="mt-3">
                            <a href="/purchase-order" class="btn btn-primary me-2">
                                <i class="fas fa-plus"></i> 去创建采购计划
                            </a>
                            <button type="button" class="btn btn-outline-secondary reload-purchase-orders-btn">
                                <i class="fas fa-redo"></i> 重新加载
                            </button>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                提示：只显示状态为"已确认"或"准备入库"且未创建入库单的采购订单
                            </small>
                        </div>
                    </td>
                </tr>
            `);
        }

        // 显示错误消息
        function showErrorMessage(message) {
            $('#purchaseOrdersTable').html(`
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5 class="text-warning">加载失败</h5>
                        <p class="text-muted">${message}</p>
                        <button type="button" class="btn btn-outline-primary reload-purchase-orders-btn">
                            <i class="fas fa-redo"></i> 重新加载
                        </button>
                    </td>
                </tr>
            `);
        }

        // 获取状态徽章样式
        function getStatusBadgeClass(status) {
            switch(status) {
                case '已确认': return 'success';
                case '待确认': return 'warning';
                case '已取消': return 'danger';
                case '已完成': return 'info';
                default: return 'secondary';
            }
        }

        // 导入采购计划事件（使用事件委托）
        $(document).on('click', '.import-purchase-btn', function() {
            const orderId = $(this).data('order-id');
            const orderNumber = $(this).data('order-number');

            console.log('选择导入采购计划:', orderId, orderNumber);

            if (confirm(`确定要从采购计划 ${orderNumber} 导入食材吗？`)) {
                importFromPurchaseOrder(orderId);
            }
        });

        // 重新加载采购订单按钮事件（使用事件委托）
        $(document).on('click', '.reload-purchase-orders-btn', function() {
            console.log('点击重新加载采购订单按钮');
            // 由于我们现在使用服务器端数据，重新加载意味着刷新页面
            window.location.reload();
        });

        // 从采购计划导入食材
        function importFromPurchaseOrder(orderId) {
            console.log('开始从采购计划导入食材:', orderId);

            // 显示加载提示
            $('.import-purchase-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 导入中...');

            $.ajax({
                url: `/stock-in/import-from-purchase/${orderId}`,
                type: 'GET',
                dataType: 'json',
                timeout: 30000, // 30秒超时
                success: function(response) {
                    console.log('导入响应:', response);
                    $('.import-purchase-btn').prop('disabled', false).html('<i class="fas fa-file-import"></i> 导入');

                    if (response.success) {
                        alert('食材导入成功！');
                        $('#purchaseOrderModal').modal('hide');
                        // 这里可以添加刷新食材列表的逻辑
                        loadIngredients();
                    } else {
                        alert('导入失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('导入失败:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });
                    $('.import-purchase-btn').prop('disabled', false).html('<i class="fas fa-file-import"></i> 导入');

                    if (status === 'timeout') {
                        alert('导入超时，请检查网络连接后重试');
                    } else {
                        alert('导入失败，请稍后重试。错误信息: ' + error);
                    }
                }
            });
        }

        // 添加食材按钮点击事件
        $('#addIngredientBtn').on('click', function() {
            // 打开食材选择模态框
            // 这里需要实现一个食材选择模态框
            alert('此功能尚未实现，请从采购计划导入食材');
        });

        // 应用批量设置按钮点击事件
        $('#applyBatchSettingsBtn').on('click', function() {
            const storageLocationId = $('#batch_storage_location').val();
            const productionDate = $('#batch_production_date').val();
            const expiryDate = $('#batch_expiry_date').val();
            const supplierId = $('#batch_supplier').val();

            // 应用到所有食材
            if (storageLocationId) {
                $('.item-storage-location').val(storageLocationId);
            }
            if (productionDate) {
                $('.item-production-date').val(productionDate);
            }
            if (expiryDate) {
                $('.item-expiry-date').val(expiryDate);
            }
            if (supplierId) {
                $('.item-supplier').val(supplierId);
            }

            alert('批量设置已应用');
        });

        // 删除食材按钮点击事件（使用事件委托）
        $(document).on('click', '.remove-item-btn', function() {
            const card = $(this).closest('.item-card');
            const itemId = card.data('id');

            if (confirm('确定要删除这个食材吗？')) {
                // 如果是已保存的食材，发送删除请求
                if (itemId) {
                    $.ajax({
                        url: `/stock-in/${stockInId}/remove-item/${itemId}`,
                        type: 'POST',
                        success: function(response) {
                            if (response.success) {
                                card.remove();
                                // 检查是否需要显示提示信息
                                const inspectionItemsCount = $('#inspectionItemsContainer .item-card').length;
                                const normalItemsCount = $('#normalItemsContainer .item-card').length;
                                $('#noInspectionItemsMsg').toggle(inspectionItemsCount === 0);
                                $('#noNormalItemsMsg').toggle(normalItemsCount === 0);
                            } else {
                                alert('删除食材失败: ' + response.message);
                            }
                        },
                        error: function() {
                            alert('服务器错误，请稍后重试');
                        }
                    });
                } else {
                    // 如果是未保存的食材，直接从DOM中移除
                    card.remove();
                }
            }
        });

        // 步骤2的上一步按钮点击事件
        $('#step2PrevBtn').on('click', function() {
            goToStep(1);
        });

        // 步骤2的下一步按钮点击事件
        $('#step2NextBtn').on('click', function() {
            // 简单验证并进入下一步
            if (!stockInId) {
                alert('请先保存基础信息');
                return;
            }

            // 检查是否有食材（如果从采购订单创建，应该已经有食材了）
            const itemCount = $('.item-card').length;
            if (itemCount === 0) {
                // 如果没有食材，提示用户
                if (confirm('当前没有添加任何食材，是否继续到下一步？')) {
                    goToStep(3);
                    loadInspectionItems();
                }
            } else {
                // 有食材，直接进入下一步
                goToStep(3);
                loadInspectionItems();
            }
        });

        // 加载需要检验的食材
        function loadInspectionItems() {
            // 简化检验加载，默认显示没有需要检验的食材
            $('#noInspectionNeededMsg').show();
            $('#inspectionItemsList').hide();
        }

        // 步骤3的上一步按钮点击事件
        $('#step3PrevBtn').on('click', function() {
            goToStep(2);
        });

        // 步骤3的下一步按钮点击事件
        $('#step3NextBtn').on('click', function() {
            // 简化处理，直接进入下一步
            loadSummary();
            goToStep(4);
        });

        // 上传文档按钮点击事件
        $('#uploadDocumentBtn').on('click', function() {
            const documentType = $('#document_type').val();
            const documentFile = $('#document_file')[0].files[0];

            if (!documentType) {
                alert('请选择文档类型');
                return;
            }

            if (!documentFile) {
                alert('请选择要上传的文件');
                return;
            }

            // 简化处理，显示成功消息
            alert('文档上传功能暂未完全实现，但已记录您的操作');

            // 清空表单
            $('#document_notes').val('');
            $('#document_file').val('');
            $('.custom-file-label').text('选择文件');
        });



        // 加载摘要信息
        function loadSummary() {
            // 简化摘要加载，显示基本信息
            if (stockInId) {
                // 填充基本信息
                $('#basicInfoSummary').html(`
                    <tr>
                        <th>入库单ID</th>
                        <td>${stockInId}</td>
                    </tr>
                    <tr>
                        <th>入库类型</th>
                        <td>${$('#stock_in_type').val()}</td>
                    </tr>
                    <tr>
                        <th>入库日期</th>
                        <td>${$('#stock_in_date').val()}</td>
                    </tr>
                    <tr>
                        <th>默认存储位置</th>
                        <td>${$('#default_storage_location option:selected').text()}</td>
                    </tr>
                    <tr>
                        <th>状态</th>
                        <td>待审核</td>
                    </tr>
                    <tr>
                        <th>备注</th>
                        <td>${$('#notes').val() || '无'}</td>
                    </tr>
                `);

                // 清空其他摘要信息
                $('#itemsSummary').html('<tr><td colspan="9" class="text-center text-muted">暂无食材信息</td></tr>');
                $('#inspectionSummarySection').hide();
                $('#documentsSummary').html('<p class="text-muted">暂无上传的文档</p>');
            }
        }

        // 步骤4的上一步按钮点击事件
        $('#step4PrevBtn').on('click', function() {
            goToStep(3);
        });

        // 提交入库单按钮点击事件
        $('#submitStockInBtn').on('click', function() {
            if (confirm('确定要提交入库单吗？提交后将无法修改')) {
                if (stockInId) {
                    alert('入库单创建成功！入库单ID: ' + stockInId);
                    // 跳转到入库单列表页
                    window.location.href = '/stock-in';
                } else {
                    alert('请先完成基础信息的保存');
                }
            }
        });

        // 自定义文件输入框显示文件名
        $(document).on('change', '.custom-file-input', function() {
            const fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').text(fileName);
        });
    });
</script>
{% endblock %}
