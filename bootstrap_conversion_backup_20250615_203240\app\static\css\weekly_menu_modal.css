/**
 * 周菜单计划模态框样式
 * 提供完整的模态框样式，兼容性好
 * 参考示例文件夹中的实现
 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1055;
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal.show {
  display: block;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
}

.modal-open {
  overflow: hidden;
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}

/* 响应式调整 */
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  
  .modal-dialog-scrollable {
    height: calc(100% - 3.5rem);
  }
  
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  
  .modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 800px;
  }
}

@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}

/* 菜单输入框样式 */
.menu-input {
  cursor: pointer;
  background-color: #fff;
  transition: all 0.3s;
}

.menu-input:hover {
  background-color: #f8f9fa;
}

.menu-input.selected {
  background-color: #e8f4e8; /* 浅绿色背景，根据您的记忆要求 */
  border-color: #28a745;
}

/* 食谱分类样式 */
.recipe-categories {
  margin-bottom: 1rem;
}

.recipe-category {
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
  background-color: #f8f9fa;
  transition: all 0.2s;
}

.recipe-category:hover {
  background-color: #e9ecef;
}

.recipe-category.active {
  background-color: #e8f4e8;
  color: #28a745;
  font-weight: 500;
}

/* 食谱卡片样式 */
.recipe-card {
  cursor: pointer;
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
  transition: all 0.2s;
}

.recipe-card:hover {
  border-color: #6c757d;
  background-color: #f8f9fa;
}

.recipe-card.selected {
  border-color: #28a745;
  background-color: #e8f4e8;
}

/* 已选菜品标签样式 */
.selected-dish-tag {
  display: inline-flex;
  align-items: center;
  background-color: #e8f4e8;
  color: #28a745;
  border: 1px solid #28a745;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.selected-dish-tag .remove-btn {
  margin-left: 0.5rem;
  cursor: pointer;
  color: #dc3545;
}

/* 菜品名称样式 */
.dish-name {
  color: #dc3545; /* 红色文本，根据您的记忆要求 */
}

/* 兼容性处理 */
@supports not (display: flex) {
  .modal-header,
  .modal-footer,
  .selected-dish-tag {
    display: block;
  }
  
  .modal-header::after,
  .modal-footer::after {
    content: "";
    display: table;
    clear: both;
  }
  
  .modal-title {
    float: left;
  }
  
  .btn-close {
    float: right;
  }
  
  .modal-footer .btn {
    float: right;
    margin-left: 0.25rem;
  }
}
