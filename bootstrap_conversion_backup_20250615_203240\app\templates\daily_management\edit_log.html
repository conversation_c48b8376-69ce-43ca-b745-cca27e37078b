{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">
        {% if log %}编辑日志{% else %}创建日志{% endif %} - {{ log_date.strftime('%Y年%m月%d日') }}
    </h1>

    <!-- 6个功能模块，一行显示布局 -->
    <div class="row mb-4">
        <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
            <div class="card border-start-primary shadow h-100 py-2">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-check fa-2x text-primary mb-2"></i>
                    <h6 class="fw-bold">检查记录</h6>
                    <p class="text-muted small">食品安全检查，支持二维码扫描上传</p>
                    {% if log %}
                    <a href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}" class="btn btn-primary btn-sm">进入检查</a>
                    {% else %}
                    <span class="text-muted small">保存日志后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
            <div class="card border-start-success shadow h-100 py-2">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h6 class="fw-bold">陪餐记录</h6>
                    <p class="text-muted small">陪餐人员记录，生成陪餐报告</p>
                    {% if log %}
                    <a href="{{ url_for('daily_management.companions', log_id=log.id) }}" class="btn btn-success btn-sm">陪餐管理</a>
                    {% else %}
                    <span class="text-muted small">保存日志后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
            <div class="card border-start-warning shadow h-100 py-2">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x text-warning mb-2"></i>
                    <h6 class="fw-bold">培训记录</h6>
                    <p class="text-muted small">员工培训档案管理</p>
                    {% if log %}
                    <a href="{{ url_for('daily_management.trainings', log_id=log.id) }}" class="btn btn-warning btn-sm">培训管理</a>
                    {% else %}
                    <span class="text-muted small">保存日志后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
            <div class="card border-start-info shadow h-100 py-2">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-info mb-2"></i>
                    <h6 class="fw-bold">特殊事件</h6>
                    <p class="text-muted small">突发事件记录处理</p>
                    {% if log %}
                    <a href="{{ url_for('daily_management.events', log_id=log.id) }}" class="btn btn-info btn-sm">事件记录</a>
                    {% else %}
                    <span class="text-muted small">保存日志后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
            <div class="card border-start-danger shadow h-100 py-2">
                <div class="card-body text-center">
                    <i class="fas fa-bug fa-2x text-danger mb-2"></i>
                    <h6 class="fw-bold">问题记录</h6>
                    <p class="text-muted small">问题发现与整改跟踪</p>
                    {% if log %}
                    <a href="{{ url_for('daily_management.issues', log_id=log.id) }}" class="btn btn-danger btn-sm">问题管理</a>
                    {% else %}
                    <span class="text-muted small">保存日志后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
            <div class="card border-start-secondary shadow h-100 py-2">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-2x text-secondary mb-2"></i>
                    <h6 class="fw-bold">工作日志</h6>
                    <p class="text-muted small">日常工作记录，生成工作报告</p>
                    {% if log %}
                    <a href="{{ url_for('daily_management.print_log', log_id=log.id) }}" class="btn btn-secondary btn-sm">打印日志</a>
                    {% else %}
                    <span class="text-primary small">正在编辑中</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 简单表单 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">日志信息</h6>
        </div>
        <div class="card-body">
            <form method="post" id="logForm" novalidate novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="weather">天气</label>
                            <input type="text" class="form-control" id="weather" name="weather"
                                   value="{{ log.weather if log else '' }}" placeholder="请输入当日天气">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="manager">管理员</label>
                            <input type="text" class="form-control" id="manager" name="manager"
                                   value="{{ log.manager if log else '' }}" placeholder="请输入管理员姓名">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="student_count">学生就餐人数</label>
                            <input type="number" class="form-control" id="student_count" name="student_count"
                                   value="{{ log.student_count if log else 0 }}" min="0">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="teacher_count">教师就餐人数</label>
                            <input type="number" class="form-control" id="teacher_count" name="teacher_count"
                                   value="{{ log.teacher_count if log else 0 }}" min="0">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="other_count">其他就餐人数</label>
                            <input type="number" class="form-control" id="other_count" name="other_count"
                                   value="{{ log.other_count if log else 0 }}" min="0">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="food_waste">食物浪费量(kg)</label>
                            <input type="number" step="0.01" class="form-control" id="food_waste" name="food_waste"
                                   value="{{ log.food_waste if log else 0 }}" min="0">
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>总就餐人数: <span id="total-count">{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}</span> 人</strong>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="breakfast_menu">早餐菜单</label>
                            <textarea class="form-control" id="breakfast_menu" name="breakfast_menu"
                                      rows="3" placeholder="请输入早餐菜单...">{{ log.breakfast_menu if log else (', '.join(menu_data['早餐']) if menu_data and menu_data['早餐'] else '') }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="lunch_menu">午餐菜单</label>
                            <textarea class="form-control" id="lunch_menu" name="lunch_menu"
                                      rows="3" placeholder="请输入午餐菜单...">{{ log.lunch_menu if log else (', '.join(menu_data['午餐']) if menu_data and menu_data['午餐'] else '') }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="dinner_menu">晚餐菜单</label>
                            <textarea class="form-control" id="dinner_menu" name="dinner_menu"
                                      rows="3" placeholder="请输入晚餐菜单...">{{ log.dinner_menu if log else (', '.join(menu_data['晚餐']) if menu_data and menu_data['晚餐'] else '') }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="special_events">特殊事件概述</label>
                    <textarea class="form-control" id="special_events" name="special_events"
                              rows="3" placeholder="请简要描述当日特殊事件...">{{ log.special_events if log else '' }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="operation_summary">运营总结</label>
                    <textarea class="form-control" id="operation_summary" name="operation_summary"
                              rows="4" placeholder="请输入当日运营总结...">{{ log.operation_summary if log else '' }}</textarea>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg me-2">
                        <i class="fas fa-save me-1"></i>保存日志
                    </button>
                    <a href="{{ url_for('daily_management.logs') }}" class="btn btn-secondary btn-lg me-2">
                        <i class="fas fa-list me-1"></i>返回列表
                    </a>
                    <a href="{{ url_for('daily_management.index') }}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-home me-1"></i>返回首页
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 计算总就餐人数
        function calculateTotal() {
            var studentCount = parseInt($('#student_count').val()) || 0;
            var teacherCount = parseInt($('#teacher_count').val()) || 0;
            var otherCount = parseInt($('#other_count').val()) || 0;
            var total = studentCount + teacherCount + otherCount;
            $('#total-count').text(total);
        }

        // 监听就餐人数输入框的变化
        $('#student_count, #teacher_count, #other_count').on('input', calculateTotal);

        // 表单提交时显示加载状态
        $('#logForm').on('submit', function(e) {
            $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
        });
    });
</script>
{% endblock %}
