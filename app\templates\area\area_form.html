{% extends 'base.html' %}
{% from 'components/page_header.html' import simple_page_header %}
{% from 'components/forms.html' import form_field, floating_field, form_card %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<!-- 页面头部 -->
{{ simple_page_header(
  title=title,
  back_url=url_for('area.index'),
  back_text="返回区域列表"
) }}

<!-- 表单内容 -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <form method="post" novalidate>
            {{ form.hidden_tag() }}

            {{ form_card(
              title="区域信息",
              form_content=area_form_content(),
              actions=[
                {'type': 'link', 'url': url_for('area.index'), 'text': '取消', 'class': 'btn-outline-secondary'},
                {'type': 'submit', 'text': '保存', 'icon': 'fas fa-save', 'class': 'btn-primary'}
              ]
            ) }}
        </form>
    </div>
</div>

{% macro area_form_content() %}
<!-- 基本信息 -->
<div class="row g-3">
    <div class="col-md-6">
        {{ floating_field(
          field=form.name,
          placeholder="请输入区域名称",
          required=True
        ) }}
    </div>
    <div class="col-md-6">
        {{ floating_field(
          field=form.code,
          placeholder="请输入区域代码",
          help_text="区域代码必须唯一，建议使用行政区划代码",
          required=True
        ) }}
    </div>
</div>

<!-- 层级信息 -->
<div class="row g-3">
    <div class="col-md-6">
        {{ floating_field(
          field=form.level,
          placeholder="选择区域级别",
          required=True
        ) }}
    </div>
    <div class="col-md-6">
        {{ floating_field(
          field=form.parent_id,
          placeholder="选择上级区域"
        ) }}
    </div>
</div>

<!-- 描述信息 -->
{{ floating_field(
  field=form.description,
  placeholder="请输入区域描述（可选）"
) }}

<!-- 特殊选项 -->
<div class="card bg-light border-0">
    <div class="card-body py-3">
        <div class="form-check">
            {{ form.is_township_school(class="form-check-input") }}
            <label class="form-check-label" for="{{ form.is_township_school.id }}">
                <strong>{{ form.is_township_school.label.text }}</strong>
            </label>
        </div>
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            勾选此选项表示该学校直接属于乡镇级别，乡镇级别用户可以直接管理此学校
        </small>
    </div>
</div>
{% endmacro %}
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    // 表单验证增强
    const form = document.querySelector('form');
    const nameField = document.getElementById('{{ form.name.id }}');
    const codeField = document.getElementById('{{ form.code.id }}');

    // 实时验证区域名称
    if (nameField) {
        nameField.addEventListener('input', function() {
            const value = this.value.trim();
            if (value.length < 2) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }

    // 实时验证区域代码
    if (codeField) {
        codeField.addEventListener('input', function() {
            const value = this.value.trim();
            const codePattern = /^[A-Z0-9]{4,12}$/;
            if (!codePattern.test(value)) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }

    // 表单提交验证
    form.addEventListener('submit', function(e) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault();
            // 滚动到第一个错误字段
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
        }
    });
});
</script>
{% endblock %}
