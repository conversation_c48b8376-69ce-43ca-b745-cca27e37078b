{% extends 'base.html' %}

{% block title %}检查卫生照片{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .header-banner {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .school-logo {
        max-height: 80px;
        margin-right: 15px;
    }

    .platform-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .school-name {
        font-size: 1.2rem;
    }

    .check-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        background-color: white;
        border: none;
    }

    .check-card .card-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        font-weight: bold;
        padding: 20px;
        border: none;
    }

    .check-card .card-body {
        padding: 30px;
    }

    .filter-section {
        background-color: #f8f9fc;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
        border-start: 4px solid #4e73df;
    }

    .photo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .photo-item {
        position: relative;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        background-color: white;
    }

    .photo-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .photo-info {
        padding: 15px;
    }

    .photo-time {
        font-size: 0.9rem;
        color: #858796;
        margin-bottom: 10px;
    }

    .photo-description {
        font-size: 0.95rem;
        color: #5a5c69;
        margin-bottom: 15px;
        min-height: 40px;
    }

    .rating-section {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .rating-stars {
        display: flex;
        margin-right: 10px;
    }

    .rating-star {
        color: #e3e6f0;
        cursor: pointer;
        font-size: 1.2rem;
        margin-right: 5px;
        transition: color 0.2s ease;
    }

    .rating-star.active {
        color: #f6c23e;
    }

    .rating-star:hover {
        color: #f6c23e;
    }

    .comment-input {
        width: 100%;
        padding: 8px;
        border: 1px solid #e3e6f0;
        border-radius: 5px;
        margin-bottom: 10px;
        font-size: 0.9rem;
    }

    .submit-btn {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .submit-btn:disabled {
        background: #858796;
        cursor: not-allowed;
    }

    .success-message {
        display: none;
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 10px;
        text-align: center;
        font-weight: bold;
    }

    .error-message {
        display: none;
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin-top: 10px;
        border: 1px solid #f5c6cb;
        font-size: 14px;
    }

    .period-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
        color: white;
    }

    .period-morning {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .period-noon {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    }

    .period-evening {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    }

    .date-filter {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .date-input {
        padding: 8px;
        border: 1px solid #e3e6f0;
        border-radius: 5px;
        font-size: 0.9rem;
    }

    .filter-btn {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* 移动端优化 */
    @d-flex (max-width: 768px) {
        .check-card .card-body {
            padding: 20px;
        }

        .photo-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }

        .photo-image {
            height: 150px;
        }

        .date-filter {
            flex-direction: column;
            align-items: stretch;
        }

        .date-input {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="header-banner">
    <div class="container">
        <div class="d-flex align-items-center">
            <img src="{{ system_logo }}" alt="Logo" class="school-logo">
            <div>
                <div class="platform-name">校园餐智慧食堂</div>
                <div class="school-name">{{ school.name }}</div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="check-card">
                <div class="card-header">
                    <i class="fas fa-images me-2"></i> 检查卫生照片
                </div>
                <div class="card-body">
                    <!-- 筛选区域 -->
                    <div class="filter-section">
                        <form id="filterForm" class="date-filter">
                            <div class="mb-3 mb-0">
                                <label for="startDate">开始日期</label>
                                <input type="date" id="startDate" class="form-control date-input" 
                                       value="{{ start_date.strftime('%Y-%m-%d') }}">
                            </div>
                            <div class="mb-3 mb-0">
                                <label for="endDate">结束日期</label>
                                <input type="date" id="endDate" class="form-control date-input" 
                                       value="{{ end_date.strftime('%Y-%m-%d') }}">
                            </div>
                            <div class="mb-3 mb-0" style="align-self: flex-end;">
                                <button type="submit" class="btn filter-btn">
                                    <i class="fas fa-filter me-1"></i> 筛选
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 照片网格 -->
                    <div class="photo-grid">
                        {% for photo in photos %}
                        <div class="photo-item" data-photo-id="{{ photo.id }}">
                            <img src="{{ photo.file_path }}" alt="卫生照片" class="photo-image">
                            <div class="period-badge period-{{ photo.inspection_record.inspection_type }}">
                                {{ photo.inspection_record.inspection_type }}
                            </div>
                            <div class="photo-info">
                                <div class="photo-time">
                                    {{ photo.upload_time.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                                <div class="photo-description">
                                    {{ photo.description or '无描述' }}
                                </div>
                                <div class="rating-section">
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                        <i class="fas fa-star rating-star {% if photo.rating and photo.rating >= i %}active{% endif %}" 
                                           data-rating="{{ i }}"></i>
                                        {% endfor %}
                                    </div>
                                    <span class="rating-value">
                                        {% if photo.rating %}
                                        {{ photo.rating }}分
                                        {% else %}
                                        未评分
                                        {% endif %}
                                    </span>
                                </div>
                                <textarea class="comment-input" placeholder="添加评价...">{{ photo.comment or '' }}</textarea>
                                <button class="submit-btn" data-action="safe-submit" data-submit-code="submitRating({{ photo.id }})" style="cursor: pointer;">
                                    <i class="fas fa-check me-1"></i> 提交评分
                                </button>
                                <div class="success-message" id="successMessage{{ photo.id }}">
                                    <i class="fas fa-check-circle me-2"></i> 评分成功
                                </div>
                                <div class="error-message" id="errorMessage{{ photo.id }}">
                                    <i class="fas fa-exclamation-circle me-2"></i> 评分失败
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 筛选表单提交
    document.getElementById('filterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        window.location.href = `/check/{{ school.id }}?start_date=${startDate}&end_date=${endDate}`;
    });

    // 评分星星点击
    document.querySelectorAll('.rating-star').forEach(star => {
        star.addEventListener('click', function() {
            const photoItem = this.closest('.photo-item');
            const stars = photoItem.querySelectorAll('.rating-star');
            const rating = parseInt(this.dataset.rating);
            
            stars.forEach(s => {
                if (parseInt(s.dataset.rating) <= rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });
    });

    // 提交评分
    function submitRating(photoId) {
        const photoItem = document.querySelector(`[data-photo-id="${photoId}"]`);
        const stars = photoItem.querySelectorAll('.rating-star.active');
        const rating = stars.length;
        const comment = photoItem.querySelector('.comment-input').value;
        const successMessage = document.getElementById(`successMessage${photoId}`);
        const errorMessage = document.getElementById(`errorMessage${photoId}`);

        // 隐藏消息
        successMessage.style.display = 'none';
        errorMessage.style.display = 'none';

        // 发送请求
        fetch('/api/v2/photos/rate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                photo_id: photoId,
                rating: rating,
                comment: comment
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                successMessage.style.display = 'block';
                // 更新评分显示
                photoItem.querySelector('.rating-value').textContent = `${rating}分`;
                // 3秒后隐藏成功消息
                setTimeout(() => {
                    successMessage.style.display = 'none';
                }, 3000);
            } else {
                errorMessage.textContent = data.error;
                errorMessage.style.display = 'block';
            }
        })
        .catch(error => {
            errorMessage.textContent = '评分失败，请重试';
            errorMessage.style.display = 'block';
        });
    }
</script>
{% endblock %} 