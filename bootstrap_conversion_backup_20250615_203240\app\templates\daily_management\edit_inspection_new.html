{% extends 'base.html' %}

{% block title %}编辑{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 卡片样式 */
    .inspection-card {
        margin-bottom: 20px;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        box-shadow: 0 0.15rem 1.75rem rgba(58, 59, 69, 0.15);
        transition: all 0.3s ease;
    }
    .inspection-card:hover {
        box-shadow: 0 0.5rem 2rem rgba(58, 59, 69, 0.2);
    }
    .inspection-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 0.75rem 1.25rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .inspection-card .card-body {
        padding: 1.25rem;
    }

    /* 表单样式 */
    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    textarea.form-control {
        min-height: 80px;
    }

    /* 按钮样式 */
    .btn-inspection {
        margin-right: 10px;
    }

    /* 页面标题样式 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    /* 响应式调整 */
    @d-flex (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }
        .page-header .btn-group {
            margin-top: 1rem;
            align-self: flex-end;
        }
    }

    /* 图片上传区域样式 */
    .photo-section {
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 15px;
        margin-bottom: 15px;
    }
    .photo-section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    /* 提示信息样式 */
    .alert-info {
        background-color: #e3f2fd;
        border-color: #bee5eb;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1 class="h3 mb-0 text-gray-800">
            编辑{{ '晨检' if inspection_type == 'morning' else '午检' if inspection_type == 'noon' else '晚检' }} - {{ log.log_date|safe_datetime('%Y-%m-%d') }}
        </h1>
        <div class="btn-group">
            <a href="{{ url_for('daily_management.inspections', log_id=log.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回
            </a>
        </div>
    </div>

    <!-- 保存成功提示 -->
    {% if saved %}
    <div class="alert alert-success alert-dismissible fade show" id="saveAlert">
        <strong>保存成功!</strong> 现在您可以上传图片并进行评分了。
        <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 fw-bold text-primary">检查记录</h6>
            <span class="text-muted small">填写备注后保存，即可上传照片</span>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" id="inspectionForm" novalidate novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <!-- 检查时间和检查人员 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label><i class="fas fa-clock"></i> 检查时间</label>
                            <input type="datetime-local" class="form-control" value="{{ now|safe_datetime('%Y-%m-%dT%H:%M') if now else '' }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label><i class="fas fa-user"></i> 检查人员</label>
                            <input type="text" class="form-control" value="{{ current_user.username }}" readonly>
                        </div>
                    </div>
                </div>

                <!-- 检查项目卡片 -->
                <div class="row">
                    {% for item in inspection_items %}
                    <div class="col-lg-6">
                        <div class="inspection-card" id="item-{{ item|replace(' ', '-')|lower }}">
                            <div class="card-header">
                                <h6 class="m-0 fw-bold text-primary">{{ item }}</h6>
                                <span class="badge {% if item in inspections and inspections[item] %}badge-success{% else %}badge-secondary{% endif %}">
                                    {% if item in inspections and inspections[item] %}已记录{% else %}未记录{% endif %}
                                </span>
                            </div>
                            <div class="card-body">
                                <!-- 备注输入区 -->
                                <div class="mb-3">
                                    <label><i class="fas fa-edit"></i> 备注</label>
                                    <textarea class="form-control item-description" id="description_{{ item|replace(' ', '_') }}" name="description_{{ item }}" rows="3"
                                              placeholder="请输入{{ item }}检查备注..." data-item="{{ item }}">{{ inspections[item].description if item in inspections else '' }}</textarea>
                                </div>

                                <!-- 单独确认按钮 -->
                                <div class="text-end mt-2 mb-2">
                                    <button type="button" class="btn btn-success btn-sm save-item-btn" data-item="{{ item }}" data-item-id="{{ inspections[item].id if item in inspections else 'new' }}">
                                        <i class="fas fa-check"></i> 确定
                                    </button>
                                    <span class="save-status ms-2 d-none text-success">
                                        <i class="fas fa-check-circle"></i> 已保存
                                    </span>
                                </div>

                                <!-- 图片上传区域 -->
                                {% if item in inspections and inspections[item] %}
                                    <div class="photo-section">
                                        <div class="photo-section-header">
                                            <h6 class="m-0 fw-bold text-primary"><i class="fas fa-camera"></i> 照片记录</h6>
                                            <span class="text-muted small">点击星星可评分</span>
                                        </div>
                                        <div class="image-gallery" id="image-gallery-inspection-{{ inspections[item].id }}"
                                             data-reference-type="inspection"
                                             data-reference-id="{{ inspections[item].id }}">
                                            <!-- 已上传图片 -->
                                            {% if inspections[item].photos %}
                                                {% for photo in inspections[item].photos %}
                                                <div class="image-container" data-photo-id="{{ photo.id }}">
                                                    <img src="{{ photo.file_path }}" class="inspection-image" alt="{{ item }}">
                                                    <div class="rating" data-value="{{ photo.rating|default(3) }}">
                                                        <span class="star {% if photo.rating|default(3) >= 1 %}active{% endif %}">★</span>
                                                        <span class="star {% if photo.rating|default(3) >= 2 %}active{% endif %}">★</span>
                                                        <span class="star {% if photo.rating|default(3) >= 3 %}active{% endif %}">★</span>
                                                        <span class="star {% if photo.rating|default(3) >= 4 %}active{% endif %}">★</span>
                                                        <span class="star {% if photo.rating|default(3) >= 5 %}active{% endif %}">★</span>
                                                    </div>
                                                    <div class="image-actions">
                                                        <button type="button" class="btn btn-sm btn-danger delete-photo" data-photo-id="{{ photo.id }}">删除</button>
                                                        <button type="button" class="btn btn-sm btn-secondary rotate-photo">旋转</button>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% endif %}

                                            <!-- 添加图片按钮 -->
                                            <div class="add-image-container">
                                                <button type="button" class="add-image-btn">
                                                    <i class="fas fa-plus"></i>
                                                    <span>添加图片</span>
                                                </button>
                                                <input type="file" class="file-input" style="display: none;" accept="image/*">
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> 请先保存检查记录，然后再上传照片。
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 提交按钮 -->
                <div class="mb-3 text-center mt-4 mb-3">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save"></i> 保存检查记录
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 图片预览模态框 -->
<div class="modal fade" id="imagePreviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">图片预览</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img src="" id="previewImage" alt="预览图片" class="img-fluid">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 加载图片上传器脚本 -->
<script nonce="{{ csp_nonce }}" src="/static/js/image_uploader.js"></script>
<script nonce="{{ csp_nonce }}">
    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，开始初始化');

        // 表单提交处理
        document.getElementById('inspectionForm').addEventListener('submit', function(e) {
            // 检查是否有新的检查项需要先保存以获取ID
            const newGalleries = document.querySelectorAll('.image-gallery[data-reference-id="new"]');
            if (newGalleries.length > 0) {
                // 提示用户先保存检查记录
                alert('请先保存检查记录，然后再上传图片');
            }

            // 显示加载指示器
            showLoadingIndicator();
        });

        // 初始化单独保存按钮
        initSaveItemButtons();

        // 如果是保存后返回的页面，显示提示
        {% if saved %}
        console.log('检测到保存标志，显示保存成功提示');
        // 自动滚动到第一个已保存的检查项
        scrollToFirstSavedItem();
        {% endif %}

        // 添加表单字段变化监听
        addFormChangeListeners();

        // 初始化工具提示
        initTooltips();
    });

    // 页面完全加载后（包括图片）初始化图片上传组件
    // 只在页面完全加载后初始化一次，避免重复初始化
    window.addEventListener('load', function() {
        console.log('页面完全加载完成，初始化图片上传组件');
        initImageUploaders();
    });

    // 初始化所有图片上传组件
    function initImageUploaders() {
        console.log('开始初始化图片上传组件...');

        // 清除现有的图片上传组件实例
        if (window.imageUploaders) {
            window.imageUploaders.forEach(uploader => {
                // 如果有销毁方法，调用它
                if (uploader.destroy) {
                    uploader.destroy();
                }
            });
        }

        // 创建新的图片上传组件实例
        window.imageUploaders = [];

        // 为每个图片库创建唯一的ID，避免冲突
        let galleryCounter = 0;

        document.querySelectorAll('.image-gallery').forEach(gallery => {
            // 确保每个图片库有唯一的ID
            if (!gallery.id) {
                gallery.id = 'image-gallery-' + (++galleryCounter);
            }

            // 只初始化有效的图片库
            if (gallery.dataset.referenceType && gallery.dataset.referenceId && gallery.dataset.referenceId !== 'new') {
                console.log(`初始化图片库: ${gallery.id}, 引用类型: ${gallery.dataset.referenceType}, 引用ID: ${gallery.dataset.referenceId}`);

                try {
                    const uploader = new ImageUploader(gallery, {
                        referenceType: gallery.dataset.referenceType,
                        referenceId: gallery.dataset.referenceId,
                        apiBaseUrl: '/daily-management/image-api'
                    });
                    window.imageUploaders.push(uploader);
                } catch (error) {
                    console.error(`初始化图片上传组件失败: ${error.message}`);
                }
            } else {
                console.log(`跳过图片库: ${gallery.id}, 引用类型或ID无效`);
            }
        });

        console.log('图片上传组件初始化完成，共初始化 ' + (window.imageUploaders ? window.imageUploaders.length : 0) + ' 个组件');
    }

    // 添加表单字段变化监听
    function addFormChangeListeners() {
        // 监听文本区域变化，动态调整高度
        document.querySelectorAll('textarea').forEach(textarea => {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // 初始调整
            textarea.style.height = 'auto';
            textarea.style.height = (textarea.scrollHeight) + 'px';
        });

        // 监听表单字段变化，显示未保存提示
        let formChanged = false;
        const formFields = document.querySelectorAll('#inspectionForm textarea, #inspectionForm input:not([readonly])');

        formFields.forEach(field => {
            field.addEventListener('change', function() {
                formChanged = true;

                // 显示未保存提示
                showUnsavedChangesIndicator();
            });

            field.addEventListener('input', function() {
                formChanged = true;

                // 显示未保存提示
                showUnsavedChangesIndicator();
            });
        });

        // 离开页面前提示
        window.addEventListener('beforeunload', function(e) {
            if (formChanged) {
                const message = '您有未保存的更改，确定要离开吗？';
                e.returnValue = message;
                return message;
            }
        });

        // 提交表单时重置标记
        document.getElementById('inspectionForm').addEventListener('submit', function() {
            formChanged = false;

            // 隐藏未保存提示
            hideUnsavedChangesIndicator();
        });
    }

    // 显示未保存更改指示器
    function showUnsavedChangesIndicator() {
        // 检查是否已存在指示器
        if (document.getElementById('unsavedChangesIndicator')) {
            return;
        }

        // 创建指示器
        const indicator = document.createElement('div');
        indicator.id = 'unsavedChangesIndicator';
        indicator.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        indicator.style.bottom = '20px';
        indicator.style.right = '20px';
        indicator.style.zIndex = '1050';
        indicator.innerHTML = `
            <strong><i class="fas fa-exclamation-triangle"></i> 未保存更改!</strong> 请记得保存您的更改。
            <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        `;

        // 添加到页面
        document.body.appendChild(indicator);
    }

    // 隐藏未保存更改指示器
    function hideUnsavedChangesIndicator() {
        const indicator = document.getElementById('unsavedChangesIndicator');
        if (indicator) {
            $(indicator).alert('close');
        }
    }

    // 显示加载指示器
    function showLoadingIndicator() {
        // 创建加载指示器
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'position-fixed w-100 h-100 d-flex justify-content-center align-items-center';
        loadingIndicator.style.top = '0';
        loadingIndicator.style.left = '0';
        loadingIndicator.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
        loadingIndicator.style.zIndex = '9999';
        loadingIndicator.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在保存，请稍候...</div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(loadingIndicator);
    }

    // 滚动到第一个已保存的检查项
    function scrollToFirstSavedItem() {
        // 获取所有已保存的检查项
        const savedItems = document.querySelectorAll('.badge-success');
        if (savedItems.length > 0) {
            // 获取第一个已保存的检查项的卡片
            const firstSavedItem = savedItems[0].closest('.inspection-card');
            if (firstSavedItem) {
                // 滚动到该卡片
                setTimeout(() => {
                    firstSavedItem.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // 高亮显示该卡片
                    firstSavedItem.classList.add('highlight-card');
                    setTimeout(() => {
                        firstSavedItem.classList.remove('highlight-card');
                    }, 2000);
                }, 500);
            }
        }
    }

    // 初始化工具提示
    function initTooltips() {
        if (typeof $ !== 'undefined' && typeof $.fn.tooltip !== 'undefined') {
            new bootstrap.Tooltip(document.querySelector('[data-bs-toggle="tooltip"]'));
        }
    }

    // 更新图片上传区域
    function updatePhotoSection(card, inspectionId) {
        const photoSection = card.querySelector('.photo-section');
        if (photoSection) {
            // 如果已经有图片上传区域，更新引用ID
            const imageGallery = photoSection.querySelector('.image-gallery');
            if (imageGallery) {
                imageGallery.dataset.referenceId = inspectionId;

                // 重新初始化图片上传组件
                initImageUploaders();
            }
        } else {
            // 如果没有图片上传区域，添加一个
            const photoSectionHTML = `
                <div class="photo-section">
                    <div class="photo-section-header">
                        <h6 class="m-0 fw-bold text-primary"><i class="fas fa-camera"></i> 照片记录</h6>
                        <span class="text-muted small">点击星星可评分</span>
                    </div>
                    <div class="image-gallery" id="image-gallery-inspection-${inspectionId}"
                         data-reference-type="inspection"
                         data-reference-id="${inspectionId}">
                        <!-- 添加图片按钮 -->
                        <div class="add-image-container">
                            <button type="button" class="add-image-btn">
                                <i class="fas fa-plus"></i>
                                <span>添加图片</span>
                            </button>
                            <input type="file" class="file-input" style="display: none;" accept="image/*">
                        </div>
                    </div>
                </div>
            `;

            // 插入到备注区域后面
            const formGroup = card.querySelector('.mb-3');
            formGroup.insertAdjacentHTML('afterend', photoSectionHTML);

            // 移除提示信息
            const alertInfo = card.querySelector('.alert-info');
            if (alertInfo) {
                alertInfo.remove();
            }

            // 重新初始化图片上传组件
            initImageUploaders();
        }
    }

    // 初始化单独保存按钮
    function initSaveItemButtons() {
        document.querySelectorAll('.save-item-btn').forEach(button => {
            button.addEventListener('click', async function() {
                const item = this.dataset.item;
                const itemId = this.dataset.itemId;
                const description = document.getElementById(`description_${item.replace(/ /g, '_')}`).value;
                const saveStatus = this.nextElementSibling;
                const card = this.closest('.inspection-card');
                const statusBadge = card.querySelector('.status-badge');

                // 显示加载状态
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';

                try {
                    // 准备数据
                    const formData = new FormData();
                    formData.append('item', item);
                    formData.append('description', description);
                    formData.append('inspection_id', itemId);
                    formData.append('period', '{{ period }}');

                    // 添加 CSRF 令牌
                    const csrfToken = document.querySelector('meta[name="csrf-token"]');
                    if (csrfToken) {
                        formData.append('csrf_token', csrfToken.getAttribute('content'));
                    }

                    console.log('发送请求数据:', {
                        item: item,
                        description: description,
                        inspection_id: itemId,
                        period: '{{ period }}'
                    });

                    // 发送请求
                    const response = await fetch('/daily-management/inspections/save-item', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': csrfToken ? csrfToken.getAttribute('content') : ''
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();
                    console.log('接收到响应:', result);

                    if (result.success) {
                        // 更新UI
                        saveStatus.classList.remove('d-none');
                        setTimeout(() => {
                            saveStatus.classList.add('d-none');
                        }, 3000);

                        // 更新状态标签
                        statusBadge.textContent = '已记录';
                        statusBadge.classList.remove('badge-warning');
                        statusBadge.classList.add('badge-success');

                        // 更新按钮的itemId属性
                        this.dataset.itemId = result.inspection_id;

                        // 更新图片上传区域并添加视觉反馈
                        updatePhotoSection(card, result.inspection_id);

                        // 添加高亮动画效果
                        card.classList.add('highlight-card');
                        setTimeout(() => {
                            card.classList.remove('highlight-card');
                        }, 2000);

                        // 显示成功消息
                        showToast('成功', `${item}检查记录已保存`, 'success');
                    } else {
                        // 显示错误消息
                        showToast('错误', result.message || '保存失败', 'error');
                        console.error('保存失败:', result.message);
                    }
                } catch (error) {
                    console.error('保存检查项失败:', error);

                    // 改进错误处理
                    if (error.message.includes('403') || error.message.includes('CSRF')) {
                        showToast('错误', 'CSRF验证失败，请刷新页面后重试', 'error');
                    } else if (error.message.includes('401')) {
                        showToast('错误', '登录已过期，请重新登录', 'error');
                        setTimeout(() => {
                            window.location.href = '/login?next=' + encodeURIComponent(window.location.pathname);
                        }, 2000);
                    } else if (error.message.includes('500')) {
                        showToast('错误', '服务器内部错误，请联系管理员', 'error');
                    } else {
                        showToast('错误', '保存失败，请重试: ' + error.message, 'error');
                    }
                } finally {
                    // 恢复按钮状态
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-check"></i> 确定';
                }
            });
        });
    }

    // 显示提示消息
    function showToast(title, message, type = 'info') {
        if (typeof Swal !== 'undefined') {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });

            Toast.fire({
                icon: type,
                title: message
            });
        } else {
            // 备用方案
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            toast.style.top = '20px';
            toast.style.right = '20px';
            toast.style.zIndex = '1060';
            toast.innerHTML = `
                <strong>${title}!</strong> ${message}
                <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (typeof $ !== 'undefined') {
                    $(toast).alert('close');
                } else {
                    toast.remove();
                }
            }, 3000);
        }
    }
</script>

<style nonce="{{ csp_nonce }}">
    /* 高亮卡片动画 */
    @keyframes highlight {
        0% { box-shadow: 0 0 0 0 rgba(78, 115, 223, 0.5); }
        70% { box-shadow: 0 0 0 10px rgba(78, 115, 223, 0); }
        100% { box-shadow: 0 0 0 0 rgba(78, 115, 223, 0); }
    }

    .highlight-card {
        animation: highlight 2s ease-in-out;
    }
</style>
{% endblock %}
