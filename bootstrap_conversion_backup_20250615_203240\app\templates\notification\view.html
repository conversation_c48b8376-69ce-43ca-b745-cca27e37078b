{% extends 'base.html' %}

{% block title %}{{ notification.title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>通知详情</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('notification.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回通知列表
            </a>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    {% if notification.level == 2 %}
                        <span class="badge badge-danger">紧急</span>
                    {% elif notification.level == 1 %}
                        <span class="badge badge-warning">重要</span>
                    {% endif %}
                    {{ notification.title }}
                </h5>
                <small>{{ notification.formatted_created_time }}</small>
            </div>
        </div>
        <div class="card-body">
            <div class="notification-meta mb-3">
                <div><strong>通知类型：</strong> {{ get_notification_type_name(notification.notification_type) }}</div>
                <div><strong>状态：</strong> {% if notification.is_read %}已读{% else %}未读{% endif %}</div>
            </div>
            
            <div class="notification-content mb-4">
                {{  notification.content|safe  }}
            </div>
            
            {% if notification.reference_id and notification.reference_type %}
            <div class="notification-reference">
                <div class="alert alert-info">
                    <p><i class="fas fa-link"></i> 此通知与以下内容相关：</p>
                    {% if notification.reference_type == 'health_certificate' %}
                        <a href="{{ url_for('employee.view_health_certificate', id=notification.reference_id) }}" class="btn btn-info btn-sm">
                            查看相关健康证
                        </a>
                    {% elif notification.reference_type == 'menu' %}
                        <a href="{{ url_for('menu.view', id=notification.reference_id) }}" class="btn btn-info btn-sm">
                            查看相关食谱
                        </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-end">
                {% if not notification.is_read %}
                <a href="{{ url_for('notification.mark_read', id=notification.id) }}" class="btn btn-secondary me-2">
                    <i class="fas fa-check"></i> 标为已读
                </a>
                {% endif %}
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    <i class="fas fa-trash"></i> 删除通知
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条通知吗？</p>
                <p><strong>{{ notification.title }}</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('notification.delete', id=notification.id) }}" method="POST"><button type="submit" class="btn btn-danger">确认删除</button>
                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 辅助函数
    function get_notification_type_name(type_code) {
        const type_names = {
            'system': '系统通知',
            'health_cert': '健康证提醒',
            'menu': '食谱通知',
            'purchase': '采购通知',
            'inspection': '检查通知',
            'task': '任务通知'
        };
        return type_names[type_code] || type_code;
    }
</script>
{% endblock %}
