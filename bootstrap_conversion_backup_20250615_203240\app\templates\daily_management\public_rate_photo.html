{% extends 'base_public.html' %}

{% block title %}评价照片{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    body {
        background-color: #f8f9fc;
        font-family: '<PERSON>uni<PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Helvetica Neue', Arial, sans-serif;
    }

    .header-banner {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .school-logo {
        max-height: 80px;
        margin-right: 15px;
    }

    .platform-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .school-name {
        font-size: 1.2rem;
    }

    .photo-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        background-color: white;
        border: none;
    }

    .photo-card .card-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        font-weight: bold;
        padding: 20px;
        border: none;
    }

    .photo-card .card-body {
        padding: 30px;
    }

    .photo-container {
        position: relative;
        width: 100%;
        margin-bottom: 20px;
    }

    .photo-preview {
        width: 100%;
        height: auto;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .rating-container {
        text-align: center;
        margin: 20px 0;
    }

    .star-rating {
        display: inline-flex;
        flex-direction: row-reverse;
        font-size: 2rem;
    }

    .star-rating input {
        display: none;
    }

    .star-rating label {
        color: #ddd;
        cursor: pointer;
        padding: 0 5px;
        transition: color 0.2s ease-in-out;
    }

    .star-rating label:hover,
    .star-rating label:hover ~ label,
    .star-rating input:checked ~ label {
        color: #ffd700;
    }

    .rating-text {
        margin-top: 10px;
        font-size: 1.1rem;
        color: #5a5c69;
    }

    .submit-btn {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 10px;
        font-weight: bold;
        font-size: 1.1rem;
        margin-top: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .submit-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .submit-btn:disabled {
        background: #858796;
        cursor: not-allowed;
    }

    .success-message {
        display: none;
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .error-message {
        display: none;
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 10px;
        margin-top: 20px;
        border: 1px solid #f5c6cb;
        font-size: 14px;
        position: relative;
    }

    .error-message.show {
        display: block;
        animation: slideIn 0.3s ease-out;
    }

    .error-message i {
        margin-right: 8px;
        color: #dc3545;
    }

    .error-message .close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background: none;
        border: none;
        color: #721c24;
        cursor: pointer;
        padding: 0;
        font-size: 16px;
    }

    .error-message .error-details {
        margin-top: 10px;
        font-size: 12px;
        color: #856404;
    }

    @keyframes slideIn {
        from {
            transform: translateY(-10px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        justify-content: center;
        align-items: center;
    }

    .loading-content {
        background-color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .loading-spinner {
        font-size: 2rem;
        color: #4e73df;
        margin-bottom: 15px;
    }

    .footer {
        background-color: #f8f9fc;
        padding: 20px 0;
        margin-top: 30px;
        text-align: center;
        color: #5a5c69;
        font-size: 0.9rem;
    }

    /* 移动端优化 */
    @d-flex (max-width: 768px) {
        .photo-card .card-body {
            padding: 20px;
        }

        .star-rating {
            font-size: 1.8rem;
        }

        .rating-text {
            font-size: 1rem;
        }

        .error-message {
            padding: 12px;
            font-size: 13px;
            margin: 15px 0;
        }

        .error-message .error-details {
            font-size: 11px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="header-banner">
    <div class="container">
        <div class="d-flex align-items-center">
            <img src="{{ system_logo }}" alt="Logo" class="school-logo">
            <div>
                <div class="platform-name">校园餐智慧食堂</div>
                <div class="school-name">{{ school.name }}</div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="photo-card">
                <div class="card-header">
                    <i class="fas fa-star me-2"></i> 评价照片
                </div>
                <div class="card-body">
                    <!-- 照片信息 -->
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            照片信息
                        </h5>
                        <p class="mb-2">
                            <strong>学校：</strong>{{ school.name }}
                        </p>
                        <p class="mb-2">
                            <strong>日期：</strong>{{ photo.upload_time.strftime('%Y年%m月%d日') }}
                        </p>
                        <p class="mb-2">
                            <strong>说明：</strong>{{ photo.description or '无' }}
                        </p>
                    </div>

                    <!-- 照片预览 -->
                    <div class="photo-container">
                        <img src="{{ photo.file_path }}" class="photo-preview" alt="待评价照片">
                    </div>

                    <!-- 评分区域 -->
                    <div class="rating-container">
                        <div class="star-rating">
                            <input type="radio" id="star5" name="rating" value="5">
                            <label for="star5"><i class="fas fa-star"></i></label>
                            <input type="radio" id="star4" name="rating" value="4">
                            <label for="star4"><i class="fas fa-star"></i></label>
                            <input type="radio" id="star3" name="rating" value="3">
                            <label for="star3"><i class="fas fa-star"></i></label>
                            <input type="radio" id="star2" name="rating" value="2">
                            <label for="star2"><i class="fas fa-star"></i></label>
                            <input type="radio" id="star1" name="rating" value="1">
                            <label for="star1"><i class="fas fa-star"></i></label>
                        </div>
                        <div class="rating-text" id="ratingText">请选择评分</div>
                    </div>

                    <!-- 提交按钮 -->
                    <button type="button" class="btn submit-btn w-100" id="submitBtn" disabled>
                        <i class="fas fa-check me-2"></i> 提交评分
                    </button>

                    <!-- 消息提示 -->
                    <div class="success-message" id="successMessage">
                        <i class="fas fa-check-circle me-2"></i>
                        评分成功！感谢您的参与。
                    </div>

                    <!-- 错误消息容器 -->
                    <div class="error-message" id="errorMessage">
                        <button type="button" class="close-btn" data-onclick="hideError()">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="error-content">
                            <i class="fas fa-exclamation-circle"></i>
                            <span id="errorText"></span>
                        </div>
                        <div class="error-details" id="errorDetails"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <h5>正在提交评分...</h5>
        <p>请稍候，正在处理您的评分</p>
    </div>
</div>

<div class="footer">
    <div class="container">
        <p>© {{ now.year }} 校园餐智慧食堂 - 版权所有</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // DOM 元素
    const ratingInputs = document.querySelectorAll('input[name="rating"]');
    const ratingText = document.getElementById('ratingText');
    const submitBtn = document.getElementById('submitBtn');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');

    // 评分文本映射
    const ratingTexts = {
        1: '很差',
        2: '较差',
        3: '一般',
        4: '较好',
        5: '很好'
    };

    // 监听评分选择
    ratingInputs.forEach(input => {
        input.addEventListener('change', function() {
            const rating = this.value;
            ratingText.textContent = ratingTexts[rating];
            submitBtn.disabled = false;
        });
    });

    // 提交评分
    submitBtn.addEventListener('click', function() {
        const selectedRating = document.querySelector('input[name="rating"]:checked');
        if (!selectedRating) {
            showError('请选择评分');
            return;
        }

        const rating = parseInt(selectedRating.value);
        submitRating(rating);
    });

    // 提交评分到服务器
    function submitRating(rating) {
        // 显示加载遮罩
        loadingOverlay.style.display = 'flex';

        // 发送请求
        fetch('/api/v2/photos/public/rate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                photo_id: {{ photo.id }},
                rating: rating
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(JSON.stringify({
                        message: data.error,
                        type: data.error_type
                    }));
                });
            }
            return response.json();
        })
        .then(data => {
            loadingOverlay.style.display = 'none';
            if (data.success) {
                showSuccess();
            } else {
                showError(data.error, data.error_type);
            }
        })
        .catch(error => {
            loadingOverlay.style.display = 'none';
            try {
                const errorData = JSON.parse(error.message);
                showError(errorData.message, errorData.type);
            } catch (e) {
                showError('评分失败，请重试', 'system_error');
            }
        });
    }

    // 显示成功消息
    function showSuccess() {
        hideMessages();
        successMessage.style.display = 'block';
        submitBtn.disabled = true;
        ratingInputs.forEach(input => input.disabled = true);

        // 滚动到成功消息
        successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // 显示错误消息
    function showError(message, errorType) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        const errorDetails = document.getElementById('errorDetails');
        
        // 设置错误消息
        errorText.textContent = message;
        
        // 根据错误类型设置详细信息
        let details = '';
        switch(errorType) {
            case 'invalid_rating':
                details = '评分必须是1-5之间的整数';
                break;
            case 'photo_not_found':
                details = '照片不存在或已被删除';
                break;
            case 'network_error':
                details = '请检查网络连接后重试';
                break;
            case 'system_error':
                details = '系统暂时无法处理请求，请稍后重试';
                break;
            default:
                details = '请重试或联系管理员';
        }
        errorDetails.textContent = details;
        
        // 显示错误消息
        errorDiv.classList.add('show');
        
        // 5秒后自动隐藏
        setTimeout(() => {
            hideError();
        }, 5000);
    }

    function hideError() {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.classList.remove('show');
    }

    // 隐藏所有消息
    function hideMessages() {
        successMessage.style.display = 'none';
        hideError();
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否已经评分
        if ({{ photo.rating }}) {
            const rating = {{ photo.rating }};
            document.querySelector(`input[name="rating"][value="${rating}"]`).checked = true;
            ratingText.textContent = ratingTexts[rating];
            submitBtn.disabled = true;
            ratingInputs.forEach(input => input.disabled = true);
        }
    });
</script>
{% endblock %} 
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>