/**
 * 文件上传功能修复脚本
 * 解决文件选择对话框不弹出的问题
 */

(function() {
    'use strict';

    /**
     * 修复文件上传功能
     */
    function fixFileUpload() {
        // 查找所有文件输入元素
        const fileInputs = document.querySelectorAll('input[type="file"]');

        fileInputs.forEach(function(fileInput) {
            // 跳过已经处理过的元素
            if (fileInput.dataset.uploadFixed) {
                return;
            }

            // 标记为已处理
            fileInput.dataset.uploadFixed = 'true';

            // 获取相关元素
            const customFileContainer = fileInput.closest('.custom-file');
            const customFileLabel = customFileContainer ? customFileContainer.querySelector('.custom-file-label') : null;

            if (customFileContainer && customFileLabel) {
                // 修复Bootstrap自定义文件上传
                fixBootstrapFileUpload(fileInput, customFileContainer, customFileLabel);
            } else {
                // 修复普通文件上传
                fixStandardFileUpload(fileInput);
            }
        });
    }

    /**
     * 修复Bootstrap自定义文件上传
     */
    function fixBootstrapFileUpload(fileInput, container, label) {
        // 检查是否已经修复过，避免重复处理
        if (container.dataset.uploadFixed) {
            return;
        }

        // 标记为已修复
        container.dataset.uploadFixed = 'true';

        // 设置样式确保可点击
        fileInput.style.position = 'absolute';
        fileInput.style.opacity = '0';
        fileInput.style.width = '100%';
        fileInput.style.height = '100%';
        fileInput.style.cursor = 'pointer';
        fileInput.style.zIndex = '1';

        // 设置容器样式
        container.style.position = 'relative';
        container.style.cursor = 'pointer';

        // 设置标签样式
        label.style.cursor = 'pointer';

        // 移除可能存在的旧事件监听器（使用命名空间）
        if (container._clickHandler) {
            container.removeEventListener('click', container._clickHandler);
        }
        if (fileInput._changeHandler) {
            fileInput.removeEventListener('change', fileInput._changeHandler);
        }

        // 创建新的事件处理函数
        container._clickHandler = function(e) {
            // 只有当点击的是容器本身或标签时才触发
            if (e.target === container || e.target === label || label.contains(e.target)) {
                e.preventDefault();
                e.stopPropagation();
                fileInput.click();
            }
        };

        fileInput._changeHandler = function() {
            let fileName = '';
            if (this.files && this.files.length > 0) {
                fileName = this.files[0].name;
            } else if (this.value) {
                fileName = this.value.split('\\').pop();
            }

            if (fileName) {
                label.textContent = fileName;
                label.classList.add('file-selected');
            } else {
                label.textContent = '选择文件';
                label.classList.remove('file-selected');
            }
        };

        // 添加事件监听器
        container.addEventListener('click', container._clickHandler);
        fileInput.addEventListener('change', fileInput._changeHandler);
    }

    /**
     * 修复标准文件上传
     */
    function fixStandardFileUpload(fileInput) {
        // 确保文件输入可见且可点击
        fileInput.style.cursor = 'pointer';

        // 如果有父级按钮或标签，确保点击时触发文件选择
        const parentButton = fileInput.closest('button, .btn, label');
        if (parentButton && parentButton.tagName !== 'LABEL') {
            // 检查是否已经添加过事件监听器
            if (!parentButton.dataset.uploadFixed) {
                parentButton.dataset.uploadFixed = 'true';

                // 移除可能存在的旧事件监听器
                if (parentButton._clickHandler) {
                    parentButton.removeEventListener('click', parentButton._clickHandler);
                }

                // 创建新的事件处理函数
                parentButton._clickHandler = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    fileInput.click();
                };

                // 添加事件监听器
                parentButton.addEventListener('click', parentButton._clickHandler);
            }
        }
    }

    /**
     * 添加通用样式
     * 注意：为了符合CSP策略，我们不再动态添加样式
     */
    function addStyles() {
        // 检查是否已添加样式
        if (document.getElementById('file-upload-fix-styles')) {
            return;
        }

        // 为了符合CSP策略，我们不再动态添加样式
        // 相关样式应该在CSS文件中定义
        console.log('文件上传修复：样式应该通过CSS文件加载，而不是动态添加');

        // 创建一个标记元素，表示样式已经"添加"
        const marker = document.createElement('div');
        marker.id = 'file-upload-fix-styles';
        marker.style.display = 'none';
        document.head.appendChild(marker);
    }

    /**
     * 初始化修复
     */
    function init() {
        // 添加样式
        addStyles();

        // 修复现有的文件上传
        fixFileUpload();

        // 监听DOM变化，修复动态添加的文件上传
        const observer = new MutationObserver(function(mutations) {
            let shouldFix = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.matches('input[type="file"]') ||
                                node.querySelector('input[type="file"]')) {
                                shouldFix = true;
                            }
                        }
                    });
                }
            });

            if (shouldFix) {
                setTimeout(fixFileUpload, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 导出到全局，以便其他脚本调用
    window.FileUploadFix = {
        fix: fixFileUpload,
        init: init
    };

})();
