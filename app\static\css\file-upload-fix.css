/* 文件上传修复样式 - 符合CSP策略 */

.form-control {
    position: relative !important;
    cursor: pointer !important;
}

.form-control-input {
    position: absolute !important;
    opacity: 0 !important;
    width: 100% !important;
    height: 100% !important;
    cursor: pointer !important;
    z-index: 1 !important;
}

.form-control-label {
    cursor: pointer !important;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control-label:hover {
    border-color: #80bdff;
}

.form-control-label.file-selected {
    color: #495057;
    background-color: #e9ecef;
}

/* 确保文件输入在所有情况下都可点击 */
input[type="file"] {
    cursor: pointer !important;
}

/* 修复可能的z-index问题 */
.form-control-input:focus {
    box-shadow: none;
    border-color: transparent;
}

/* Bootstrap 4 自定义文件上传样式增强 */
.form-control-input {
    position: relative !important;
    z-index: 2 !important;
    width: 100% !important;
    height: calc(1.5em + 0.75rem + 2px) !important;
    margin: 0 !important;
    opacity: 0 !important;
    cursor: pointer !important;
}

.form-control-label {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    left: 0 !important;
    z-index: 1 !important;
    height: calc(1.5em + 0.75rem + 2px) !important;
    padding: 0.375rem 0.75rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    color: #495057 !important;
    background-color: #fff !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;
    cursor: pointer !important;
}

.form-control-label::after {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 3 !important;
    display: block !important;
    height: calc(1.5em + 0.75rem) !important;
    padding: 0.375rem 0.75rem !important;
    line-height: 1.5 !important;
    color: #495057 !important;
    content: "浏览" !important;
    background-color: #e9ecef !important;
    border-start: inherit !important;
    border-radius: 0 0.25rem 0.25rem 0 !important;
    cursor: pointer !important;
}

.form-control:hover .form-control-label {
    border-color: #80bdff !important;
}

.form-control-input:focus ~ .form-control-label {
    border-color: #80bdff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.form-control-input:disabled ~ .form-control-label,
.form-control-input[readonly] ~ .form-control-label {
    background-color: #e9ecef !important;
    cursor: not-allowed !important;
}

.form-control-input:disabled ~ .form-control-label::after,
.form-control-input[readonly] ~ .form-control-label::after {
    background-color: #e9ecef !important;
    cursor: not-allowed !important;
}

/* 确保文件输入框可点击 */
.form-control {
    position: relative !important;
    display: inline-block !important;
    width: 100% !important;
    height: calc(1.5em + 0.75rem + 2px) !important;
    margin-bottom: 0 !important;
}

/* 修复可能的层级问题 */
.form-control-input:focus {
    box-shadow: none !important;
    border-color: transparent;
}
