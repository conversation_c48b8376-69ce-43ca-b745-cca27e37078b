{"conversion_time": "2025-06-15T20:32:40.081964", "files_processed": 425, "files_modified": 220, "total_replacements": 2344, "replacements_by_type": {"class_d-md-none": 2, "class_d-lg-none": 1, "class_shadow-sm": 1, "class_shadow": 1, "class_shadow-lg": 1, "class_overflow-auto": 1, "class_overflow-hidden": 1, "class_badge-success": 3, "class_badge-warning": 1, "class_btn-outline-primary": 2, "class_btn-outline-success": 1, "structure_1": 1, "class_badge-primary": 2, "class_btn-outline-secondary": 1, "class_btn-outline-danger": 1, "class_btn-outline-warning": 1, "class_custom-file": 2, "class_btn-outline-info": 1, "js_'hidden_bs_modal'": 1, "class_custom-checkbox": 2, "class_custom-control": 1, "class_badge-secondary": 1, "class_badge-danger": 2, "class_badge-info": 3, "js__modal('show')": 1, "js__modal('hide')": 1, "class_cleanup": 1, "class_badge-dark": 1, "class_badge-light": 1, "structure_4": 4, "class_badge-pill": 1, "class_custom-radio": 2, "structure_3": 3, "class_form-control-file": 1, "structure_2": 2, "class_custom-switch": 2, "class_nav-tabs": 4, "class_btn-default": 1, "class_form-control-plaintext": 1, "class_modal-dialog-centered": 1, "class_btn-outline-dark": 3, "js_'show_bs_modal'": 1, "class_border-top-0": 1, "class_border-bottom-0": 1, "class_btn-outline-light": 4, "class_fixed-top": 1, "class_nav-pills": 1, "structure_8": 8, "structure_5": 5, "structure_6": 6, "js_'shown_bs_modal'": 1, "class_text-right": 1, "class_border-left": 1, "class_border-right": 1, "class_mr-(d+)": 10, "class_ml-(d+)": 1, "class_text-left": 1, "attr_dismiss=": 2, "attr_toggle=": 1, "class_font-weight-bold": 2, "js_'hide_bs_modal'": 1, "class_pl-(d+)": 2, "class_no-gutters": 2, "class_sr-only": 1}, "files_with_changes": [{"file": "app\\templates\\mobile-demo.html", "replacements": 6, "details": {"classes": 5, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\mobile_test.html", "replacements": 6, "details": {"classes": 5, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\theme_demo.html", "replacements": 9, "details": {"classes": 9, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\_formhelpers.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\data_management.html", "replacements": 33, "details": {"classes": 29, "attributes": 0, "structures": 1, "javascript": 2, "cleanup": 1}}, {"file": "app\\templates\\admin\\permission_help.html", "replacements": 45, "details": {"classes": 45, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\roles.html", "replacements": 9, "details": {"classes": 5, "attributes": 0, "structures": 4, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\role_permissions.html", "replacements": 13, "details": {"classes": 12, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\admin\\role_templates.html", "replacements": 20, "details": {"classes": 20, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\users.html", "replacements": 26, "details": {"classes": 19, "attributes": 0, "structures": 3, "javascript": 3, "cleanup": 1}}, {"file": "app\\templates\\admin\\user_permissions.html", "replacements": 5, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\admin\\video_management.html", "replacements": 10, "details": {"classes": 7, "attributes": 0, "structures": 3, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\view_role.html", "replacements": 26, "details": {"classes": 25, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\view_user.html", "replacements": 58, "details": {"classes": 57, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\guide_management\\demo_data.html", "replacements": 17, "details": {"classes": 15, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\admin\\guide_management\\scenarios.html", "replacements": 9, "details": {"classes": 3, "attributes": 0, "structures": 2, "javascript": 4, "cleanup": 0}}, {"file": "app\\templates\\admin\\guide_management\\users.html", "replacements": 7, "details": {"classes": 7, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\super_delete\\index.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\system\\module_visibility.html", "replacements": 10, "details": {"classes": 10, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\system\\monitor.html", "replacements": 1, "details": {"classes": 0, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\admin\\video_guide\\create.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\admin\\video_guide\\edit.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\area\\index.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\area\\view_area.html", "replacements": 40, "details": {"classes": 37, "attributes": 0, "structures": 3, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\batch_flow\\form.html", "replacements": 6, "details": {"classes": 6, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\consultation\\detail.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\consultation\\list.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\consumption_plan\\create.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\consumption_plan\\create_from_weekly.html", "replacements": 22, "details": {"classes": 21, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\consumption_plan\\edit.html", "replacements": 16, "details": {"classes": 15, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\consumption_plan\\index.html", "replacements": 20, "details": {"classes": 20, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\consumption_plan\\new.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\consumption_plan\\select_weekly_menu.html", "replacements": 5, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\consumption_plan\\super_editor.html", "replacements": 32, "details": {"classes": 30, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\daily_management\\add_companion.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\add_companion_iframe.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\add_event.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\add_inspection_photo.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\add_issue.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\add_training.html", "replacements": 11, "details": {"classes": 11, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\check_photos.html", "replacements": 6, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\daily_management\\companions.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\edit_event.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\edit_inspection.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\edit_inspection_new.html", "replacements": 17, "details": {"classes": 12, "attributes": 0, "structures": 4, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\daily_management\\edit_inspection_photo.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\edit_issue.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\edit_training.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\events.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\fixed_inspection_qrcode.html", "replacements": 10, "details": {"classes": 8, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\inspections.html", "replacements": 28, "details": {"classes": 28, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\inspections_new.html", "replacements": 16, "details": {"classes": 16, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\inspection_display.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\inspection_form.html", "replacements": 10, "details": {"classes": 9, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\daily_management\\inspection_templates.html", "replacements": 22, "details": {"classes": 16, "attributes": 0, "structures": 2, "javascript": 3, "cleanup": 1}}, {"file": "app\\templates\\daily_management\\issues.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\optimized_dashboard.html", "replacements": 42, "details": {"classes": 39, "attributes": 0, "structures": 1, "javascript": 2, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\public_rate_inspection_photos.html", "replacements": 6, "details": {"classes": 4, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\simplified_dashboard.html", "replacements": 6, "details": {"classes": 6, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\simplified_inspection.html", "replacements": 10, "details": {"classes": 3, "attributes": 0, "structures": 4, "javascript": 2, "cleanup": 1}}, {"file": "app\\templates\\daily_management\\trainings.html", "replacements": 4, "details": {"classes": 2, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\view_companion.html", "replacements": 3, "details": {"classes": 2, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\view_inspection_photo.html", "replacements": 3, "details": {"classes": 2, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\view_training.html", "replacements": 3, "details": {"classes": 1, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\components\\data_visualization.html", "replacements": 8, "details": {"classes": 4, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\components\\enhanced_image_uploader.html", "replacements": 3, "details": {"classes": 2, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\components\\navigation.html", "replacements": 14, "details": {"classes": 14, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\daily_management\\widgets\\image_widget.html", "replacements": 2, "details": {"classes": 0, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\data_repair\\index.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\data_repair\\tools.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\employee\\daily_health_check.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\employee\\employee_form.html", "replacements": 5, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\employee\\health_certificates.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\employee\\health_certificate_form.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\employee\\index.html", "replacements": 32, "details": {"classes": 31, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\employee\\medical_examination_form.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\employee\\training_record_form.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\employee\\view_employee.html", "replacements": 22, "details": {"classes": 22, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\financial\\accounting_subjects\\form.html", "replacements": 6, "details": {"classes": 3, "attributes": 0, "structures": 1, "javascript": 2, "cleanup": 0}}, {"file": "app\\templates\\financial\\accounting_subjects\\text_tree.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\financial\\payables\\pending_stock_ins.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\financial\\reports\\trial_balance.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\financial\\reports\\voucher_summary.html", "replacements": 10, "details": {"classes": 10, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\financial\\vouchers\\edit.html", "replacements": 1, "details": {"classes": 0, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\financial\\vouchers\\edit_professional.html", "replacements": 8, "details": {"classes": 7, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\financial\\vouchers\\index.html", "replacements": 8, "details": {"classes": 2, "attributes": 0, "structures": 3, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\financial\\vouchers\\text_view.html", "replacements": 6, "details": {"classes": 5, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\financial\\vouchers\\view.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\food_sample\\create.html", "replacements": 7, "details": {"classes": 7, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\food_sample\\index.html", "replacements": 3, "details": {"classes": 2, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\food_sample\\view.html", "replacements": 4, "details": {"classes": 3, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\food_trace\\index.html", "replacements": 8, "details": {"classes": 8, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\food_trace\\qr_scan.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\guide\\scenario_selection.html", "replacements": 23, "details": {"classes": 20, "attributes": 0, "structures": 2, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\guide\\step_modal.html", "replacements": 17, "details": {"classes": 16, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\includes\\modal.html", "replacements": 3, "details": {"classes": 0, "attributes": 0, "structures": 3, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\ingredient\\categories.html", "replacements": 14, "details": {"classes": 9, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 1}}, {"file": "app\\templates\\ingredient\\form.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\ingredient\\index_category.html", "replacements": 13, "details": {"classes": 9, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\ingredient\\turnover.html", "replacements": 16, "details": {"classes": 16, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\ingredient\\view.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\inspection\\direct_index.html", "replacements": 3, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\inspection\\edit.html", "replacements": 13, "details": {"classes": 11, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\inspection\\simplified_index.html", "replacements": 6, "details": {"classes": 6, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\inspection\\view.html", "replacements": 7, "details": {"classes": 5, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\inventory\\detail.html", "replacements": 20, "details": {"classes": 20, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\inventory\\expiry.html", "replacements": 6, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\inventory\\index.html", "replacements": 28, "details": {"classes": 27, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\inventory\\ingredient.html", "replacements": 11, "details": {"classes": 11, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\inventory\\statistics.html", "replacements": 41, "details": {"classes": 36, "attributes": 0, "structures": 2, "javascript": 2, "cleanup": 1}}, {"file": "app\\templates\\inventory_alert\\batch_create_requisition.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\inventory_alert\\check.html", "replacements": 14, "details": {"classes": 11, "attributes": 0, "structures": 2, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\inventory_alert\\create_requisition.html", "replacements": 7, "details": {"classes": 7, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\inventory_alert\\index.html", "replacements": 6, "details": {"classes": 5, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\inventory_alert\\view.html", "replacements": 7, "details": {"classes": 6, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\macros\\progress_steps.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\macros\\purchase_order_status.html", "replacements": 25, "details": {"classes": 24, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\main\\canteen_dashboard.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\main\\canteen_dashboard_new.html", "replacements": 27, "details": {"classes": 24, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 1}}, {"file": "app\\templates\\main\\food_samples.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\main\\index.html", "replacements": 48, "details": {"classes": 45, "attributes": 0, "structures": 1, "javascript": 2, "cleanup": 0}}, {"file": "app\\templates\\main\\purchase_orders.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\main\\recipes.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\main\\suppliers.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\material_batch\\form.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\material_batch\\index.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\material_batch\\view.html", "replacements": 8, "details": {"classes": 8, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\notification\\index.html", "replacements": 10, "details": {"classes": 8, "attributes": 0, "structures": 2, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\notification\\view.html", "replacements": 3, "details": {"classes": 2, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\adjust_products.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\approve.html", "replacements": 10, "details": {"classes": 10, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\confirm.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\create.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\index.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\select_ingredients.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\set_attributes.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\product_batch\\view.html", "replacements": 9, "details": {"classes": 9, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\purchase_order\\create_form.html", "replacements": 24, "details": {"classes": 20, "attributes": 0, "structures": 2, "javascript": 2, "cleanup": 0}}, {"file": "app\\templates\\purchase_order\\create_from_menu.html", "replacements": 19, "details": {"classes": 16, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 1}}, {"file": "app\\templates\\purchase_order\\index.html", "replacements": 61, "details": {"classes": 40, "attributes": 0, "structures": 8, "javascript": 12, "cleanup": 1}}, {"file": "app\\templates\\purchase_order\\view.html", "replacements": 39, "details": {"classes": 25, "attributes": 0, "structures": 5, "javascript": 8, "cleanup": 1}}, {"file": "app\\templates\\recipe\\categories.html", "replacements": 4, "details": {"classes": 0, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\recipe\\create.html", "replacements": 2, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\recipe\\favorites.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\recipe\\form.html", "replacements": 7, "details": {"classes": 7, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\recipe\\form_new.html", "replacements": 7, "details": {"classes": 7, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\recipe\\form_simplified.html", "replacements": 9, "details": {"classes": 6, "attributes": 0, "structures": 1, "javascript": 2, "cleanup": 0}}, {"file": "app\\templates\\recipe\\index.html", "replacements": 25, "details": {"classes": 21, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\recipe\\view.html", "replacements": 10, "details": {"classes": 7, "attributes": 0, "structures": 1, "javascript": 2, "cleanup": 0}}, {"file": "app\\templates\\school_admin\\users.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\school_admin\\user_form.html", "replacements": 1, "details": {"classes": 0, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\security\\dashboard.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\batch_editor.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\batch_editor_simplified.html", "replacements": 19, "details": {"classes": 16, "attributes": 0, "structures": 2, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\stock_in\\batch_editor_simplified_scripts.html", "replacements": 3, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\batch_editor_step1.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\batch_editor_step2.html", "replacements": 9, "details": {"classes": 8, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\stock_in\\by_ingredient.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\create_from_purchase_order.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\edit.html", "replacements": 33, "details": {"classes": 20, "attributes": 0, "structures": 5, "javascript": 7, "cleanup": 1}}, {"file": "app\\templates\\stock_in\\form.html", "replacements": 32, "details": {"classes": 30, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\index.html", "replacements": 24, "details": {"classes": 23, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\stock_in\\trace.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_in\\view.html", "replacements": 37, "details": {"classes": 28, "attributes": 0, "structures": 4, "javascript": 4, "cleanup": 1}}, {"file": "app\\templates\\stock_in\\wizard.html", "replacements": 15, "details": {"classes": 9, "attributes": 0, "structures": 2, "javascript": 4, "cleanup": 0}}, {"file": "app\\templates\\stock_in_detail\\view.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_out\\edit.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_out\\form.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_out\\index.html", "replacements": 14, "details": {"classes": 13, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\stock_out\\view.html", "replacements": 18, "details": {"classes": 18, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\stock_out_item\\detail.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\storage_location\\form.html", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\storage_location\\view.html", "replacements": 9, "details": {"classes": 8, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\supplier\\category_index.html", "replacements": 6, "details": {"classes": 2, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\supplier\\certificate_form.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\supplier\\certificate_index.html", "replacements": 7, "details": {"classes": 3, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\supplier\\certificate_view.html", "replacements": 9, "details": {"classes": 3, "attributes": 0, "structures": 2, "javascript": 4, "cleanup": 0}}, {"file": "app\\templates\\supplier\\form.html", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\supplier\\index.html", "replacements": 15, "details": {"classes": 11, "attributes": 0, "structures": 1, "javascript": 3, "cleanup": 0}}, {"file": "app\\templates\\supplier\\product_form.html", "replacements": 6, "details": {"classes": 6, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\supplier\\product_index.html", "replacements": 26, "details": {"classes": 9, "attributes": 0, "structures": 3, "javascript": 14, "cleanup": 0}}, {"file": "app\\templates\\supplier\\product_view.html", "replacements": 17, "details": {"classes": 5, "attributes": 0, "structures": 3, "javascript": 9, "cleanup": 0}}, {"file": "app\\templates\\supplier\\school_index.html", "replacements": 15, "details": {"classes": 3, "attributes": 0, "structures": 3, "javascript": 9, "cleanup": 0}}, {"file": "app\\templates\\supplier\\view.html", "replacements": 12, "details": {"classes": 12, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\system_fix\\permission_audit.html", "replacements": 10, "details": {"classes": 10, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\system_fix\\permission_migration.html", "replacements": 10, "details": {"classes": 10, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\traceability\\batch_trace.html", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\traceability\\ingredient_trace.html", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\trace_document\\upload.html", "replacements": 13, "details": {"classes": 13, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\warehouse\\view.html", "replacements": 8, "details": {"classes": 7, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\warehouse_new\\index.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\warehouse_new\\view.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\weekly_menu\\plan.html", "replacements": 17, "details": {"classes": 9, "attributes": 0, "structures": 6, "javascript": 1, "cleanup": 1}}, {"file": "app\\templates\\weekly_menu\\plan_time_aware.html", "replacements": 5, "details": {"classes": 3, "attributes": 0, "structures": 1, "javascript": 0, "cleanup": 1}}, {"file": "app\\templates\\weekly_menu\\plan_v2.html", "replacements": 18, "details": {"classes": 15, "attributes": 0, "structures": 1, "javascript": 1, "cleanup": 1}}, {"file": "app\\templates\\weekly_menu\\view.html", "replacements": 7, "details": {"classes": 5, "attributes": 0, "structures": 2, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\index.html", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\templates\\weekly_menu\\weekly_menu(new)\\view.html", "replacements": 14, "details": {"classes": 10, "attributes": 0, "structures": 3, "javascript": 0, "cleanup": 1}}, {"file": "app\\static\\css\\color-contrast-fix.css", "replacements": 7, "details": {"classes": 7, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\dashboard-optimization.css", "replacements": 28, "details": {"classes": 28, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\file-upload-fix.css", "replacements": 28, "details": {"classes": 28, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\homepage.css", "replacements": 17, "details": {"classes": 17, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\mobile-optimization.css", "replacements": 41, "details": {"classes": 41, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\process_navigation.css", "replacements": 8, "details": {"classes": 8, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\sidebar-layout.css", "replacements": 5, "details": {"classes": 5, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\table-optimization.css", "replacements": 6, "details": {"classes": 6, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\theme-colors.css", "replacements": 91, "details": {"classes": 91, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\css\\weekly_menu_modal.css", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\categorized-ingredient-select.js", "replacements": 1, "details": {"classes": 1, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\file-upload-fix.js", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\image_uploader.js", "replacements": 4, "details": {"classes": 0, "attributes": 2, "structures": 1, "javascript": 1, "cleanup": 0}}, {"file": "app\\static\\js\\main.js", "replacements": 4, "details": {"classes": 3, "attributes": 1, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\menu-switcher.js", "replacements": 2, "details": {"classes": 1, "attributes": 1, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\mobile-table-cards.js", "replacements": 3, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\progress-steps.js", "replacements": 2, "details": {"classes": 2, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\quick_fix_recursion.js", "replacements": 4, "details": {"classes": 4, "attributes": 0, "structures": 0, "javascript": 0, "cleanup": 0}}, {"file": "app\\static\\js\\stock-in-detail-enhancement.js", "replacements": 5, "details": {"classes": 3, "attributes": 0, "structures": 0, "javascript": 2, "cleanup": 0}}, {"file": "app\\static\\js\\user_guide.js", "replacements": 36, "details": {"classes": 24, "attributes": 2, "structures": 2, "javascript": 8, "cleanup": 0}}, {"file": "app\\static\\js\\weekly_menu_modal.js", "replacements": 11, "details": {"classes": 3, "attributes": 2, "structures": 2, "javascript": 4, "cleanup": 0}}, {"file": "app\\static\\js\\weekly_menu_v2.js", "replacements": 11, "details": {"classes": 3, "attributes": 2, "structures": 2, "javascript": 4, "cleanup": 0}}], "errors": []}