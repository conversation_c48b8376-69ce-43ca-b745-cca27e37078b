<!-- 场景选择模态框 -->
<div class="modal fade" id="scenarioSelectionModal" tabindex="-1" role="dialog" aria-labelledby="scenarioSelectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="scenarioSelectionModalLabel">
                    <i class="fas fa-school me-2"></i>选择您的学校类型
                </h5>
                <button type="button" class="close text-white" data-bs-dismiss="modal" aria-label="关闭">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <p class="text-muted">为了提供更贴合您学校实际情况的引导内容，请选择您的学校类型：</p>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card scenario-card h-100" data-scenario="primary">
                            <div class="card-body text-center">
                                <i class="fas fa-child fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">小学</h5>
                                <p class="card-text small text-muted">
                                    重点关注营养搭配和食品安全，适合小学生的特殊需求
                                </p>
                                <div class="scenario-features">
                                    <span class="badge badge-primary me-1">营养搭配</span>
                                    <span class="badge badge-primary me-1">食品安全</span>
                                    <span class="badge badge-primary">陪餐管理</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card scenario-card h-100" data-scenario="middle">
                            <div class="card-body text-center">
                                <i class="fas fa-user-graduate fa-3x text-info mb-3"></i>
                                <h5 class="card-title">中学</h5>
                                <p class="card-text small text-muted">
                                    重点关注成本控制和效率管理，适合中学生的用餐需求
                                </p>
                                <div class="scenario-features">
                                    <span class="badge badge-info me-1">成本控制</span>
                                    <span class="badge badge-info me-1">效率管理</span>
                                    <span class="badge badge-info">营养均衡</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card scenario-card h-100" data-scenario="high">
                            <div class="card-body text-center">
                                <i class="fas fa-graduation-cap fa-3x text-success mb-3"></i>
                                <h5 class="card-title">高中</h5>
                                <p class="card-text small text-muted">
                                    重点关注快速供餐和营养补充，适合高中生的学习需求
                                </p>
                                <div class="scenario-features">
                                    <span class="badge badge-success me-1">快速供餐</span>
                                    <span class="badge badge-success me-1">营养补充</span>
                                    <span class="badge badge-success">时间管理</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card scenario-card h-100" data-scenario="vocational">
                            <div class="card-body text-center">
                                <i class="fas fa-tools fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">职业学校</h5>
                                <p class="card-text small text-muted">
                                    重点关注成本效益和实用管理，适合职业学校的特点
                                </p>
                                <div class="scenario-features">
                                    <span class="badge badge-warning me-1">成本效益</span>
                                    <span class="badge badge-warning me-1">实用管理</span>
                                    <span class="badge badge-warning">灵活配置</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card scenario-card h-100" data-scenario="university">
                            <div class="card-body text-center">
                                <i class="fas fa-university fa-3x text-danger mb-3"></i>
                                <h5 class="card-title">大学</h5>
                                <p class="card-text small text-muted">
                                    重点关注规模化管理和数据分析，适合大学的复杂需求
                                </p>
                                <div class="scenario-features">
                                    <span class="badge badge-danger me-1">规模化管理</span>
                                    <span class="badge badge-danger me-1">数据分析</span>
                                    <span class="badge badge-danger">质量控制</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card scenario-card h-100" data-scenario="rural">
                            <div class="card-body text-center">
                                <i class="fas fa-mountain fa-3x text-secondary mb-3"></i>
                                <h5 class="card-title">乡村学校</h5>
                                <p class="card-text small text-muted">
                                    重点关注简化流程和基础功能，适合资源有限的乡村学校
                                </p>
                                <div class="scenario-features">
                                    <span class="badge badge-secondary me-1">简化流程</span>
                                    <span class="badge badge-secondary me-1">基础功能</span>
                                    <span class="badge badge-secondary">成本控制</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>提示：</strong>选择学校类型后，系统会为您定制专属的引导内容和操作建议。
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="confirmScenarioBtn" disabled>
                    <i class="fas fa-check me-1"></i>确认选择
                </button>
                <button type="button" class="btn btn-outline-primary" id="autoDetectBtn">
                    <i class="fas fa-magic me-1"></i>自动检测
                </button>
            </div>
        </div>
    </div>
</div>

<style nonce="{{ csp_nonce }}">
.scenario-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.scenario-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.scenario-card.selected {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.scenario-features {
    margin-top: 10px;
}

.scenario-features .badge {
    font-size: 0.75em;
}
</style>

<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    let selectedScenario = null;
    
    // 场景卡片点击事件
    $('.scenario-card').click(function() {
        $('.scenario-card').removeClass('selected');
        $(this).addClass('selected');
        selectedScenario = $(this).data('scenario');
        $('#confirmScenarioBtn').prop('disabled', false);
    });
    
    // 确认选择按钮
    $('#confirmScenarioBtn').click(function() {
        if (selectedScenario) {
            startGuideWithScenario(selectedScenario);
        }
    });
    
    // 自动检测按钮
    $('#autoDetectBtn').click(function() {
        autoDetectSchoolType();
    });
    
    function startGuideWithScenario(schoolType) {
        // 开始场景化引导
        $.ajax({
            url: '/api/guide/start',
            type: 'POST',
            data: {
                school_type: schoolType
            },
            success: function(data) {
                if (data.success) {
                    $('#scenarioSelectionModal').modal('hide');
                    
                    // 显示场景化欢迎消息
                    showScenarioWelcome(schoolType);
                    
                    // 开始引导
                    if (typeof userGuide !== 'undefined') {
                        userGuide.schoolType = schoolType;
                        userGuide.showStep('welcome');
                    }
                } else {
                    alert('启动引导失败：' + data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('启动引导失败:', error);
                alert('启动引导失败，请稍后重试');
            }
        });
    }
    
    function autoDetectSchoolType() {
        // 获取学校名称（可以从用户信息或页面中获取）
        const schoolName = '{{ current_user.area.name if current_user.area else "" }}';
        
        $.ajax({
            url: '/api/guide/detect-school-type',
            type: 'POST',
            data: {
                school_name: schoolName
            },
            success: function(data) {
                if (data.success) {
                    const detectedType = data.detected_type;
                    
                    // 高亮检测到的类型
                    $('.scenario-card').removeClass('selected');
                    $(`.scenario-card[data-scenario="${detectedType}"]`).addClass('selected');
                    selectedScenario = detectedType;
                    $('#confirmScenarioBtn').prop('disabled', false);
                    
                    // 显示检测结果
                    alert(`根据您的学校信息，系统检测到您的学校类型为：${getScenarioName(detectedType)}\n\n如果检测结果正确，请点击"确认选择"开始引导。`);
                } else {
                    alert('自动检测失败：' + data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('自动检测失败:', error);
                alert('自动检测失败，请手动选择学校类型');
            }
        });
    }
    
    function getScenarioName(scenario) {
        const names = {
            'primary': '小学',
            'middle': '中学',
            'high': '高中',
            'vocational': '职业学校',
            'university': '大学',
            'rural': '乡村学校'
        };
        return names[scenario] || '未知类型';
    }
    
    function showScenarioWelcome(schoolType) {
        // 获取场景化欢迎消息
        $.ajax({
            url: `/api/guide/scenario/${schoolType}`,
            type: 'GET',
            success: function(data) {
                if (data.success && data.scenario_guide.welcome_message) {
                    // 显示定制化欢迎消息
                    const welcomeHtml = `
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <h5><i class="fas fa-star me-2"></i>专属引导已启动</h5>
                            <p>${data.scenario_guide.welcome_message}</p>
                            <button type="button" class="close" data-bs-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    `;
                    
                    // 在页面顶部显示欢迎消息
                    $('body').prepend(welcomeHtml);
                    
                    // 3秒后自动隐藏
                    setTimeout(() => {
                        $('.alert-success').fadeOut();
                    }, 3000);
                }
            }
        });
    }
});
</script>
