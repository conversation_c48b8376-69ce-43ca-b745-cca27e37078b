{% extends 'base.html' %}

{% block title %}特殊事件详情{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        margin: -5px;
    }
    .photo-item {
        width: 200px;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }
    .photo-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }
    .photo-caption {
        padding: 8px;
        background-color: #f8f9fc;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">特殊事件详情</h1>
        <a href="{{ url_for('daily_management.events', log_id=event.daily_log_id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">事件信息</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>事件类型：</strong>{{ event.event_type }}</p>
                    <p><strong>事件时间：</strong>{{ event.event_time }}</p>
                    <p><strong>参与人员：</strong>{{ event.participants }}</p>
                    <p><strong>创建时间：</strong>{{ event.created_at }}</p>
                    <p><strong>关联日志：</strong><a href="{{ url_for('daily_management.edit_log', date_str=event.daily_log.log_date) }}">{{ event.daily_log.log_date }}</a></p>
                </div>
                <div class="col-md-6">
                    <p><strong>事件描述：</strong></p>
                    <p>{{ event.description }}</p>
                    <p><strong>处理措施：</strong></p>
                    <p>{{ event.handling_measures }}</p>
                    <p><strong>事件总结：</strong></p>
                    <p>{{ event.event_summary }}</p>
                </div>
            </div>

            {% if photos %}
            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="fw-bold">相关照片：</h6>
                </div>
                {% for photo in photos %}
                <div class="col-md-4 mb-3">
                    <img src="{{ url_for('static', filename=photo.file_path) }}" class="img-fluid" alt="事件照片">
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
