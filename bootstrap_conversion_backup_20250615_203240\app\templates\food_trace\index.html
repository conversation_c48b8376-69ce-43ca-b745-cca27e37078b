{% extends 'base.html' %}

{% block title %}食材溯源{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 12px 20px;">
                    <h5 class="card-title mb-0" style="font-size: 16px; font-weight: 500;">食材溯源</h5>
                </div>
                <div class="card-body">
                    <!-- 查询表单 -->
                    <form method="get" action="{{ url_for('food_trace.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>区域</label>
                                    <select name="area_id" class="form-control">
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>日期</label>
                                    <input type="date" name="date" class="form-control" value="{{ trace_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>餐次</label>
                                    <select name="meal_type" class="form-control">
                                        <option value="早餐" {% if meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                        <option value="午餐" {% if meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                        <option value="晚餐" {% if meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">查询</button>
                                        <a href="{{ url_for('food_trace.print_samples', area_id=area_id, date=trace_date, meal_type=meal_type) }}" class="btn btn-secondary" target="_blank">
                                            <i class="fas fa-print"></i> 打印留样记录
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    {% if error_message %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
                    </div>
                    {% endif %}

                    {% if no_recipes_message %}
                    <div class="alert alert-warning" style="border-start: 4px solid #ffc107;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-calendar-times" style="font-size: 24px; color: #856404; margin-right: 15px;"></i>
                            <div>
                                <h5 style="color: #856404; margin-bottom: 8px; font-size: 16px;">
                                    <strong>{{ no_recipes_message }}</strong>
                                </h5>
                                <p style="margin-bottom: 0; font-size: 14px; color: #856404;">
                                    当前查询的日期和餐次没有安排菜品，无法进行食材溯源分析。<br>
                                    请检查：
                                </p>
                                <ul style="margin-top: 8px; margin-bottom: 0; font-size: 14px; color: #856404;">
                                    <li>是否已发布该日期餐次的周菜单</li>
                                    <li>是否已创建该日期餐次的菜单计划</li>
                                    <li>菜单状态是否为"已发布"或"已执行"</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if trace_data and has_recipes %}
                    <!-- 食材一致性分析 -->
                    {% if trace_data.ingredient_analysis %}
                    <div class="card mb-4">
                        <div class="card-header" style="background: linear-gradient(135deg, {% if trace_data.ingredient_analysis.has_discrepancy %}#dc3545{% else %}#28a745{% endif %} 0%, {% if trace_data.ingredient_analysis.has_discrepancy %}#c82333{% else %}#20c997{% endif %} 100%); color: white; padding: 12px 20px;">
                            <h5 class="mb-0" style="font-size: 16px; font-weight: 500;">
                                <i class="fas {% if trace_data.ingredient_analysis.has_discrepancy %}fa-exclamation-triangle{% else %}fa-check-circle{% endif %}"></i>
                                食材一致性分析
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <p><strong>匹配率：</strong>
                                        <span style="color: {% if trace_data.ingredient_analysis.match_rate >= 80 %}#28a745{% elif trace_data.ingredient_analysis.match_rate >= 60 %}#ffc107{% else %}#dc3545{% endif %}; font-size: 16px; font-weight: bold;">
                                            {{ trace_data.ingredient_analysis.match_rate }}%
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>匹配食材：</strong> <span style="color: #28a745; font-weight: bold;">{{ trace_data.ingredient_analysis.matched_ingredients|length }}</span></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>缺失食材：</strong> <span style="color: #dc3545; font-weight: bold;">{{ trace_data.ingredient_analysis.missing_ingredients|length }}</span></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>多余食材：</strong> <span style="color: #ffc107; font-weight: bold;">{{ trace_data.ingredient_analysis.extra_ingredients|length }}</span></p>
                                </div>
                            </div>

                            {% if trace_data.ingredient_analysis.has_discrepancy %}
                            <div class="alert alert-warning mt-3" style="border-start: 4px solid #ffc107;">
                                <h6 style="color: #856404; font-size: 15px;"><i class="fas fa-exclamation-triangle"></i> 发现食材差异</h6>
                                <p style="font-size: 14px; margin-bottom: 8px;">出库食材与食谱所需食材存在不一致，请核实以下情况：</p>

                                {% if trace_data.ingredient_analysis.missing_ingredients %}
                                <div class="mb-2">
                                    <strong style="color: #dc3545;">缺失食材：</strong>
                                    {% for ingredient in trace_data.ingredient_analysis.missing_ingredients %}
                                        <span class="badge badge-danger" style="font-size: 12px; margin-right: 4px;">{{ ingredient }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}

                                {% if trace_data.ingredient_analysis.extra_ingredients %}
                                <div class="mb-2">
                                    <strong style="color: #ffc107;">多余食材：</strong>
                                    {% for ingredient in trace_data.ingredient_analysis.extra_ingredients %}
                                        <span class="badge badge-warning" style="font-size: 12px; margin-right: 4px;">{{ ingredient }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="alert alert-success mt-3" style="border-start: 4px solid #28a745;">
                                <h6 style="color: #155724; font-size: 15px;"><i class="fas fa-check-circle"></i> 食材一致性良好</h6>
                                <p style="font-size: 14px; margin-bottom: 0;">出库食材与食谱所需食材基本一致，溯源数据可靠。</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- 溯源概览 -->
                    <div class="card mb-4">
                        <div class="card-header" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 12px 20px;">
                            <h5 class="mb-0" style="font-size: 16px; font-weight: 500;">溯源概览</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <p><strong>日期：</strong> {{ trace_date }}</p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>餐次：</strong> {{ meal_type }}</p>
                                </div>
                                <div class="col-md-3">
                                    {% if trace_data.consumption_plan %}
                                    <p><strong>用餐人数：</strong> {{ trace_data.consumption_plan.diners_count|default('-') }}</p>
                                    {% endif %}
                                </div>
                                <div class="col-md-3">
                                    {% if trace_data.stock_out %}
                                    <p><strong>出库单号：</strong> {{ trace_data.stock_out.stock_out_number }}</p>
                                    {% endif %}
                                </div>
                            </div>

                            {% if trace_data.recipes %}
                            <h6 class="mt-3" style="font-size: 15px; color: #495057;">菜品列表</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped" style="font-size: 14px;">
                                    <thead style="background-color: #f8f9fa;">
                                        <tr>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">菜品名称</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">分类</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">主要食材</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">来源</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for recipe in trace_data.recipes %}
                                        <tr>
                                            <td style="padding: 12px; font-size: 15px; font-weight: 500;">{{ recipe.name }}</td>
                                            <td style="padding: 12px;">{{ recipe.category|default('-') }}</td>
                                            <td style="padding: 12px;">
                                                {% if recipe.main_ingredients %}
                                                    {% for ingredient in recipe.main_ingredients %}
                                                        <span class="badge badge-secondary" style="font-size: 12px; margin-right: 4px;">{{ ingredient }}</span>
                                                    {% endfor %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td style="padding: 12px;">
                                                {% if recipe.source == 'weekly_menu' %}
                                                    <span class="badge badge-success" style="font-size: 12px;">周菜单</span>
                                                {% elif recipe.source == 'menu_plan' %}
                                                    <span class="badge badge-info" style="font-size: 12px;">日菜单</span>
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if trace_data.batches %}
                    <!-- 批次溯源信息 -->
                    <div class="card mb-4">
                        <div class="card-header" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 12px 20px;">
                            <h5 class="mb-0" style="font-size: 16px; font-weight: 500;">批次溯源信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped" style="font-size: 14px;">
                                    <thead style="background-color: #f8f9fa;">
                                        <tr>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">食材名称</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">批次号</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">出库数量</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">过期日期</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">供应商</th>
                                            <th style="font-size: 14px; font-weight: 500; padding: 10px;">联系方式</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for batch in trace_data.batches %}
                                        <tr>
                                            <td style="padding: 12px; font-size: 15px; font-weight: 500;">{{ batch.ingredient_name|default('-') }}</td>
                                            <td style="padding: 12px;">
                                                <span class="badge badge-primary" style="font-size: 12px;">{{ batch.batch_number }}</span>
                                            </td>
                                            <td style="padding: 12px;">{{ batch.quantity }} {{ batch.unit }}</td>
                                            <td style="padding: 12px;">
                                                {% if batch.expiry_date %}
                                                    {{ batch.expiry_date }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td style="padding: 12px;">
                                                {% if batch.supplier %}
                                                    {{ batch.supplier.name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td style="padding: 12px;">
                                                {% if batch.supplier %}
                                                    {% if batch.supplier.contact_person %}
                                                        {{ batch.supplier.contact_person }}<br>
                                                    {% endif %}
                                                    {% if batch.supplier.phone %}
                                                        <small style="font-size: 12px;">{{ batch.supplier.phone }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 溯源链 -->
                    <div class="card mb-4">
                        <div class="card-header" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 12px 20px;">
                            <h5 class="mb-0" style="font-size: 16px; font-weight: 500;">完整溯源链</h5>
                        </div>
                        <div class="card-body">
                            <div class="trace-chain">
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-utensils"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6 style="font-size: 15px; font-weight: 500; margin-bottom: 4px;">菜单计划</h6>
                                        <p style="font-size: 14px; margin-bottom: 2px;"><span style="color: #dc3545; font-weight: bold;">{{ trace_date }}</span> <span style="color: #dc3545; font-weight: bold;">{{ meal_type }}</span></p>
                                        <small style="font-size: 12px; color: #6c757d;"><span style="color: #dc3545; font-weight: bold;">{{ trace_data.recipes|length }}</span> 个菜品</small>
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6 style="font-size: 15px; font-weight: 500; margin-bottom: 4px;">消耗计划</h6>
                                        {% if trace_data.consumption_plan %}
                                        <p style="font-size: 14px; margin-bottom: 2px;">计划ID: <span style="color: #dc3545; font-weight: bold;">{{ trace_data.consumption_plan.id }}</span></p>
                                        <small style="font-size: 12px; color: #6c757d;">用餐人数: <span style="color: #dc3545; font-weight: bold;">{{ trace_data.consumption_plan.diners_count|default('-') }}</span></small>
                                        {% else %}
                                        <p style="font-size: 14px; margin-bottom: 2px; color: #dc3545;">暂无消耗计划</p>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-dolly-flatbed"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6 style="font-size: 15px; font-weight: 500; margin-bottom: 4px;">出库记录</h6>
                                        {% if trace_data.stock_out %}
                                        <p style="font-size: 14px; margin-bottom: 2px;"><span style="color: #dc3545; font-weight: bold;">{{ trace_data.stock_out.stock_out_number }}</span></p>
                                        <small style="font-size: 12px; color: #6c757d;">{{ trace_data.stock_out.stock_out_date }} - <span style="color: #dc3545; font-weight: bold;">{{ trace_data.stock_out.status }}</span></small>
                                        {% else %}
                                        <p style="font-size: 14px; margin-bottom: 2px; color: #dc3545;">暂无出库记录</p>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-boxes"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6 style="font-size: 15px; font-weight: 500; margin-bottom: 4px;">批次信息</h6>
                                        <p style="font-size: 14px; margin-bottom: 2px;"><span style="color: #dc3545; font-weight: bold;">{{ trace_data.batches|length }}</span> 个批次</p>
                                        {% if trace_data.batches %}
                                        {% set ingredient_names = [] %}
                                        {% for batch in trace_data.batches %}
                                            {% if batch.ingredient_name and batch.ingredient_name not in ingredient_names %}
                                                {% set _ = ingredient_names.append(batch.ingredient_name) %}
                                            {% endif %}
                                        {% endfor %}
                                        <small style="font-size: 12px; color: #6c757d;">涉及 <span style="color: #dc3545; font-weight: bold;">{{ ingredient_names|length }}</span> 种食材</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-industry"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6 style="font-size: 15px; font-weight: 500; margin-bottom: 4px;">供应商</h6>
                                        {% set supplier_names = [] %}
                                        {% for batch in trace_data.batches %}
                                            {% if batch.supplier and batch.supplier.name not in supplier_names %}
                                                {% set _ = supplier_names.append(batch.supplier.name) %}
                                            {% endif %}
                                        {% endfor %}
                                        <p style="font-size: 14px; margin-bottom: 2px;"><span style="color: #dc3545; font-weight: bold;">{{ supplier_names|length }}</span> 个供应商</p>
                                        {% if supplier_names %}
                                        <small style="font-size: 12px; color: #6c757d;">
                                            {% for name in supplier_names[:3] %}
                                                <span style="color: #dc3545; font-weight: bold;">{{ name }}</span>{% if not loop.last %}, {% endif %}
                                            {% endfor %}
                                            {% if supplier_names|length > 3 %}等{% endif %}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 留样记录 -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">留样记录</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>留样编号</th>
                                            <th>食谱名称</th>
                                            <th>留样图片</th>
                                            <th>留样数量</th>
                                            <th>留样时间</th>
                                            <th>销毁时间</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for sample in trace_data.food_samples %}
                                        <tr>
                                            <td>{{ sample.sample_number }}</td>
                                            <td>{{ sample.recipe_name }}</td>
                                            <td>
                                                {% if sample.sample_image %}
                                                <a href="{{ url_for('static', filename=sample.sample_image) }}" target="_blank">
                                                    <img src="{{ url_for('static', filename=sample.sample_image) }}" alt="留样图片" style="max-height: 50px;">
                                                </a>
                                                {% else %}
                                                无图片
                                                {% endif %}
                                            </td>
                                            <td>{{ sample.sample_quantity }} {{ sample.sample_unit }}</td>
                                            <td>{{ sample.start_time }}</td>
                                            <td>{{ sample.end_time }}</td>
                                            <td>{{ sample.status }}</td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无留样记录</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% elif not no_recipes_message and not error_message %}
                    <div class="alert alert-info">
                        请选择区域、日期和餐次进行查询
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .trace-chain {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .trace-step {
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 600px;
        margin-bottom: 12px;
        padding: 16px 20px;
        border-radius: 8px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        box-shadow: 0 3px 8px rgba(0,0,0,0.12);
        border-start: 4px solid #1e3c72;
    }

    .trace-icon {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        border-radius: 50%;
        margin-right: 18px;
        font-size: 18px;
        box-shadow: 0 2px 6px rgba(30, 60, 114, 0.3);
    }

    .trace-content {
        flex: 1;
    }

    .trace-arrow {
        margin: 8px 0;
        color: #1e3c72;
        font-size: 18px;
        opacity: 0.7;
    }
</style>
{% endblock %}
