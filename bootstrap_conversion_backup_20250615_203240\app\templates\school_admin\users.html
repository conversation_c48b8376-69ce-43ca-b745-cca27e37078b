{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ school.name }} - 用户管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('school_admin.add_user') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-user-plus"></i> 添加用户
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>真实姓名</th>
                                    <th>电子邮箱</th>
                                    <th>手机号码</th>
                                    <th>所属区域</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users.items %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.real_name }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.phone }}</td>
                                    <td>
                                        {% if user.area %}
                                            {{ user.area.get_level_name() }} - {{ user.area.name }}
                                        {% else %}
                                            未分配
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% for role in user.roles %}
                                            <span class="badge badge-info">{{ role.name }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% if user.status == 1 %}
                                            <span class="badge badge-success">启用</span>
                                        {% else %}
                                            <span class="badge badge-danger">禁用</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('school_admin.edit_user', id=user.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="mt-3">
                        {% include '_pagination.html' %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
