{# 区域相关宏 #}

{% macro area_tree_content(top_level_areas, current_user) %}
<div id="area-tree" class="area-tree">
    {% if top_level_areas %}
    <div class="list-group list-group-flush">
        {% for area in top_level_areas %}
        <div class="list-group-item border-0 px-0">
            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ url_for('area.view_area', id=area.id) }}"
                   class="text-decoration-none d-flex align-items-center">
                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                    <span>{{ area.name }}</span>
                    <span class="badge bg-info ms-2">{{ area.get_level_name() }}</span>
                </a>
                {% if area.children %}
                <button class="btn btn-sm btn-outline-secondary" type="button"
                        data-bs-toggle="collapse" data-bs-target="#area-{{ area.id }}"
                        aria-expanded="false">
                    <i class="fas fa-chevron-down"></i>
                </button>
                {% endif %}
            </div>

            {% if area.children %}
            <div class="collapse mt-2" id="area-{{ area.id }}">
                <div class="ms-3 border-start border-2 border-light ps-3">
                    {% for child in area.children %}
                    <div class="d-flex justify-content-between align-items-center py-1">
                        <a href="{{ url_for('area.view_area', id=child.id) }}"
                           class="text-decoration-none d-flex align-items-center">
                            <i class="fas fa-building text-success me-2"></i>
                            <span>{{ child.name }}</span>
                            <span class="badge bg-success ms-2">{{ child.get_level_name() }}</span>
                        </a>
                        {% if child.children %}
                        <button class="btn btn-sm btn-outline-secondary" type="button"
                                data-bs-toggle="collapse" data-bs-target="#area-{{ child.id }}">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        {% endif %}
                    </div>

                    {% if child.children %}
                    <div class="collapse mt-1" id="area-{{ child.id }}">
                        <div class="ms-3 border-start border-2 border-light ps-3">
                            {% for grandchild in child.children %}
                            <div class="py-1">
                                <a href="{{ url_for('area.view_area', id=grandchild.id) }}"
                                   class="text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-school text-warning me-2"></i>
                                    <span>{{ grandchild.name }}</span>
                                    <span class="badge bg-warning ms-2">{{ grandchild.get_level_name() }}</span>
                                    {% if grandchild.level == 3 and grandchild.is_township_school %}
                                    <span class="badge bg-primary ms-1">乡镇学校</span>
                                    {% endif %}
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-4">
        <i class="fas fa-map-marked-alt fs-1 text-muted opacity-50 mb-3"></i>
        <p class="text-muted mb-0">暂无区域数据</p>
        {% if current_user.is_admin() %}
        <a href="{{ url_for('area.add_area') }}" class="btn btn-primary btn-sm mt-2">
            <i class="fas fa-plus me-1"></i>添加第一个区域
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endmacro %}

{% macro current_area_info(current_area, current_user) %}
<div class="row g-3">
    <div class="col-12">
        <dl class="row mb-0">
            <dt class="col-sm-4">区域名称</dt>
            <dd class="col-sm-8">{{ current_area.name }}</dd>

            <dt class="col-sm-4">区域代码</dt>
            <dd class="col-sm-8"><code>{{ current_area.code }}</code></dd>

            <dt class="col-sm-4">区域级别</dt>
            <dd class="col-sm-8">
                <span class="badge bg-info">{{ current_area.get_level_name() }}</span>
            </dd>

            <dt class="col-sm-4">上级区域</dt>
            <dd class="col-sm-8">
                {% if current_area.parent %}
                <a href="{{ url_for('area.view_area', id=current_area.parent.id) }}"
                   class="text-decoration-none">
                    {{ current_area.parent.name }}
                </a>
                {% else %}
                <span class="text-muted">无</span>
                {% endif %}
            </dd>

            {% if current_area.description %}
            <dt class="col-sm-4">描述</dt>
            <dd class="col-sm-8">{{ current_area.description }}</dd>
            {% endif %}
        </dl>
    </div>
</div>

<div class="mt-3 d-flex gap-2">
    <a href="{{ url_for('area.view_area', id=current_area.id) }}"
       class="btn btn-primary btn-sm">
        <i class="fas fa-eye me-1"></i>查看详情
    </a>
    {% if current_user.is_admin() %}
    <a href="{{ url_for('area.edit_area', id=current_area.id) }}"
       class="btn btn-outline-secondary btn-sm">
        <i class="fas fa-edit me-1"></i>编辑
    </a>
    {% endif %}
</div>
{% endmacro %}

{% macro area_stats(current_area) %}
<div class="text-center">
    <div class="row g-2">
        <div class="col-6">
            <div class="p-2 bg-light rounded">
                <div class="fs-4 fw-bold text-primary">{{ current_area.children|length }}</div>
                <small class="text-muted">下级区域</small>
            </div>
        </div>
        <!-- 可根据需要添加更多统计项 -->
    </div>
</div>
{% endmacro %} 