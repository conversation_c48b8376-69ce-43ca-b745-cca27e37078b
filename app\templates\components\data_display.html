<!-- Bootstrap 5.3.6 数据展示组件库 -->
<!-- 使用方法: {% from 'components/data_display.html' import data_table, stat_card, progress_card %} -->

<!-- 现代化数据表格 -->
{% macro data_table(headers, rows, options={}) %}
{% set table_id = options.get('id', 'data-table-' ~ range(1000, 9999) | random) %}
{% set responsive = options.get('responsive', True) %}
{% set hover = options.get('hover', True) %}
{% set striped = options.get('striped', False) %}
{% set size = options.get('size', 'sm') %}
{% set sticky_header = options.get('sticky_header', False) %}

<div class="{% if responsive %}table-responsive{% endif %}">
  <table class="table {% if hover %}table-hover{% endif %} {% if striped %}table-striped{% endif %} {% if size %}table-{{ size }}{% endif %} mb-0" 
         id="{{ table_id }}"
         {% if sticky_header %}style="position: relative;"{% endif %}>
    <thead class="table-light {% if sticky_header %}sticky-top{% endif %}">
      <tr>
        {% for header in headers %}
        <th scope="col" 
            class="{{ header.get('class', '') }}"
            {% if header.get('width') %}style="width: {{ header.width }};"{% endif %}
            {% if header.get('sortable') %}data-sortable="true"{% endif %}>
          {{ header.text }}
          {% if header.get('sortable') %}
          <i class="fas fa-sort ms-1 text-muted sort-icon" style="cursor: pointer;"></i>
          {% endif %}
        </th>
        {% endfor %}
      </tr>
    </thead>
    <tbody>
      {% for row in rows %}
      <tr {% if row.get('class') %}class="{{ row.class }}"{% endif %}>
        {% for cell in row.cells if row.cells else row %}
        <td class="{{ cell.get('class', '') if cell is mapping else '' }}">
          {% if cell is mapping %}
            {% if cell.type == 'badge' %}
            <span class="badge bg-{{ cell.get('color', 'secondary') }}">{{ cell.text }}</span>
            {% elif cell.type == 'link' %}
            <a href="{{ cell.url }}" class="text-decoration-none">{{ cell.text }}</a>
            {% elif cell.type == 'button' %}
            <a href="{{ cell.url }}" class="btn btn-{{ cell.get('color', 'primary') }} btn-sm">{{ cell.text }}</a>
            {% elif cell.type == 'actions' %}
            <div class="btn-group btn-group-sm">
              {% for action in cell.actions %}
              <a href="{{ action.url }}" 
                 class="btn btn-outline-{{ action.get('color', 'secondary') }}"
                 title="{{ action.get('title', '') }}"
                 {% if action.get('onclick') %}onclick="{{ action.onclick }}"{% endif %}>
                <i class="{{ action.icon }}"></i>
              </a>
              {% endfor %}
            </div>
            {% elif cell.type == 'progress' %}
            <div class="progress" style="height: 8px;">
              <div class="progress-bar bg-{{ cell.get('color', 'primary') }}" 
                   style="width: {{ cell.value }}%"></div>
            </div>
            <small class="text-muted">{{ cell.value }}%</small>
            {% elif cell.type == 'avatar' %}
            <div class="d-flex align-items-center">
              {% if cell.get('image') %}
              <img src="{{ cell.image }}" class="rounded-circle me-2" width="32" height="32">
              {% else %}
              <div class="bg-{{ cell.get('color', 'primary') }} rounded-circle d-flex align-items-center justify-content-center me-2" 
                   style="width: 32px; height: 32px;">
                <span class="text-white fw-bold">{{ cell.text[0] }}</span>
              </div>
              {% endif %}
              <span>{{ cell.text }}</span>
            </div>
            {% elif cell.type == 'html' %}
            {{ cell.text | safe }}
            {% else %}
            {{ cell.text }}
            {% endif %}
          {% else %}
          {{ cell }}
          {% endif %}
        </td>
        {% endfor %}
      </tr>
      {% else %}
      <tr>
        <td colspan="{{ headers|length }}" class="text-center text-muted py-4">
          <i class="fas fa-inbox fs-1 opacity-50 mb-2"></i>
          <div>{{ options.get('empty_message', '暂无数据') }}</div>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

{% if options.get('sortable') %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
  const table = document.getElementById('{{ table_id }}');
  const headers = table.querySelectorAll('th[data-sortable="true"]');
  
  headers.forEach((header, index) => {
    header.addEventListener('click', function() {
      sortTable(table, index);
    });
  });
  
  function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = !table.dataset.sortDirection || table.dataset.sortDirection === 'desc';
    
    rows.sort((a, b) => {
      const aText = a.cells[column].textContent.trim();
      const bText = b.cells[column].textContent.trim();
      
      if (isAscending) {
        return aText.localeCompare(bText, 'zh-CN', { numeric: true });
      } else {
        return bText.localeCompare(aText, 'zh-CN', { numeric: true });
      }
    });
    
    rows.forEach(row => tbody.appendChild(row));
    table.dataset.sortDirection = isAscending ? 'asc' : 'desc';
    
    // 更新排序图标
    headers.forEach(h => {
      const icon = h.querySelector('.sort-icon');
      icon.className = 'fas fa-sort ms-1 text-muted sort-icon';
    });
    
    const currentIcon = header.querySelector('.sort-icon');
    currentIcon.className = `fas fa-sort-${isAscending ? 'up' : 'down'} ms-1 text-primary sort-icon`;
  }
});
</script>
{% endif %}
{% endmacro %}

<!-- 统计卡片 -->
{% macro stat_card(title, value, subtitle=None, icon=None, color="primary", trend=None, size="normal") %}
<div class="card border-0 shadow-sm h-100">
  <div class="card-body {% if size == 'small' %}py-3{% else %}py-4{% endif %} text-center">
    {% if icon %}
    <div class="mb-3">
      <div class="d-inline-flex align-items-center justify-content-center 
                  bg-{{ color }} bg-opacity-10 rounded-circle" 
           style="width: {{ '48px; height: 48px' if size == 'small' else '60px; height: 60px' }};">
        <i class="{{ icon }} text-{{ color }} {{ 'fs-5' if size == 'small' else 'fs-4' }}"></i>
      </div>
    </div>
    {% endif %}
    
    <h3 class="fw-bold text-{{ color }} mb-1 {{ 'fs-4' if size == 'small' else 'fs-3' }}">{{ value }}</h3>
    <h6 class="text-muted mb-0 {{ 'fs-7' if size == 'small' else 'fs-6' }}">{{ title }}</h6>
    
    {% if subtitle %}
    <small class="text-muted d-block mt-1">{{ subtitle }}</small>
    {% endif %}
    
    {% if trend %}
    <div class="mt-2">
      <span class="badge bg-{{ trend.color }} bg-opacity-10 text-{{ trend.color }}">
        <i class="fas fa-{{ trend.icon }} me-1"></i>{{ trend.text }}
      </span>
    </div>
    {% endif %}
  </div>
</div>
{% endmacro %}

<!-- 进度卡片 -->
{% macro progress_card(title, items, color="primary") %}
<div class="card border-0 shadow-sm h-100">
  <div class="card-header bg-{{ color }} text-white py-2">
    <h6 class="card-title mb-0">{{ title }}</h6>
  </div>
  <div class="card-body py-3">
    {% for item in items %}
    <div class="mb-3">
      <div class="d-flex justify-content-between align-items-center mb-1">
        <span class="small fw-medium">{{ item.label }}</span>
        <span class="small text-muted">{{ item.value }}%</span>
      </div>
      <div class="progress" style="height: 6px;">
        <div class="progress-bar bg-{{ item.get('color', color) }}" 
             style="width: {{ item.value }}%"></div>
      </div>
    </div>
    {% endfor %}
  </div>
</div>
{% endmacro %}

<!-- 时间线组件 -->
{% macro timeline(items, color="primary") %}
<div class="timeline">
  {% for item in items %}
  <div class="timeline-item">
    <div class="timeline-marker bg-{{ item.get('color', color) }}">
      {% if item.get('icon') %}
      <i class="{{ item.icon }} text-white"></i>
      {% endif %}
    </div>
    <div class="timeline-content">
      <h6 class="timeline-title">{{ item.title }}</h6>
      {% if item.get('time') %}
      <small class="text-muted">{{ item.time }}</small>
      {% endif %}
      {% if item.get('description') %}
      <p class="timeline-description mb-0">{{ item.description }}</p>
      {% endif %}
    </div>
  </div>
  {% endfor %}
</div>

<style nonce="{{ csp_nonce }}">
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #fff;
  box-shadow: 0 0 0 3px #dee2e6;
}

.timeline-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid var(--bs-primary);
}

.timeline-title {
  margin-bottom: 5px;
  font-weight: 600;
}

.timeline-description {
  color: #6c757d;
  font-size: 0.9rem;
}
</style>
{% endmacro %}

<!-- 数据列表 -->
{% macro data_list(items, title=None, actions=None) %}
<div class="card border-0 shadow-sm">
  {% if title or actions %}
  <div class="card-header bg-light py-2">
    <div class="d-flex justify-content-between align-items-center">
      {% if title %}
      <h6 class="card-title mb-0">{{ title }}</h6>
      {% endif %}
      {% if actions %}
      <div class="btn-group btn-group-sm">
        {% for action in actions %}
        <a href="{{ action.url }}" class="btn btn-outline-secondary">
          {% if action.icon %}<i class="{{ action.icon }}"></i>{% endif %}
          {% if action.text %}{{ action.text }}{% endif %}
        </a>
        {% endfor %}
      </div>
      {% endif %}
    </div>
  </div>
  {% endif %}
  
  <div class="list-group list-group-flush">
    {% for item in items %}
    <div class="list-group-item d-flex justify-content-between align-items-center py-2">
      <div class="flex-grow-1">
        {% if item.get('icon') %}
        <i class="{{ item.icon }} text-{{ item.get('color', 'muted') }} me-2"></i>
        {% endif %}
        <span class="{% if item.get('strong') %}fw-semibold{% endif %}">{{ item.text }}</span>
        {% if item.get('subtitle') %}
        <small class="text-muted d-block">{{ item.subtitle }}</small>
        {% endif %}
      </div>
      
      {% if item.get('value') %}
      <span class="badge bg-{{ item.get('badge_color', 'secondary') }}">{{ item.value }}</span>
      {% endif %}
      
      {% if item.get('actions') %}
      <div class="btn-group btn-group-sm ms-2">
        {% for action in item.actions %}
        <a href="{{ action.url }}" 
           class="btn btn-outline-secondary btn-sm"
           title="{{ action.get('title', '') }}">
          <i class="{{ action.icon }}"></i>
        </a>
        {% endfor %}
      </div>
      {% endif %}
    </div>
    {% else %}
    <div class="list-group-item text-center py-4">
      <i class="fas fa-inbox fs-1 text-muted opacity-50 mb-2"></i>
      <div class="text-muted">暂无数据</div>
    </div>
    {% endfor %}
  </div>
</div>
{% endmacro %}

<!-- 键值对展示 -->
{% macro key_value_list(items, columns=1) %}
<dl class="row mb-0">
  {% for item in items %}
  <dt class="col-sm-{{ 12 // columns // 2 }} text-muted">{{ item.key }}</dt>
  <dd class="col-sm-{{ 12 // columns - (12 // columns // 2) }}">
    {% if item.get('type') == 'badge' %}
    <span class="badge bg-{{ item.get('color', 'secondary') }}">{{ item.value }}</span>
    {% elif item.get('type') == 'link' %}
    <a href="{{ item.url }}" class="text-decoration-none">{{ item.value }}</a>
    {% else %}
    {{ item.value }}
    {% endif %}
  </dd>
  {% endfor %}
</dl>
{% endmacro %}
