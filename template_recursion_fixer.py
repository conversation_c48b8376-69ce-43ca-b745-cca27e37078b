#!/usr/bin/env python3
"""
模板递归错误修复工具
专门用于检测和修复Jinja2模板中的循环引用问题
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

class TemplateRecursionFixer:
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.templates_dir = self.project_root / "app" / "templates"
        self.components_dir = self.templates_dir / "components"
        
        # 记录修复统计
        self.files_checked = 0
        self.files_fixed = 0
        self.issues_found = 0
        self.issues_fixed = 0
        
        # 依赖关系图
        self.dependency_graph = {}
        self.circular_dependencies = []
        
    def analyze_template_dependencies(self) -> Dict[str, List[str]]:
        """分析模板文件的依赖关系"""
        dependencies = {}
        
        print("🔍 分析模板依赖关系...")
        
        for template_file in self.templates_dir.rglob("*.html"):
            relative_path = str(template_file.relative_to(self.templates_dir))
            dependencies[relative_path] = []
            
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找 extends 语句
                extends_matches = re.findall(r"{% extends ['\"]([^'\"]+)['\"] %}", content)
                for match in extends_matches:
                    dependencies[relative_path].append(match)
                
                # 查找 from ... import 语句
                import_matches = re.findall(r"{% from ['\"]([^'\"]+)['\"] import", content)
                for match in import_matches:
                    dependencies[relative_path].append(match)
                
                # 查找 include 语句
                include_matches = re.findall(r"{% include ['\"]([^'\"]+)['\"] %}", content)
                for match in include_matches:
                    dependencies[relative_path].append(match)
                    
            except Exception as e:
                print(f"❌ 分析 {relative_path} 时出错: {e}")
        
        self.dependency_graph = dependencies
        return dependencies
    
    def detect_circular_dependencies(self) -> List[List[str]]:
        """检测循环依赖"""
        print("🔄 检测循环依赖...")
        
        def dfs(node: str, path: List[str], visited: Set[str]) -> List[str]:
            if node in path:
                # 找到循环
                cycle_start = path.index(node)
                return path[cycle_start:] + [node]
            
            if node in visited:
                return []
            
            visited.add(node)
            path.append(node)
            
            for dependency in self.dependency_graph.get(node, []):
                cycle = dfs(dependency, path.copy(), visited.copy())
                if cycle:
                    return cycle
            
            return []
        
        cycles = []
        for template in self.dependency_graph:
            cycle = dfs(template, [], set())
            if cycle and cycle not in cycles:
                cycles.append(cycle)
        
        self.circular_dependencies = cycles
        return cycles
    
    def fix_circular_dependencies(self):
        """修复循环依赖"""
        if not self.circular_dependencies:
            print("✅ 未发现循环依赖")
            return
        
        print(f"🔧 发现 {len(self.circular_dependencies)} 个循环依赖，开始修复...")
        
        for i, cycle in enumerate(self.circular_dependencies):
            print(f"\n🔄 循环依赖 {i+1}: {' -> '.join(cycle)}")
            
            # 修复策略：移除最后一个依赖关系
            if len(cycle) >= 2:
                source_file = cycle[-2]
                target_file = cycle[-1]
                
                self.remove_dependency(source_file, target_file)
                self.issues_fixed += 1
    
    def remove_dependency(self, source_file: str, target_file: str):
        """移除指定的依赖关系"""
        source_path = self.templates_dir / source_file
        
        if not source_path.exists():
            print(f"⚠️ 源文件不存在: {source_file}")
            return
        
        try:
            with open(source_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 移除 from ... import 语句
            escaped_target = re.escape(target_file)
            pattern = r"{% from ['\"]" + escaped_target + r"['\"] import[^%]*%}"
            content = re.sub(pattern, '', content)

            # 移除 include 语句
            pattern = r"{% include ['\"]" + escaped_target + r"['\"] %}"
            content = re.sub(pattern, '', content)
            
            # 清理多余的空行
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            
            if content != original_content:
                with open(source_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 已修复 {source_file} 中对 {target_file} 的依赖")
                self.files_fixed += 1
            
        except Exception as e:
            print(f"❌ 修复 {source_file} 时出错: {e}")
    
    def check_problematic_patterns(self):
        """检查可能导致递归的模式"""
        print("🔍 检查可能导致递归的模式...")
        
        problematic_patterns = [
            (r"{% from ['\"]([^'\"]*)\1['\"] import", "自引用导入"),
            (r"{% include ['\"]([^'\"]*)\1['\"] %}", "自引用包含"),
            (r"{{ ([a-zA-Z_][a-zA-Z0-9_]*)\(\s*\1\s*\)", "宏自调用"),
        ]
        
        for template_file in self.templates_dir.rglob("*.html"):
            self.files_checked += 1
            relative_path = str(template_file.relative_to(self.templates_dir))
            
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern, description in problematic_patterns:
                    matches = re.finditer(pattern, content)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        print(f"⚠️ {relative_path}:{line_num} - {description}")
                        print(f"   {match.group().strip()}")
                        self.issues_found += 1
                        
            except Exception as e:
                print(f"❌ 检查 {relative_path} 时出错: {e}")
    
    def create_safe_templates(self):
        """创建安全的模板版本"""
        print("🛡️ 创建安全的模板版本...")
        
        # 为有问题的模板创建简化版本
        problematic_files = [
            "area/index.html",
            "supplier/index.html", 
            "ingredient/index.html"
        ]
        
        for file_path in problematic_files:
            full_path = self.templates_dir / file_path
            if full_path.exists():
                self.create_safe_version(full_path)
    
    def create_safe_version(self, template_path: Path):
        """为单个模板创建安全版本"""
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除所有组件导入
            content = re.sub(r"{% from 'components/[^']*' import[^%]*%}\n?", '', content)
            
            # 移除宏调用，替换为简单HTML
            content = re.sub(r"{{ [a-zA-Z_][a-zA-Z0-9_]*\([^}]*\) }}", 
                           '<div class="alert alert-info">内容已简化</div>', content)
            
            # 清理多余空行
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            
            # 备份原文件
            backup_path = template_path.with_suffix('.html.backup')
            if not backup_path.exists():
                template_path.rename(backup_path)
                print(f"📦 已备份: {template_path.name}")
            
            # 写入安全版本
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已创建安全版本: {template_path.name}")
            
        except Exception as e:
            print(f"❌ 创建安全版本失败 {template_path}: {e}")
    
    def run(self):
        """运行修复工具"""
        print("🚀 模板递归错误修复工具启动")
        print("=" * 50)
        
        if not self.templates_dir.exists():
            print(f"❌ 模板目录不存在: {self.templates_dir}")
            return
        
        # 1. 分析依赖关系
        self.analyze_template_dependencies()
        
        # 2. 检测循环依赖
        cycles = self.detect_circular_dependencies()
        
        # 3. 修复循环依赖
        if cycles:
            self.fix_circular_dependencies()
        
        # 4. 检查其他问题模式
        self.check_problematic_patterns()
        
        # 5. 创建安全模板
        if self.issues_found > 0:
            self.create_safe_templates()
        
        # 6. 输出统计
        print(f"\n📊 修复完成:")
        print(f"   检查文件: {self.files_checked}")
        print(f"   修复文件: {self.files_fixed}")
        print(f"   发现问题: {self.issues_found}")
        print(f"   修复问题: {self.issues_fixed}")
        print(f"   循环依赖: {len(self.circular_dependencies)}")
        
        if self.issues_fixed > 0:
            print(f"\n🎉 已修复 {self.issues_fixed} 个递归问题")
            print("✅ 模板现在应该可以正常工作了")
        else:
            print(f"\n✅ 未发现需要修复的递归问题")

if __name__ == "__main__":
    fixer = TemplateRecursionFixer()
    fixer.run()
