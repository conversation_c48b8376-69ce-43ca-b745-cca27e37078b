<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ weekly_menu.area.name }}周菜单计划</title>
    <style nonce="{{ csp_nonce }}">
        body {
            font-family: SimSun, "宋体", "Microsoft YaHei", "微软雅黑", sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12pt;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
        }
        .title {
            font-size: 20pt;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .subtitle {
            font-size: 16pt;
            margin-bottom: 15px;
        }
        .info {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
        }
        .info-item {
            margin-bottom: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #000;
            padding: 6px;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        .date-cell {
            font-weight: bold;
            text-align: center;
            width: 10%;
        }
        .meal-cell {
            width: 30%;
            text-align: left;
        }
        .meal-list {
            text-align: left;
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }
        .meal-list li {
            margin-bottom: 3px;
            font-size: 10pt;
        }
        .footer {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            margin-top: 40px;
        }
        @d-flex print {
            body {
                padding: 0;
            }
            @page {
                size: A4 landscape;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class=\"container'>
        <div class='header\">
            <div class=\"title'>{{ weekly_menu.area.name }}周菜单计划表</div>
            <div class='subtitle\">{{ weekly_menu.week_start|format_datetime('%Y年%m月%d日') }} 至 {{ weekly_menu.week_end|format_datetime('%Y年%m月%d日') }}</div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th style=\"width: 10%'>日期</th>
                    <th class='w-30\">早餐</th>
                    <th class=\"w-30'>午餐</th>
                    <th class='w-30\">晚餐</th>
                </tr>
            </thead>
            <tbody>
                {% for date_str, day_data in week_data.items() %}
                <tr>
                    <td class=\"date-cell'>
                        <div>{{ day_data.weekday }}</div>
                        <div>{{ date_str }}</div>
                    </td>
                    {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                    <td class='meal-cell\">
                        {% if day_data.meals[meal_type] %}
                        <ul class=\"meal-list'>
                            {% for recipe in day_data.meals[meal_type] %}
                            <li>{{ recipe.name }}</li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <span class='text-muted\">未安排</span>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class=\"footer'>
            <div>
                <div><strong>制表人：</strong>{{ weekly_menu.creator.real_name or weekly_menu.creator.username }}</div>
                <div class='signature\">签名：________________</div>
            </div>
            <div>
                <div><strong>审核人：</strong>________________</div>
                <div class=\"signature'>签名：________________</div>
            </div>
            <div>
                <div><strong>日期：</strong>{{ weekly_menu.created_at|format_datetime('%Y年%m月%d日') }}</div>
            </div>
        </div>
    </div>
    
    <script nonce='{{ csp_nonce }}\">
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
