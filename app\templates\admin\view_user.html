{% extends 'admin/base_admin.html' %}

{% block page_title %}用户详情{% endblock %}

{% block toolbar_actions %}
<a href="{{ url_for('system.users') }}" class="btn btn-secondary">
    <i class="fas fa-arrow-left"></i> 返回列表
</a>
        <a href="{{ url_for('system.edit_user', id=user.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> 编辑用户
        </a>
{% endblock %}

{% block content_body %}
<div class="row g-3">
    <!-- 基本信息 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header py-2">
                <h6 class="mb-0">基本信息</h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label text-muted">用户名</label>
                        <div>{{ user.username }}</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">真实姓名</label>
                        <div>{{ user.real_name or '-' }}</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">电子邮箱</label>
                        <div>{{ user.email }}</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">状态</label>
                        <div>
                            {% if user.status == 1 %}
                            <span class="badge bg-success">启用</span>
                            {% else %}
                            <span class="badge bg-danger">禁用</span>
                            {% endif %}
            </div>
        </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">创建时间</label>
                        <div>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">最后更新</label>
                        <div>{{ user.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限信息 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header py-2">
                <h6 class="mb-0">权限信息</h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label text-muted">所属区域</label>
                        <div>
                {% if user.area %}
                            <span class="badge bg-info">{{ user.area.get_level_name() }}</span>
                            {{ user.area.name }}
                                    {% else %}
                            <span class="text-muted">未设置</span>
                                    {% endif %}
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label text-muted">角色</label>
                        <div>
                            {% for role in user.roles %}
                            <span class="badge bg-primary">{{ role.name }}</span>
                            {% else %}
                            <span class="text-muted">无角色</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label text-muted">权限</label>
                        <div>
                            {% set permissions = user.get_all_permissions() %}
                            {% if permissions %}
                            <div class="row g-2">
                                {% for module, perms in permissions.items() %}
                    <div class="col-md-6">
                        <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h6 class="card-title mb-2">{{ module }}</h6>
                                            {% for perm in perms %}
                                            <span class="badge bg-secondary">{{ perm }}</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                        {% endfor %}
                            </div>
                            {% else %}
                            <span class="text-muted">无权限</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>

    <!-- 其他信息 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header py-2">
                <h6 class="mb-0">其他信息</h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label text-muted">备注</label>
                        <div>{{ user.notes or '无' }}</div>
                </div>
                    <div class="col-12">
                        <label class="form-label text-muted">最后登录</label>
                        <div>
                            {% if user.last_login %}
                            {{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') }}
                {% else %}
                            <span class="text-muted">从未登录</span>
                {% endif %}
            </div>
        </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}