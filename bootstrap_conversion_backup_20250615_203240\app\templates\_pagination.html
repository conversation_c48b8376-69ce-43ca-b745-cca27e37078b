{% if pagination is defined %}
    {% set items = pagination %}
{% elif users is defined %}
    {% set items = users %}
{% elif logs is defined %}
    {% set items = logs %}
{% elif records is defined %}
    {% set items = records %}
{% endif %}

{% if items and items.pages > 1 %}
<nav aria-label="分页导航">
    <ul class="pagination justify-content-center">
        {% if items.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for(request.endpoint, page=items.prev_num, **request.args) }}" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
                <span class="visually-hidden">上一页</span>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <a class="page-link" href="#" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
                <span class="visually-hidden">上一页</span>
            </a>
        </li>
        {% endif %}

        {% for page_num in items.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
            {% if page_num %}
                {% if page_num == items.page %}
                <li class="page-item active">
                    <a class="page-link" href="#">{{ page_num }} <span class="visually-hidden">(当前页)</span></a>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for(request.endpoint, page=page_num, **request.args) }}">{{ page_num }}</a>
                </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#">...</a>
                </li>
            {% endif %}
        {% endfor %}

        {% if items.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for(request.endpoint, page=items.next_num, **request.args) }}" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
                <span class="visually-hidden">下一页</span>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <a class="page-link" href="#" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
                <span class="visually-hidden">下一页</span>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
<div class="text-center">
    <small class="text-muted">
        显示第 {{ items.page }} 页，共 {{ items.pages }} 页，总计 {{ items.total }} 条记录
    </small>
</div>
{% endif %}
