/* 仪表盘页面优化样式 */

/* === 基础布局优化 === */
.container-fluid {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--theme-primary, #4e73df);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--theme-primary-light, rgba(78, 115, 223, 0.1));
}

/* === 今日菜单优化 === */
.menu-card {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  transition: all 0.3s ease;
  height: 100%;
}

.menu-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.menu-card .card-header {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  color: white;
  border: none;
  padding: 1rem;
  text-align: center;
  font-weight: 600;
}

.menu-card .card-header h5 {
  margin: 0;
  font-size: 1.1rem;
  /* text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); */ /* 注释掉文字阴影，避免模糊效果 */
}

.menu-card .card-body {
  padding: 1.25rem;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.menu-item {
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background-color: #eaecf4;
  border-color: var(--theme-primary-light);
}

.menu-item:last-child {
  margin-bottom: 0;
}

.status-badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-weight: 600;
}

/* === 陪餐记录表格优化 === */
.table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

.table-hover tbody tr:hover {
  background-color: rgba(78, 115, 223, 0.05);
}

/* 这个样式已经在 table-optimization.css 中统一定义了 */

.table tbody td {
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
  border-top: 1px solid #e3e6f0;
  font-size: 1.125rem; /* 18px */
}

.table tbody tr:first-child td {
  border-top: none;
}

/* === 功能卡片优化 === */
.function-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
  height: 100%;
}

.function-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-color: var(--theme-primary);
}

.function-icon {
  font-size: 2.5rem;
  color: var(--theme-primary);
  margin-bottom: 1rem;
}

.function-card h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #5a5c69;
  margin-bottom: 0.5rem;
}

.function-card .text-muted {
  font-size: 0.8rem;
}

/* === 食品溯源和留样卡片优化 === */
.card h-100 {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  transition: all 0.3s ease;
}

.card.h-100:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* .card-header 样式已移至 theme-colors.css 统一管理 */

/* .card-header h5 样式已移至 theme-colors.css 统一管理 */

.card-body {
  padding: 1.5rem 1.25rem;
}

.card-body p {
  color: #5a5c69;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.card-body .fa-4x {
  opacity: 0.8;
}

/* === 二维码容器优化 === */
.qr-code-container {
  padding: 1rem;
  background-color: #f8f9fc;
  border-radius: 8px;
  margin: 1rem 0;
  border: 2px dashed #e3e6f0;
}

.qr-code-img {
  max-width: 150px;
  height: auto;
  border-radius: 4px;
}

/* === 快速操作区域优化 === */
.quick-actions .btn {
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.quick-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* === 徽章和状态指示器优化 === */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
}

/* === 星级评分优化 === */
.star-rating {
  color: #ffc107;
  font-size: 0.875rem;
}

.star-rating .fa-star {
  margin-right: 1px;
}

/* === 加载状态优化 === */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* === 响应式优化 === */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .menu-card .card-body {
    min-height: 150px;
    padding: 1rem;
  }

  .function-icon {
    font-size: 2rem;
  }

  .function-card h5 {
    font-size: 0.9rem;
  }

  .table thead th {
    font-size: 0.75rem;
    padding: 0.5rem 0.25rem;
  }

  .table tbody td {
    font-size: 0.8rem;
    padding: 0.5rem 0.25rem;
  }
}

@media (max-width: 576px) {
  .menu-card .card-body {
    min-height: 120px;
  }

  .qr-code-img {
    max-width: 120px;
  }

  .quick-actions .btn {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }
}

/* === 动画效果 === */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.5s ease-out;
}

/* === 特殊效果 === */
.card-footer {
  background-color: #f8f9fc;
  border-top: 1px solid #e3e6f0;
  padding: 0.75rem 1.25rem;
}

.text-decoration-none:hover {
  text-decoration: none !important;
}

/* === 改进的空状态显示 === */
.empty-state {
  text-align: center;
  padding: 2rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  font-size: 1rem;
  margin-bottom: 0;
}

/* === 入库单详情页面优化 === */
.stock-in-detail-page .card {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  margin-bottom: 1.5rem;
}

/* .card-header 样式已移至 theme-colors.css 统一管理 */

/* 入库明细表格优化 */
.stock-in-items-table {
  margin-bottom: 0;
}

.stock-in-items-table thead {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
}

.stock-in-items-table thead th {
  background: transparent;
  color: white;
  border: none;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 12px 16px;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
}

.stock-in-items-table tbody td {
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
  border-top: 1px solid #e3e6f0;
  font-size: 0.875rem;
}

.stock-in-items-table tbody tr:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.05);
}

.stock-in-items-table tbody tr:first-child td {
  border-top: none;
}

.stock-in-items-table a {
  color: var(--theme-primary);
  text-decoration: none;
  font-weight: 500;
}

.stock-in-items-table a:hover {
  color: var(--theme-primary-dark);
  text-decoration: underline;
}

/* 入库单据管理表格优化 */
.document-table {
  margin-bottom: 0;
}

.document-table thead {
  background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

.document-table thead th {
  background: transparent;
  color: white;
  border: none;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 12px 16px;
  text-align: center;
  vertical-align: middle;
}

.document-table tbody td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-top: 1px solid #e3e6f0;
  font-size: 0.875rem;
}

.document-table tbody tr:hover {
  background-color: rgba(23, 162, 184, 0.05);
}

.document-table .filename-cell {
  max-width: 200px;
}

.document-table .filename-text {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.document-table .action-buttons {
  white-space: nowrap;
}

/* 文档表格中的小按钮样式 - 优先级更高 */
.document-table .action-buttons .document-actions .btn {
  width: 16px !important;
  height: 18px !important;
  padding: 0 !important;
  font-size: 8px !important;
  margin: 0 !important;
  border-radius: 2px !important;
  min-width: 16px !important;
  max-width: 16px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  border-width: 1px !important;
}

/* 覆盖默认的按钮样式 */
.document-table .action-buttons .btn {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

/* 强制应用小按钮样式到所有操作按钮 */
.document-table td.action-buttons .document-actions .btn-outline-primary,
.document-table td.action-buttons .document-actions .btn-outline-success,
.document-table td.action-buttons .document-actions .btn-outline-danger {
  width: 16px !important;
  height: 18px !important;
  padding: 0 !important;
  font-size: 8px !important;
  margin: 0 1px !important;
  border-radius: 2px !important;
  min-width: 16px !important;
  max-width: 16px !important;
}

/* 文档类型徽章优化 */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
}

.badge-warning {
  background-color: #f6c23e;
  color: #1a1a1a;
}

.badge-info {
  background-color: #36b9cc;
  color: white;
}

/* 操作按钮区域优化 */
.stock-in-actions {
  background-color: #f8f9fc;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.stock-in-actions .btn {
  font-size: 0.85rem;
  padding: 0.4rem 0.7rem;
  border-radius: 3px;
  font-weight: 600;
  margin: 0.1rem;
  transition: all 0.2s ease;
}

.stock-in-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* 模态框优化 */
.modal-header.bg-info {
  background-color: #17a2b8 !important;
}

.modal-header.bg-primary {
  background-color: var(--theme-primary) !important;
}

/* 文档信息面板优化 */
.document-info-panel {
  background-color: #f8f9fc;
  border-right: 1px solid #e3e6f0;
}

.document-preview-panel {
  background-color: white;
}

/* 文件上传区域优化 */
.custom-file-label {
  border: 2px dashed #e3e6f0;
  background-color: #f8f9fc;
  transition: all 0.2s ease;
}

.custom-file-input:focus ~ .custom-file-label {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 批次号选择器优化 */
.badge.mr-1.mb-1 {
  cursor: pointer;
  transition: all 0.2s ease;
}

.badge.mr-1.mb-1:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格响应式优化 */
@media (max-width: 768px) {
  .stock-in-items-table thead th,
  .document-table thead th {
    font-size: 0.75rem;
    padding: 0.5rem 0.25rem;
  }

  .stock-in-items-table tbody td,
  .document-table tbody td {
    font-size: 0.8rem;
    padding: 0.5rem 0.25rem;
  }

  .document-table .action-buttons .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .stock-in-actions {
    padding: 1rem;
  }

  .stock-in-actions .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    margin: 0.25rem 0;
    width: 100%;
  }
}

/* 状态提示优化 */
.alert {
  border: none;
  border-radius: 8px;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert-info {
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
  border-left: 4px solid #17a2b8;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(246, 194, 62, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
  border-left: 4px solid #f6c23e;
}

.alert-success {
  background: linear-gradient(135deg, rgba(28, 200, 138, 0.1) 0%, rgba(40, 167, 69, 0.1) 100%);
  border-left: 4px solid #1cc88a;
}

.alert-secondary {
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(134, 142, 150, 0.1) 100%);
  border-left: 4px solid #6c757d;
}
