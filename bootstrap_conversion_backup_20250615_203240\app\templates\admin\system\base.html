{% extends 'base.html' %}

{% block title %}系统管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{% block admin_title %}系统管理{% endblock %}</h2>
        <p class="text-muted">{% block admin_description %}管理系统设置和配置{% endblock %}</p>
    </div>
    <div class="col-md-4 text-end">
        {% block admin_actions %}
        <a href="{{ url_for('system.dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-tachometer-alt"></i> 返回仪表盘
        </a>
        {% endblock %}
    </div>
</div>

<div class="row">
    <div class="col-md-3 mb-4">
        <div class="list-group">
            <a href="{{ url_for('system.dashboard') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system.dashboard' %}active{% endif %}">
                <i class="fas fa-tachometer-alt"></i> 系统仪表盘
            </a>
            <a href="{{ url_for('system.settings') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system.settings' %}active{% endif %}">
                <i class="fas fa-cogs"></i> 系统设置
            </a>
            <a href="{{ url_for('system.users') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system.users' %}active{% endif %}">
                <i class="fas fa-users"></i> 用户管理
            </a>
            <a href="{{ url_for('system.roles') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system.roles' %}active{% endif %}">
                <i class="fas fa-user-tag"></i> 角色管理
            </a>
            <a href="{{ url_for('system.module_visibility') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system.module_visibility' %}active{% endif %}">
                <i class="fas fa-eye"></i> 模块可见性
            </a>
            <a href="{{ url_for('system.backups') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system.backups' %}active{% endif %}">
                <i class="fas fa-database"></i> 数据库备份
            </a>
            <a href="{{ url_for('system.monitor') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system.monitor' %}active{% endif %}">
                <i class="fas fa-chart-line"></i> 系统监控
            </a>
            <a href="{{ url_for('admin_data.super_delete_index') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'admin_data.super_delete_index' %}active{% endif %}">
                <i class="fas fa-trash"></i> 超级删除
            </a>
            <a href="{{ url_for('system_fix.index') }}" class="list-group-item list-group-item-action {% if request.endpoint == 'system_fix.index' %}active{% endif %}">
                <i class="fas fa-tools"></i> 系统修复
            </a>
        </div>
    </div>
    <div class="col-md-9">
        {% block admin_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
{% endblock %}
