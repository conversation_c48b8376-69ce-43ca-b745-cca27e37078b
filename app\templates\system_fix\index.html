{% extends 'base.html' %}

{% block title %}系统修复工具{% endblock %}

{% block content %}
<div class=\"container mt-4'>
    <div class='row\">
        <div class=\"col-md-12'>
            <div class='card\">
                <div class=\"card-header bg-primary text-white'>
                    <h4 class='mb-0\">系统修复工具</h4>
                </div>
                <div class=\"card-body'>
                    <div class='alert alert-warning\">
                        <i class=\"fas fa-exclamation-triangle'></i> 警告：这些操作会修改系统数据和结构，请谨慎使用！
                    </div>

                    <div class='row mt-4\">
                        <div class=\"col-md-6'>
                            <div class='card mb-3\">
                                <div class=\"card-header bg-info text-white'>
                                    <h5 class='mb-0\">数据库结构修复</h5>
                                </div>
                                <div class=\"card-body'>
                                    <p>修复数据库表结构，添加缺失的列或表。</p>
                                    <a href='{{ url_for('system_fix.fix_recipes_table') }}\" class=\"btn btn-outline-primary'>
                                        <i class='fas fa-tools\"></i> 修复食谱表结构
                                    </a>
                                    <a href="{{ url_for('system_fix.fix_food_samples_table') }}" class=\"btn btn-outline-primary mt-2'>
                                        <i class='fas fa-tools\"></i> 修复留样表结构
                                    </a>
                                    <a href="{{ url_for('system_fix.fix_purchase_orders_table') }}" class=\"btn btn-outline-primary mt-2'>
                                        <i class='fas fa-tools\"></i> 修复采购订单表结构
                                    </a>
                                    <a href="{{ url_for('system_fix.fix_supplier_school_relations') }}" class=\"btn btn-outline-primary mt-2'>
                                        <i class='fas fa-tools\"></i> 修复供应商-学校关联表
                                    </a>
                                    <a href="{{ url_for('system_fix.fix_needs_inspection_route') }}" class=\"btn btn-outline-primary mt-2'>
                                        <i class='fas fa-tools\"></i> 修复食材检验检疫字段
                                    </a>
                                    <a href="{{ url_for('system_fix.verify_database_structure') }}" class=\"btn btn-outline-info mt-2'>
                                        <i class='fas fa-check-circle\"></i> 验证数据库结构
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class=\"col-md-6'>
                            <div class='card mb-3\">
                                <div class=\"card-header bg-success text-white'>
                                    <h5 class='mb-0\">数据修复</h5>
                                </div>
                                <div class=\"card-body'>
                                    <p>检查并修复缺失或损坏的数据。</p>
                                    <a href='{{ url_for('system_fix.fix_missing_data') }}\" class=\"btn btn-outline-success'>
                                        <i class='fas fa-database\"></i> 修复缺失数据
                                    </a>
                                    <a href="{{ url_for('permission_migration.index') }}" class=\"btn btn-outline-success mt-2'>
                                        <i class='fas fa-key\"></i> 权限迁移工具
                                    </a>
                                    <a href="{{ url_for('permission_audit.index') }}" class=\"btn btn-outline-success mt-2'>
                                        <i class='fas fa-search\"></i> 权限审计工具
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class=\"row'>
                        <div class='col-md-6\">
                            <div class=\"card mb-3'>
                                <div class='card-header bg-warning text-dark\">
                                    <h5 class=\"mb-0'>代码兼容性修复</h5>
                                </div>
                                <div class='card-body\">
                                    <p>修复SQL语法和代码兼容性问题。</p>
                                    <a href="{{ url_for('system_fix.fix_sql_syntax') }}" class=\"btn btn-outline-warning'>
                                        <i class='fas fa-code\"></i> 修复SQL语法
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class=\"col-md-6'>
                            <div class='card mb-3\">
                                <div class=\"card-header bg-danger text-white'>
                                    <h5 class='mb-0\">全面修复</h5>
                                </div>
                                <div class=\"card-body'>
                                    <p>执行所有修复操作。</p>
                                    <a href='{{ url_for('system_fix.enhanced_auto_fix') }}\" class=\"btn btn-outline-danger mb-2'>
                                        <i class='fas fa-magic\"></i> 增强版一键修复
                                    </a>
                                    <p class=\"small text-muted'>增强版一键修复可以自动检测和修复表结构、列缺失、数据类型不匹配和SQL语法兼容性问题。</p>
                                    <hr>
                                    <a href='{{ url_for('system_fix.fix_all') }}\" class=\"btn btn-outline-danger'>
                                        <i class='fas fa-hammer\"></i> 执行所有修复
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class=\"mt-4'>
                        <a href='{{ url_for('main.index') }}\" class=\"btn btn-secondary'>
                            <i class='fas fa-arrow-left\"></i> 返回首页
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
