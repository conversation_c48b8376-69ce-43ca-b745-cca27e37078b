{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .table th {
        font-size: 14px;
    }
    .table td {
        font-size: 14px;
    }
    .btn-group-sm > .btn, .btn-sm {
        padding: .25rem .5rem;
        font-size: 14px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school) }}

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold text-primary">
                <i class="fas fa-clipboard-list me-1"></i> 日志列表
            </h6>
            <div>
                <a href="{{ url_for('daily_management.edit_log', date_str=now|format_datetime('%Y-%m-%d')) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus-circle me-1"></i> 创建今日日志
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>管理员</th>
                            <th>天气</th>
                            <th>学生就餐</th>
                            <th>教师就餐</th>
                            <th>其他就餐</th>
                            <th>食物浪费(kg)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr>
                            <td>{{ log.log_date|format_datetime('%Y-%m-%d') }}</td>
                            <td>{{ log.manager or '' }}</td>
                            <td>{{ log.weather or '' }}</td>
                            <td>{{ log.student_count or 0 }}</td>
                            <td>{{ log.teacher_count or 0 }}</td>
                            <td>{{ log.other_count or 0 }}</td>
                            <td>{{ log.food_waste or 0 }}</td>
                            <td>
                                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date|format_datetime('%Y-%m-%d')) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if logs.pages > 1 %}
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        {% if logs.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('daily_management.logs', page=logs.prev_num) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in logs.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                            {% if page_num %}
                                {% if page_num == logs.page %}
                                <li class="page-item active">
                                    <a class="page-link" href="{{ url_for('daily_management.logs', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('daily_management.logs', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if logs.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('daily_management.logs', page=logs.next_num) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
