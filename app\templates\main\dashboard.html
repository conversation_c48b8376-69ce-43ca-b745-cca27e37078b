{% extends 'base.html' %}
{% from 'components/layout.html' import content_section, responsive_grid, sidebar_layout %}
{% from 'components/data_display.html' import stat_card, data_table, data_list %}
{% from 'components/cards.html' import info_card %}

{% block title %}控制面板 - {{ super() }}{% endblock %}

{% block content %}
<!-- 欢迎信息 -->
{% call content_section(
  title="控制面板",
  subtitle="欢迎回来，" + (current_user.real_name or current_user.username)
) %}

<!-- 现代化统计卡片 -->
<div class="row g-3 mb-4">
  <!-- 供应商卡片 -->
  <div class="col-lg-3 col-md-6">
    <a href="{{ url_for('main.suppliers') }}" class="text-decoration-none">
      <div class="card border h-100 modern-stat-card">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h3 class="mb-1 text-primary">{{ suppliers_count }}</h3>
              <div class="text-muted">供应商</div>
            </div>
            <div class="text-primary">
              <i class="fas fa-building fs-4"></i>
            </div>
          </div>
          <div class="mt-2">
            <small class="text-muted">查看详情</small>
          </div>
        </div>
      </div>
    </a>
  </div>

  <!-- 食材卡片 -->
  <div class="col-lg-3 col-md-6">
    <a href="{{ url_for('main.ingredients') }}" class="text-decoration-none">
      <div class="card border h-100 modern-stat-card">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h3 class="mb-1 text-success">{{ ingredients_count }}</h3>
              <div class="text-muted">食材种类</div>
            </div>
            <div class="text-success">
              <i class="fas fa-carrot fs-4"></i>
            </div>
          </div>
          <div class="mt-2">
            <small class="text-muted">查看详情</small>
          </div>
        </div>
      </div>
    </a>
  </div>

  <!-- 食谱卡片 -->
  <div class="col-lg-3 col-md-6">
    <a href="{{ url_for('main.recipes') }}" class="text-decoration-none">
      <div class="card border h-100 modern-stat-card">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h3 class="mb-1 text-info">{{ recipes_count }}</h3>
              <div class="text-muted">菜谱数量</div>
            </div>
            <div class="text-info">
              <i class="fas fa-utensils fs-4"></i>
            </div>
          </div>
          <div class="mt-2">
            <small class="text-muted">查看详情</small>
          </div>
        </div>
      </div>
    </a>
  </div>

  <!-- 留样卡片 -->
  <div class="col-lg-3 col-md-6">
    <a href="{{ url_for('main.food_samples') }}" class="text-decoration-none">
      <div class="card border h-100 modern-stat-card">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h3 class="mb-1 text-warning">{{ samples_count }}</h3>
              <div class="text-muted">留样记录</div>
            </div>
            <div class="text-warning">
              <i class="fas fa-vial fs-4"></i>
            </div>
          </div>
          <div class="mt-2">
            <small class="text-muted">查看详情</small>
          </div>
        </div>
      </div>
    </a>
  </div>
</div>

<style nonce="{{ csp_nonce }}">
.modern-stat-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 16px !important;
  overflow: hidden;
}

.modern-stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.modern-stat-card .card-body {
  position: relative;
  overflow: hidden;
}

.modern-stat-card .card-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}
</style>
{% endcall %}

<!-- 主要内容区域 -->
{{ sidebar_layout(
  main_content=recent_orders_content(),
  sidebar_content=quick_actions_content(),
  main_cols="lg-8",
  sidebar_cols="lg-4"
) }}

{% macro recent_orders_content() %}
<!-- 最近采购订单 - 简洁设计 -->
<div class="card border">
  <div class="card-header bg-light border-bottom">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h6 class="mb-1 fw-semibold">最近采购订单</h6>
        <small class="text-muted">Recent Purchase Orders</small>
      </div>
      <a href="{{ url_for('main.purchase_orders') }}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-external-link-alt me-1"></i>查看全部
      </a>
    </div>
  </div>

  <!-- 桌面端表格 -->
  <div class="d-none d-lg-block">
    <div class="table-responsive">
      <table class="table table-hover mb-0">
        <thead class="table-light">
          <tr>
            <th class="border-0 fw-semibold">订单号</th>
            <th class="border-0 fw-semibold">供应商</th>
            <th class="border-0 fw-semibold text-end">金额</th>
            <th class="border-0 fw-semibold text-center">状态</th>
            <th class="border-0 fw-semibold">日期</th>
          </tr>
        </thead>
        <tbody>
          {% for order in recent_orders %}
          <tr class="border-0">
            <td class="py-3 border-0">
              <span class="fw-medium text-primary">#{{ order.id }}</span>
            </td>
            <td class="py-3 border-0">{{ order.supplier.name }}</td>
            <td class="py-3 border-0 text-end">
              <span class="fw-bold text-success">¥{{ order.total_amount }}</span>
            </td>
            <td class="py-3 border-0 text-center">
              {% set status_color = 'warning' if order.status == '待审核' else 'info' if order.status == '已发货' else 'success' if order.status == '已完成' else 'secondary' %}
              <span class="badge bg-{{ status_color }} bg-opacity-10 text-{{ status_color }} border border-{{ status_color }}">
                {{ order.status }}
              </span>
            </td>
            <td class="py-3 border-0 text-muted">{{ order.order_date|format_datetime('%m-%d') }}</td>
          </tr>
          {% else %}
          <tr>
            <td colspan="5" class="text-center py-5 border-0">
              <div class="text-muted">
                <i class="fas fa-shopping-cart fs-1 opacity-25 mb-3"></i>
                <div>暂无采购订单</div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- 移动端卡片 -->
  <div class="d-lg-none">
    <div class="card-body p-3">
      {% for order in recent_orders %}
      <div class="card mb-3 border-0 shadow-sm" style="border-radius: 12px;">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="flex-grow-1">
              <h6 class="mb-1 fw-semibold text-primary">订单 #{{ order.id }}</h6>
              <div class="text-muted small">{{ order.supplier.name }}</div>
            </div>
            <div class="text-end">
              <div class="fw-bold text-success fs-5">¥{{ order.total_amount }}</div>
              {% set status_color = 'warning' if order.status == '待审核' else 'info' if order.status == '已发货' else 'success' if order.status == '已完成' else 'secondary' %}
              <span class="badge bg-{{ status_color }} bg-opacity-10 text-{{ status_color }} border border-{{ status_color }}">
                {{ order.status }}
              </span>
            </div>
          </div>
          <div class="d-flex align-items-center text-muted small">
            <i class="fas fa-calendar me-2"></i>
            {{ order.order_date|format_datetime('%Y-%m-%d') }}
          </div>
        </div>
      </div>
      {% else %}
      <div class="text-center py-5">
        <i class="fas fa-shopping-cart fs-1 text-muted opacity-25 mb-3"></i>
        <div class="text-muted">暂无采购订单</div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>
{% endmacro %}

{% macro quick_actions_content() %}
<!-- 快捷操作 - 简洁设计 -->
<div class="card border">
  <div class="card-header bg-light border-bottom">
    <div>
      <h6 class="mb-1 fw-semibold">快捷操作</h6>
      <small class="text-muted">Quick Actions</small>
    </div>
  </div>

  <!-- 桌面端操作列表 -->
  <div class="d-none d-lg-block">
    <div class="list-group list-group-flush">
      <a href="#" class="list-group-item list-group-item-action border-0 py-3">
        <div class="d-flex align-items-center">
          <div class="bg-primary bg-opacity-10 rounded-3 p-2 me-3">
            <i class="fas fa-building text-primary"></i>
          </div>
          <div class="flex-grow-1">
            <div class="fw-medium">添加供应商</div>
            <small class="text-muted">管理供应商信息</small>
          </div>
          <i class="fas fa-chevron-right text-muted"></i>
        </div>
      </a>

      <a href="#" class="list-group-item list-group-item-action border-0 py-3">
        <div class="d-flex align-items-center">
          <div class="bg-success bg-opacity-10 rounded-3 p-2 me-3">
            <i class="fas fa-carrot text-success"></i>
          </div>
          <div class="flex-grow-1">
            <div class="fw-medium">添加食材</div>
            <small class="text-muted">录入新的食材信息</small>
          </div>
          <i class="fas fa-chevron-right text-muted"></i>
        </div>
      </a>

      <a href="#" class="list-group-item list-group-item-action border-0 py-3">
        <div class="d-flex align-items-center">
          <div class="bg-info bg-opacity-10 rounded-3 p-2 me-3">
            <i class="fas fa-utensils text-info"></i>
          </div>
          <div class="flex-grow-1">
            <div class="fw-medium">添加食谱</div>
            <small class="text-muted">创建新的菜谱</small>
          </div>
          <i class="fas fa-chevron-right text-muted"></i>
        </div>
      </a>

      <a href="#" class="list-group-item list-group-item-action border-0 py-3">
        <div class="d-flex align-items-center">
          <div class="bg-warning bg-opacity-10 rounded-3 p-2 me-3">
            <i class="fas fa-shopping-cart text-warning"></i>
          </div>
          <div class="flex-grow-1">
            <div class="fw-medium">创建采购订单</div>
            <small class="text-muted">发起新的采购申请</small>
          </div>
          <i class="fas fa-chevron-right text-muted"></i>
        </div>
      </a>

      <a href="#" class="list-group-item list-group-item-action border-0 py-3">
        <div class="d-flex align-items-center">
          <div class="bg-danger bg-opacity-10 rounded-3 p-2 me-3">
            <i class="fas fa-vial text-danger"></i>
          </div>
          <div class="flex-grow-1">
            <div class="fw-medium">添加留样记录</div>
            <small class="text-muted">记录食品留样信息</small>
          </div>
          <i class="fas fa-chevron-right text-muted"></i>
        </div>
      </a>
    </div>
  </div>

  <!-- 移动端按钮组 -->
  <div class="d-lg-none">
    <div class="card-body p-3">
      <div class="row g-2">
        <div class="col-6">
          <a href="#" class="btn btn-outline-primary w-100 py-3" style="border-radius: 12px;">
            <i class="fas fa-building d-block mb-1"></i>
            <small>添加供应商</small>
          </a>
        </div>
        <div class="col-6">
          <a href="#" class="btn btn-outline-success w-100 py-3" style="border-radius: 12px;">
            <i class="fas fa-carrot d-block mb-1"></i>
            <small>添加食材</small>
          </a>
        </div>
        <div class="col-6">
          <a href="#" class="btn btn-outline-info w-100 py-3" style="border-radius: 12px;">
            <i class="fas fa-utensils d-block mb-1"></i>
            <small>添加食谱</small>
          </a>
        </div>
        <div class="col-6">
          <a href="#" class="btn btn-outline-warning w-100 py-3" style="border-radius: 12px;">
            <i class="fas fa-shopping-cart d-block mb-1"></i>
            <small>采购订单</small>
          </a>
        </div>
        <div class="col-12">
          <a href="#" class="btn btn-outline-danger w-100 py-3" style="border-radius: 12px;">
            <i class="fas fa-vial me-2"></i>添加留样记录
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endmacro %}

{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    // 统计卡片悬停效果增强
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 自动刷新数据 (可选)
    if (window.location.pathname === '/dashboard') {
        setInterval(() => {
            // 这里可以添加AJAX刷新逻辑
            console.log('Dashboard data refresh check');
        }, 300000); // 5分钟检查一次
    }
});
</script>
{% endblock %}
