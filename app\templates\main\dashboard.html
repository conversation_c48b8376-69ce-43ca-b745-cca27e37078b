{% extends 'base.html' %}
{% from 'components/layout.html' import content_section, responsive_grid, sidebar_layout %}
{% from 'components/data_display.html' import stat_card, data_table, data_list %}
{% from 'components/cards.html' import info_card %}

{% block title %}控制面板 - {{ super() }}{% endblock %}

{% block content %}
<!-- 欢迎信息 -->
{% call content_section(
  title="控制面板",
  subtitle="欢迎回来，" + (current_user.real_name or current_user.username)
) %}

<!-- 统计卡片 -->
{% set stats_data = [
  {
    'title': '供应商',
    'value': suppliers_count,
    'icon': 'fas fa-building',
    'color': 'primary',
    'url': url_for('main.suppliers')
  },
  {
    'title': '食材',
    'value': ingredients_count,
    'icon': 'fas fa-carrot',
    'color': 'success',
    'url': url_for('main.ingredients')
  },
  {
    'title': '食谱',
    'value': recipes_count,
    'icon': 'fas fa-utensils',
    'color': 'warning',
    'url': url_for('main.recipes')
  },
  {
    'title': '留样',
    'value': samples_count,
    'icon': 'fas fa-vial',
    'color': 'danger',
    'url': url_for('main.food_samples')
  }
] %}

<div class="row g-3 mb-4">
  {% for stat in stats_data %}
  <div class="col-lg-3 col-md-6">
    <a href="{{ stat.url }}" class="text-decoration-none">
      <div class="card border-0 shadow-sm h-100 stat-card" data-color="{{ stat.color }}">
        <div class="card-body text-center py-3">
          <div class="mb-2">
            <div class="d-inline-flex align-items-center justify-content-center
                        bg-{{ stat.color }} bg-opacity-10 rounded-circle"
                 style="width: 50px; height: 50px;">
              <i class="{{ stat.icon }} text-{{ stat.color }} fs-4"></i>
            </div>
          </div>
          <h3 class="fw-bold text-{{ stat.color }} mb-1">{{ stat.value }}</h3>
          <h6 class="text-muted mb-0">{{ stat.title }}</h6>
        </div>
        <div class="card-footer bg-{{ stat.color }} bg-opacity-10 text-center py-2 d-none d-md-block">
          <small class="text-{{ stat.color }}">
            <i class="fas fa-arrow-right me-1"></i>查看详情
          </small>
        </div>
      </div>
    </a>
  </div>
  {% endfor %}
</div>

<style nonce="{{ csp_nonce }}">
.stat-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>
{% endcall %}

<!-- 主要内容区域 -->
{{ sidebar_layout(
  main_content=recent_orders_content(),
  sidebar_content=quick_actions_content(),
  main_cols="lg-8",
  sidebar_cols="lg-4"
) }}

{% macro recent_orders_content() %}
<!-- 最近采购订单 -->
{% set order_headers = [
  {'text': '订单号', 'class': ''},
  {'text': '供应商', 'class': ''},
  {'text': '金额', 'class': 'text-end'},
  {'text': '状态', 'class': 'text-center'},
  {'text': '日期', 'class': ''}
] %}

{% set order_rows = [] %}
{% for order in recent_orders %}
  {% set status_color = 'warning' if order.status == '待审核' else 'info' if order.status == '已发货' else 'success' if order.status == '已完成' else 'secondary' %}
  {% set row = {
    'cells': [
      {'text': '#' + order.id|string, 'type': 'text'},
      {'text': order.supplier.name, 'type': 'text'},
      {'text': '¥' + order.total_amount|string, 'type': 'text', 'class': 'text-end fw-medium'},
      {'text': order.status, 'type': 'badge', 'color': status_color, 'class': 'text-center'},
      {'text': order.order_date|format_datetime('%Y-%m-%d'), 'type': 'text'}
    ]
  } %}
  {% set order_rows = order_rows + [row] %}
{% endfor %}

<div class="card border-0 shadow-sm">
  <div class="card-header bg-primary text-white py-2">
    <div class="d-flex justify-content-between align-items-center">
      <h6 class="card-title mb-0">最近采购订单</h6>
      <a href="{{ url_for('main.purchase_orders') }}" class="btn btn-sm btn-outline-light">
        <i class="fas fa-external-link-alt me-1"></i>查看全部
      </a>
    </div>
  </div>

  <!-- 桌面端表格 -->
  <div class="d-none d-lg-block">
    {{ data_table(order_headers, order_rows, {
      'responsive': True,
      'hover': True,
      'size': 'sm',
      'empty_message': '暂无采购订单'
    }) }}
  </div>

  <!-- 移动端卡片 -->
  <div class="d-lg-none">
    <div class="card-body p-2">
      {% for order in recent_orders %}
      <div class="card mb-2 border-start border-primary border-3">
        <div class="card-body py-2 px-3">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <h6 class="mb-1 fs-6">订单 #{{ order.id }}</h6>
              <small class="text-muted">{{ order.supplier.name }}</small>
            </div>
            <div class="text-end">
              <div class="fw-bold text-primary">¥{{ order.total_amount }}</div>
              {% set status_color = 'warning' if order.status == '待审核' else 'info' if order.status == '已发货' else 'success' if order.status == '已完成' else 'secondary' %}
              <span class="badge bg-{{ status_color }}">{{ order.status }}</span>
            </div>
          </div>
          <div class="mt-1">
            <small class="text-muted">
              <i class="fas fa-calendar me-1"></i>{{ order.order_date|format_datetime('%Y-%m-%d') }}
            </small>
          </div>
        </div>
      </div>
      {% else %}
      <div class="text-center py-4">
        <i class="fas fa-shopping-cart fs-1 text-muted opacity-50 mb-2"></i>
        <p class="text-muted mb-0">暂无采购订单</p>
      </div>
      {% endfor %}
    </div>
  </div>
</div>
{% endmacro %}

{% macro quick_actions_content() %}
<!-- 快捷操作 -->
{% set quick_actions = [
  {
    'text': '添加供应商',
    'icon': 'fas fa-building',
    'url': url_for('supplier.create') if url_for('supplier.create') else '#',
    'color': 'primary'
  },
  {
    'text': '添加食材',
    'icon': 'fas fa-carrot',
    'url': url_for('ingredient.create') if url_for('ingredient.create') else '#',
    'color': 'success'
  },
  {
    'text': '添加食谱',
    'icon': 'fas fa-utensils',
    'url': url_for('recipe.create') if url_for('recipe.create') else '#',
    'color': 'warning'
  },
  {
    'text': '创建采购订单',
    'icon': 'fas fa-shopping-cart',
    'url': url_for('purchase_order.create') if url_for('purchase_order.create') else '#',
    'color': 'info'
  },
  {
    'text': '添加留样记录',
    'icon': 'fas fa-vial',
    'url': url_for('food_sample.create') if url_for('food_sample.create') else '#',
    'color': 'danger'
  }
] %}

{{ data_list(
  items=quick_actions,
  title="快捷操作"
) }}

<!-- 移动端按钮组 -->
<div class="d-lg-none mt-3">
  <div class="d-grid gap-2">
    {% for action in quick_actions %}
    <a href="{{ action.url }}" class="btn btn-outline-{{ action.color }}">
      <i class="{{ action.icon }} me-2"></i>{{ action.text }}
    </a>
    {% endfor %}
  </div>
</div>
{% endmacro %}

{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    // 统计卡片悬停效果增强
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 自动刷新数据 (可选)
    if (window.location.pathname === '/dashboard') {
        setInterval(() => {
            // 这里可以添加AJAX刷新逻辑
            console.log('Dashboard data refresh check');
        }, 300000); // 5分钟检查一次
    }
});
</script>
{% endblock %}
