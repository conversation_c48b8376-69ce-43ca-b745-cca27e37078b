{% extends 'daily_management/print/base_print.html' %}

{% block title %}食堂特殊事件记录 - {{ log.log_date|format_datetime('%Y-%m-%d') }}{% endblock %}

{% block document_title %}食堂特殊事件记录{% endblock %}

{% block document_subtitle %}{{ log.log_date|format_datetime('%Y年%m月%d日') }}{% endblock %}

{% block document_info %}
<div class="info-row">
    <div class="info-label">日期：</div>
    <div class="info-value">{{ log.log_date|format_datetime('%Y-%m-%d') }}</div>
</div>
<div class="info-row">
    <div class="info-label">管理员：</div>
    <div class="info-value">{{ log.manager or '未设置' }}</div>
</div>
<div class="info-row">
    <div class="info-label">事件数量：</div>
    <div class="info-value">{{ events|length }}个</div>
</div>
{% endblock %}

{% block content %}
<!-- 特殊事件记录 -->
<div class="section-title">特殊事件列表</div>
{% if events %}
<table>
    <thead>
        <tr>
            <th width="20%">事件类型</th>
            <th width="20%">事件时间</th>
            <th width="20%">负责人</th>
            <th width="40%">事件描述</th>
        </tr>
    </thead>
    <tbody>
        {% for event in events %}
        <tr>
            <td>{{ event.event_type }}</td>
            <td>{{ event.event_time|format_datetime('%H:%M') if event.event_time else '未记录' }}</td>
            <td>{{ event.responsible_person }}</td>
            <td>{{ event.description }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- 事件详情 -->
<div class="section-title">事件详情</div>
{% for event in events %}
<div style="margin-bottom: 20px; border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
    <h3 style="margin-top: 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">
        {{ event.event_type }}
    </h3>
    
    <div style="margin-bottom: 10px;">
        <strong>事件时间：</strong>
        {% if event.event_time %}
            {% if event.event_time is string %}
                {{ event.event_time }}
            {% else %}
                {{ event.event_time.strftime('%Y-%m-%d %H:%M') }}
            {% endif %}
        {% else %}
            未记录
        {% endif %}
        <strong class="ms-3">负责人：</strong> {{ event.responsible_person }}
        <strong class="ms-3">参与人数：</strong> {{ event.participant_count|default('未记录') }}人
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>事件描述：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ event.description|default('无描述')|nl2br }}
        </div>
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>准备工作：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ event.preparation|default('无准备工作记录')|nl2br }}
        </div>
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>执行情况：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ event.execution|default('无执行情况记录')|nl2br }}
        </div>
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>反馈评价：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ event.feedback|default('无反馈评价')|nl2br }}
        </div>
    </div>
    
    <!-- 事件照片 -->
    {% if event.photos and event.photos|length > 0 %}
    <div style="margin-top: 15px;">
        <strong>事件照片：</strong>
        <div class="photo-container">
            {% for photo in event.photos %}
            <div class="photo-item">
                <img src="{{ url_for('static', filename=photo.file_path) }}" alt="事件照片">
                <div class="photo-caption">事件照片 {{ loop.index }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endfor %}
{% else %}
<p>暂无特殊事件记录</p>
{% endif %}
{% endblock %}

{% block signature %}
<div class="signature-item">
    <div class="signature-line"></div>
    <div>记录人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>食堂负责人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>学校负责人</div>
</div>
{% endblock %}
