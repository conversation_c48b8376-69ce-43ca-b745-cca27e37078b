/**
 * 分类食材选择组件
 * 
 * 使用方法:
 * 1. 引入此JS文件
 * 2. 创建一个select元素，添加class="categorized-ingredient-select"
 * 3. 初始化组件: $('.categorized-ingredient-select').categorizedIngredientSelect();
 */

(function($) {
    $.fn.categorizedIngredientSelect = function(options) {
        // 默认配置
        var settings = $.extend({
            apiUrl: '/ingredient/api',
            categoryApiUrl: '/ingredient-category/api',
            placeholder: '-- 请选择食材 --',
            emptyMessage: '没有找到食材',
            loadingMessage: '加载中...',
            showCategory: true,
            showSearch: true,
            width: '100%'
        }, options);

        // 对每个匹配的元素应用插件
        return this.each(function() {
            var $select = $(this);
            var originalId = $select.attr('id');
            var originalName = $select.attr('name');
            var originalRequired = $select.prop('required');
            var originalValue = $select.val();
            
            // 创建包装容器
            var $wrapper = $('<div class="categorized-ingredient-select-wrapper"></div>');
            $select.after($wrapper);
            
            // 创建新的select元素
            var $newSelect = $('<select></select>')
                .attr('id', originalId)
                .attr('name', originalName)
                .prop('required', originalRequired)
                .addClass('form-control');
            
            // 添加placeholder选项
            $newSelect.append('<option value="">' + settings.placeholder + '</option>');
            
            // 将新的select元素添加到包装容器
            $wrapper.append($newSelect);
            
            // 移除原始select元素
            $select.remove();
            
            // 初始化Select2
            $newSelect.select2({
                width: settings.width,
                placeholder: settings.placeholder,
                allowClear: true,
                templateResult: formatIngredient,
                templateSelection: formatIngredientSelection
            });
            
            // 加载数据
            loadData($newSelect, originalValue);
            
            // 格式化食材选项
            function formatIngredient(ingredient) {
                if (ingredient.loading) {
                    return ingredient.text;
                }
                
                if (!ingredient.id) {
                    return ingredient.text;
                }
                
                var $container = $(
                    '<div class="select2-ingredient-option">' +
                    '<span class="ingredient-name">' + ingredient.text + '</span>' +
                    (ingredient.category ? '<span class="ingredient-category badge badge-info">' + ingredient.category + '</span>' : '') +
                    '</div>'
                );
                
                return $container;
            }
            
            // 格式化已选择的食材
            function formatIngredientSelection(ingredient) {
                return ingredient.text || ingredient.name;
            }
            
            // 加载数据
            function loadData($select, selectedValue) {
                // 显示加载中
                $select.empty().append('<option value="">' + settings.loadingMessage + '</option>');
                
                // 加载分类
                $.ajax({
                    url: settings.categoryApiUrl,
                    type: 'GET',
                    dataType: 'json',
                    success: function(categories) {
                        // 加载食材
                        $.ajax({
                            url: settings.apiUrl,
                            type: 'GET',
                            dataType: 'json',
                            success: function(ingredients) {
                                // 清空select
                                $select.empty();
                                
                                // 添加placeholder选项
                                $select.append('<option value="">' + settings.placeholder + '</option>');
                                
                                if (ingredients.length === 0) {
                                    $select.append('<option disabled>' + settings.emptyMessage + '</option>');
                                    return;
                                }
                                
                                // 创建分类映射
                                var categoryMap = {};
                                categories.forEach(function(category) {
                                    categoryMap[category.id] = category.name;
                                });
                                
                                // 按分类组织食材
                                var ingredientsByCategory = {};
                                var uncategorized = [];
                                
                                ingredients.forEach(function(ingredient) {
                                    if (ingredient.category_id && categoryMap[ingredient.category_id]) {
                                        if (!ingredientsByCategory[ingredient.category_id]) {
                                            ingredientsByCategory[ingredient.category_id] = [];
                                        }
                                        ingredientsByCategory[ingredient.category_id].push(ingredient);
                                    } else {
                                        uncategorized.push(ingredient);
                                    }
                                });
                                
                                // 添加分类组和食材选项
                                categories.forEach(function(category) {
                                    if (ingredientsByCategory[category.id] && ingredientsByCategory[category.id].length > 0) {
                                        var $optgroup = $('<optgroup label="' + category.name + '"></optgroup>');
                                        
                                        ingredientsByCategory[category.id].forEach(function(ingredient) {
                                            var $option = $('<option></option>')
                                                .val(ingredient.id)
                                                .text(ingredient.name)
                                                .data('category', category.name);
                                            
                                            $optgroup.append($option);
                                        });
                                        
                                        $select.append($optgroup);
                                    }
                                });
                                
                                // 添加未分类的食材
                                if (uncategorized.length > 0) {
                                    var $optgroup = $('<optgroup label="其他"></optgroup>');
                                    
                                    uncategorized.forEach(function(ingredient) {
                                        var $option = $('<option></option>')
                                            .val(ingredient.id)
                                            .text(ingredient.name);
                                        
                                        $optgroup.append($option);
                                    });
                                    
                                    $select.append($optgroup);
                                }
                                
                                // 设置选中值
                                if (selectedValue) {
                                    $select.val(selectedValue).trigger('change');
                                }
                            },
                            error: function() {
                                $select.empty().append('<option value="">' + settings.placeholder + '</option>');
                                $select.append('<option disabled>加载食材失败</option>');
                            }
                        });
                    },
                    error: function() {
                        $select.empty().append('<option value="">' + settings.placeholder + '</option>');
                        $select.append('<option disabled>加载分类失败</option>');
                    }
                });
            }
        });
    };
})(jQuery);
