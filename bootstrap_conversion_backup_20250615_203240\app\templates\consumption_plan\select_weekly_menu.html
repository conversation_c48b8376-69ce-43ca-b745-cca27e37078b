{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">{{ title }}</h3>
        <div>
            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回消耗计划
            </a>
        </div>
    </div>

    <!-- 当前学校信息 -->
    <div class="card mb-3">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="fas fa-school"></i> {{ current_area.name }}
                    </h5>
                    <small class="text-muted">选择周次来创建消耗计划</small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        只显示本周和下周的菜单
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- 周次选择 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-calendar-week"></i> 选择周次
            </h5>
        </div>
        <div class="card-body">
            <div class="btn-group btn-group-lg" role="group">
                {% for option in week_options %}
                <a href="{{ url_for('consumption_plan.select_weekly_menu', week_offset=option.offset) }}"
                   class="btn {% if option.selected %}btn-primary{% else %}btn-outline-primary{% endif %}
                          {% if not option.has_menu %}disabled{% endif %}">
                    <i class="fas fa-calendar-week"></i> {{ option.name }}
                    <br>
                    <small>{{ option.start_str }} ~ {{ option.end_str }}</small>
                    {% if option.has_menu %}
                    <br><small class="text-success">({{ option.consumption_plans_count }} 个消耗计划)</small>
                    {% else %}
                    <br><small class="text-muted">(无菜单)</small>
                    {% endif %}
                </a>
                {% endfor %}
            </div>

            {% if current_menu %}
            <div class="mt-3">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    已选择：{{ current_menu.week_start.strftime('%Y年第%U周') }}
                    ({{ current_menu.week_start.strftime('%m-%d') }} 至 {{ current_menu.week_end.strftime('%m-%d') }})
                    <a href="{{ url_for('consumption_plan.create_from_weekly', weekly_menu_id=current_menu.id) }}"
                       class="btn btn-sm btn-primary ms-2">
                        <i class="fas fa-plus"></i> 创建消耗计划
                    </a>
                </div>
            </div>
            {% else %}
            <div class="mt-3">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    当前选择的周次还没有菜单，请先创建周菜单。
                    <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-sm btn-warning ms-2">
                        <i class="fas fa-plus"></i> 创建周菜单
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 周菜单详情（如果有选择的菜单） -->
    {% if current_menu %}
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-utensils"></i> 周菜单详情
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-2">
                        <strong>学校：</strong>{{ current_menu.area.name }}
                    </div>
                    <div class="mb-2">
                        <strong>状态：</strong>
                        <span class="badge badge-success">{{ current_menu.status }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>创建时间：</strong>
                        <small class="text-muted">{{ current_menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <div class="mb-3">
                        <strong>创建人：</strong>
                        <small>{{ current_menu.creator.real_name or current_menu.creator.username }}</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="text-center">
                        <h6 class="text-muted">操作选项</h6>
                        <div class="btn-group-vertical">
                            <a href="{{ url_for('weekly_menu_v2.view', id=current_menu.id) }}"
                               class="btn btn-outline-info btn-sm mb-2" target="_blank">
                                <i class="fas fa-eye"></i> 查看完整菜单
                            </a>
                            <a href="{{ url_for('consumption_plan.create_from_weekly', weekly_menu_id=current_menu.id) }}"
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 创建消耗计划
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
