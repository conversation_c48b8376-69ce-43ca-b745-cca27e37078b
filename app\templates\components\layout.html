<!-- Bootstrap 5.3.6 布局组件库 -->
<!-- 使用方法: {% from 'components/layout.html' import page_container, content_section, sidebar_content %} -->

<!-- 标准页面容器 -->
{% macro page_container(fluid=True, class="") %}
<div class="container{% if fluid %}-fluid{% endif %} {{ class }}">
  {{ caller() }}
</div>
{% endmacro %}

<!-- 内容区域包装器 -->
{% macro content_section(title=None, subtitle=None, actions=None, class="") %}
<section class="content-section {{ class }}">
  {% if title or actions %}
  <div class="d-flex justify-content-between align-items-start mb-3">
    {% if title %}
    <div class="flex-grow-1">
      <h1 class="h4 mb-1 fw-semibold text-dark">{{ title }}</h1>
      {% if subtitle %}
      <p class="text-muted mb-0 fs-6">{{ subtitle }}</p>
      {% endif %}
    </div>
    {% endif %}
    
    {% if actions %}
    <div class="flex-shrink-0 ms-3">
      <div class="btn-toolbar gap-2">
        {% for action in actions %}
        <a href="{{ action.url }}" 
           class="btn {{ action.class|default('btn-primary') }} {{ action.size|default('btn-sm') }}"
           {% if action.title %}title="{{ action.title }}"{% endif %}
           {% if action.target %}target="{{ action.target }}"{% endif %}>
          {% if action.icon %}<i class="{{ action.icon }}"></i>{% endif %}
          {% if action.text %}{{ action.text }}{% endif %}
        </a>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  </div>
  {% endif %}
  
  {{ caller() }}
</section>
{% endmacro %}

<!-- 响应式网格布局 -->
{% macro responsive_grid(items, cols="lg-4 md-6", gap="3", class="") %}
<div class="row g-{{ gap }} {{ class }}">
  {% for item in items %}
  <div class="col-{{ cols }}">
    {{ item }}
  </div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 两栏布局 -->
{% macro two_column_layout(left_content, right_content, left_cols="md-8", right_cols="md-4", gap="3") %}
<div class="row g-{{ gap }}">
  <div class="col-{{ left_cols }}">
    {{ left_content }}
  </div>
  <div class="col-{{ right_cols }}">
    {{ right_content }}
  </div>
</div>
{% endmacro %}

<!-- 三栏布局 -->
{% macro three_column_layout(left_content, center_content, right_content, cols="lg-4", gap="3") %}
<div class="row g-{{ gap }}">
  <div class="col-{{ cols }}">
    {{ left_content }}
  </div>
  <div class="col-{{ cols }}">
    {{ center_content }}
  </div>
  <div class="col-{{ cols }}">
    {{ right_content }}
  </div>
</div>
{% endmacro %}

<!-- 侧边栏布局 -->
{% macro sidebar_layout(main_content, sidebar_content, sidebar_position="right", main_cols="md-8", sidebar_cols="md-4") %}
<div class="row g-3">
  {% if sidebar_position == "left" %}
  <div class="col-{{ sidebar_cols }}">
    {{ sidebar_content }}
  </div>
  <div class="col-{{ main_cols }}">
    {{ main_content }}
  </div>
  {% else %}
  <div class="col-{{ main_cols }}">
    {{ main_content }}
  </div>
  <div class="col-{{ sidebar_cols }}">
    {{ sidebar_content }}
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 堆叠布局 (垂直) -->
{% macro stack_layout(items, gap="3", class="") %}
<div class="vstack gap-{{ gap }} {{ class }}">
  {% for item in items %}
  <div>{{ item }}</div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 水平堆叠布局 -->
{% macro hstack_layout(items, gap="3", class="") %}
<div class="hstack gap-{{ gap }} {{ class }}">
  {% for item in items %}
  <div>{{ item }}</div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 居中布局 -->
{% macro center_layout(content, max_width="lg", class="") %}
<div class="row justify-content-center {{ class }}">
  <div class="col-{{ max_width }}">
    {{ content }}
  </div>
</div>
{% endmacro %}

<!-- 全宽布局 -->
{% macro full_width_layout(content, class="") %}
<div class="w-100 {{ class }}">
  {{ content }}
</div>
{% endmacro %}

<!-- 固定比例布局 -->
{% macro ratio_layout(content, ratio="16x9", class="") %}
<div class="ratio ratio-{{ ratio }} {{ class }}">
  {{ content }}
</div>
{% endmacro %}

<!-- 粘性布局 -->
{% macro sticky_layout(content, position="top", offset="0", class="") %}
<div class="sticky-{{ position }} {{ class }}" style="top: {{ offset }};">
  {{ content }}
</div>
{% endmacro %}

<!-- 可滚动区域 -->
{% macro scrollable_area(content, height="300px", class="") %}
<div class="overflow-auto {{ class }}" style="max-height: {{ height }};">
  {{ content }}
</div>
{% endmacro %}

<!-- 响应式显示控制 -->
{% macro responsive_show(content, breakpoints="md", class="") %}
<div class="d-none d-{{ breakpoints }}-block {{ class }}">
  {{ content }}
</div>
{% endmacro %}

{% macro responsive_hide(content, breakpoints="md", class="") %}
<div class="d-{{ breakpoints }}-none {{ class }}">
  {{ content }}
</div>
{% endmacro %}

<!-- 移动端专用布局 -->
{% macro mobile_layout(content, class="") %}
<div class="d-md-none {{ class }}">
  {{ content }}
</div>
{% endmacro %}

<!-- 桌面端专用布局 -->
{% macro desktop_layout(content, class="") %}
<div class="d-none d-md-block {{ class }}">
  {{ content }}
</div>
{% endmacro %}

<!-- 弹性布局 -->
{% macro flex_layout(items, direction="row", justify="start", align="start", wrap=False, gap="0", class="") %}
<div class="d-flex flex-{{ direction }} justify-content-{{ justify }} align-items-{{ align }} {% if wrap %}flex-wrap{% endif %} {% if gap != '0' %}gap-{{ gap }}{% endif %} {{ class }}">
  {% for item in items %}
  <div>{{ item }}</div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 网格布局 (CSS Grid) -->
{% macro grid_layout(items, columns="3", gap="3", class="") %}
<div class="d-grid gap-{{ gap }} {{ class }}" style="grid-template-columns: repeat({{ columns }}, 1fr);">
  {% for item in items %}
  <div>{{ item }}</div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 自适应网格 -->
{% macro auto_grid(items, min_width="250px", gap="3", class="") %}
<div class="d-grid gap-{{ gap }} {{ class }}" style="grid-template-columns: repeat(auto-fit, minmax({{ min_width }}, 1fr));">
  {% for item in items %}
  <div>{{ item }}</div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 紧凑布局包装器 -->
{% macro compact_wrapper(content, padding="2", class="") %}
<div class="p-{{ padding }} {{ class }}">
  {{ content }}
</div>
{% endmacro %}

<!-- 间距控制 -->
{% macro spaced_content(content, margin="3", padding="0", class="") %}
<div class="m-{{ margin }} p-{{ padding }} {{ class }}">
  {{ content }}
</div>
{% endmacro %}

<!-- 使用示例注释 -->
<!--
使用示例:

1. 基本页面布局:
{% call page_container() %}
  {% call content_section(title="页面标题", subtitle="页面描述") %}
    <!-- 页面内容 -->
  {% endcall %}
{% endcall %}

2. 响应式网格:
{{ responsive_grid(items=[card1, card2, card3], cols="lg-4 md-6", gap="3") }}

3. 两栏布局:
{{ two_column_layout(
  left_content=main_content,
  right_content=sidebar_content,
  left_cols="md-8",
  right_cols="md-4"
) }}

4. 移动端/桌面端布局:
{{ desktop_layout(desktop_table) }}
{{ mobile_layout(mobile_cards) }}

5. 弹性布局:
{{ flex_layout(
  items=[item1, item2, item3],
  direction="row",
  justify="between",
  align="center",
  gap="3"
) }}

6. 自适应网格:
{{ auto_grid(items=card_list, min_width="300px", gap="3") }}
-->
