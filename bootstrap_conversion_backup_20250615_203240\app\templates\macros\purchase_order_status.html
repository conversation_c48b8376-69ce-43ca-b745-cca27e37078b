{# 采购订单状态相关的宏定义 #}

{# 显示统一的订单状态标签 #}
{% macro render_order_status(order, status_info) %}
    {% set unified_status = get_unified_status(order, status_info) %}
    {% set status_class = get_status_class(unified_status) %}
    
    <span class="badge {{ status_class }} status-badge">{{ unified_status }}</span>
{% endmacro %}

{# 显示订单操作按钮 #}
{% macro render_order_actions(order, status_info, size='sm') %}
    {% set unified_status = get_unified_status(order, status_info) %}
    
    <div class="order-actions">
        {% if unified_status == '待确认' %}
            <button type="button" class="btn btn-outline-success btn-{{ size }} confirm-btn" data-id="{{ order.id }}" title="确认订单">
                <i class="fas fa-check"></i> 确认
            </button>
            <button type="button" class="btn btn-outline-danger btn-{{ size }} cancel-btn" data-id="{{ order.id }}" title="取消订单">
                <i class="fas fa-times"></i> 取消
            </button>
        {% elif unified_status == '已确认' %}
            <button type="button" class="btn btn-outline-info btn-{{ size }} deliver-btn" data-id="{{ order.id }}" title="标记送达">
                <i class="fas fa-truck"></i> 送达
            </button>
            {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
            <button type="button" class="btn btn-outline-danger btn-{{ size }} cancel-btn" data-id="{{ order.id }}" title="取消订单">
                <i class="fas fa-times"></i> 取消
            </button>
            {% endif %}
        {% elif unified_status == '准备入库' %}
            {% if not status_info.has_stock_in %}
                <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}" class="btn btn-outline-success btn-{{ size }}" title="创建入库单">
                    <i class="fas fa-dolly"></i> 入库
                </a>
            {% else %}
                <a href="{{ url_for('stock_in.view_details', id=status_info.stock_in_id) }}" class="btn btn-outline-info btn-{{ size }}" title="查看入库单">
                    <i class="fas fa-clipboard-list"></i> 入库单
                </a>
            {% endif %}
            {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
            <button type="button" class="btn btn-outline-danger btn-{{ size }} cancel-btn" data-id="{{ order.id }}" title="取消订单">
                <i class="fas fa-times"></i> 取消
            </button>
            {% endif %}
        {% elif unified_status == '入库已完成' %}
            <span class="btn btn-outline-success btn-{{ size }} disabled" title="入库已完成">
                <i class="fas fa-check-circle"></i> 入库已完成
            </span>
        {% elif unified_status == '已取消' %}
            {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
            <button type="button" class="btn btn-outline-danger btn-{{ size }} delete-btn" data-id="{{ order.id }}" title="删除订单">
                <i class="fas fa-trash"></i> 删除
            </button>
            {% endif %}
        {% endif %}
        
        {# 打印按钮 - 除了待确认状态都显示 #}
        {% if unified_status != '待确认' %}
        <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}" class="btn btn-outline-secondary btn-{{ size }}" target="_blank" title="打印订单">
            <i class="fas fa-print"></i>{% if size != 'sm' %} 打印{% endif %}
        </a>
        {% endif %}
    </div>
{% endmacro %}

{# 显示移动端操作按钮 #}
{% macro render_mobile_actions(order, status_info) %}
    {% set unified_status = get_unified_status(order, status_info) %}
    
    <div class="mobile-actions d-flex">
        {# 查看详情按钮 #}
        <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-eye"></i>
        </a>
        
        {% if unified_status == '待确认' %}
            <button type="button" class="btn btn-outline-success confirm-btn" data-id="{{ order.id }}">
                <i class="fas fa-check"></i>
            </button>
            <button type="button" class="btn btn-outline-danger cancel-btn" data-id="{{ order.id }}">
                <i class="fas fa-times"></i>
            </button>
        {% elif unified_status == '已确认' %}
            <button type="button" class="btn btn-outline-info deliver-btn" data-id="{{ order.id }}">
                <i class="fas fa-truck"></i>
            </button>
        {% elif unified_status == '准备入库' %}
            {% if not status_info.has_stock_in %}
                <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}" class="btn btn-outline-success">
                    <i class="fas fa-dolly"></i>
                </a>
            {% else %}
                <a href="{{ url_for('stock_in.view_details', id=status_info.stock_in_id) }}" class="btn btn-outline-info">
                    <i class="fas fa-clipboard-list"></i>
                </a>
            {% endif %}
        {% elif unified_status == '入库已完成' %}
            <span class="btn btn-outline-success disabled" title="入库已完成">
                <i class="fas fa-check-circle"></i>
            </span>
        {% endif %}
        
        {# 打印按钮 #}
        {% if unified_status != '待确认' %}
        <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}" class="btn btn-outline-secondary" target="_blank">
            <i class="fas fa-print"></i>
        </a>
        {% endif %}
    </div>
{% endmacro %}

{# 获取统一状态的辅助函数 #}
{% macro get_unified_status(order, status_info) -%}
{%- if status_info.has_stock_in and status_info.stock_in_status in ['已入库', '已审核'] -%}
入库已完成
{%- elif status_info.has_stock_in and status_info.stock_in_status == '已取消' -%}
准备入库
{%- else -%}
{{ order.status }}
{%- endif -%}
{% endmacro %}

{# 获取状态样式类的辅助函数 #}
{% macro get_status_class(status) %}
    {% if status == '待确认' %}
        badge-warning
    {% elif status == '已确认' %}
        badge-info
    {% elif status == '准备入库' %}
        badge-primary
    {% elif status == '入库已完成' %}
        badge-success
    {% elif status == '已取消' %}
        badge-danger
    {% else %}
        badge-secondary
    {% endif %}
{% endmacro %}

{# 显示状态进度条 #}
{% macro render_status_progress(order, status_info) %}
    {% set unified_status = get_unified_status(order, status_info) %}
    {% set progress_steps = ['待确认', '已确认', '准备入库', '入库已完成'] %}
    {% set current_step = progress_steps.index(unified_status) if unified_status in progress_steps else -1 %}
    
    <div class="status-progress">
        {% for step in progress_steps %}
            {% set step_index = loop.index0 %}
            {% set is_active = step_index <= current_step %}
            {% set is_current = step_index == current_step %}
            
            <div class="progress-step {{ 'active' if is_active else '' }} {{ 'current' if is_current else '' }}">
                <div class="step-icon">
                    {% if step == '待确认' %}
                        <i class="fas fa-clock"></i>
                    {% elif step == '已确认' %}
                        <i class="fas fa-check"></i>
                    {% elif step == '准备入库' %}
                        <i class="fas fa-truck"></i>
                    {% elif step == '入库已完成' %}
                        <i class="fas fa-warehouse"></i>
                    {% endif %}
                </div>
                <div class="step-label">{{ step }}</div>
            </div>
            
            {% if not loop.last %}
            <div class="progress-line {{ 'active' if step_index < current_step else '' }}"></div>
            {% endif %}
        {% endfor %}
    </div>
{% endmacro %}

{# 显示状态时间线 #}
{% macro render_status_timeline(order) %}
    <div class="status-timeline">
        <div class="timeline-item">
            <div class="timeline-icon bg-primary">
                <i class="fas fa-plus"></i>
            </div>
            <div class="timeline-content">
                <h6>订单创建</h6>
                <p class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M') if order.created_at else '-' }}</p>
            </div>
        </div>
        
        {% if order.confirmed_at %}
        <div class="timeline-item">
            <div class="timeline-icon bg-info">
                <i class="fas fa-check"></i>
            </div>
            <div class="timeline-content">
                <h6>订单确认</h6>
                <p class="text-muted">{{ order.confirmed_at.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
        </div>
        {% endif %}
        
        {% if order.delivered_at %}
        <div class="timeline-item">
            <div class="timeline-icon bg-primary">
                <i class="fas fa-truck"></i>
            </div>
            <div class="timeline-content">
                <h6>货物送达</h6>
                <p class="text-muted">{{ order.delivered_at.strftime('%Y-%m-%d %H:%M') }}</p>
                {% if order.delivery_notes %}
                <small class="text-muted">备注：{{ order.delivery_notes }}</small>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        {% if order.cancelled_at %}
        <div class="timeline-item">
            <div class="timeline-icon bg-danger">
                <i class="fas fa-times"></i>
            </div>
            <div class="timeline-content">
                <h6>订单取消</h6>
                <p class="text-muted">{{ order.cancelled_at.strftime('%Y-%m-%d %H:%M') }}</p>
                {% if order.cancel_reason %}
                <small class="text-muted">原因：{{ order.cancel_reason }}</small>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
{% endmacro %}
