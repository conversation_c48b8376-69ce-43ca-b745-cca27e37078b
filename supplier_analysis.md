
# Bootstrap 5.3.6 重构报告

## 总体统计
- 处理文件数: 13
- 修改文件数: 0
- 错误文件数: 0

## 详细结果

### app\templates\supplier\category_form.html
- 复杂度评分: 0
- 发现问题: 0

### app\templates\supplier\category_index.html
- 复杂度评分: 11
- 发现问题: 5
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\certificate_form.html
- 复杂度评分: 7
- 发现问题: 3
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\certificate_index.html
- 复杂度评分: 13
- 发现问题: 5
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\certificate_view.html
- 复杂度评分: 12
- 发现问题: 5
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\form.html
- 复杂度评分: 13
- 发现问题: 4
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\index.html
- 复杂度评分: 15
- 发现问题: 5
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\product_form.html
- 复杂度评分: 16
- 发现问题: 4
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\product_index.html
- 复杂度评分: 23
- 发现问题: 5
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\product_view.html
- 复杂度评分: 18
- 发现问题: 5
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\school_form.html
- 复杂度评分: 10
- 发现问题: 4
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\school_index.html
- 复杂度评分: 16
- 发现问题: 5
  - 发现Bootstrap 4类名: close
  - 发现jQuery使用: \$\(
  - 发现jQuery使用: \.ajax\(
  - 发现jQuery使用: \.click\(
  - 发现jQuery使用: \.ready\(

### app\templates\supplier\view.html
- 复杂度评分: 6
- 发现问题: 0
