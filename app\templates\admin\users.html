{% extends 'admin/base_admin.html' %}

{% block page_title %}用户管理{% endblock %}

{% block toolbar_actions %}
<a href="{{ url_for('system.add_user') }}" class="btn btn-primary">
    <i class="fas fa-plus"></i> 添加用户
</a>
<a href="{{ url_for('system.roles') }}" class="btn btn-info">
    <i class="fas fa-users-cog"></i> 角色管理
</a>
{% endblock %}

{% block content_body %}
<!-- 批量操作卡片 -->
<div class="card mb-3">
    <div class="card-header py-2">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">批量操作</h6>
            <div>
                <button id="selectAllBtn" class="btn btn-sm btn-outline-secondary">全选</button>
                <button id="deselectAllBtn" class="btn btn-sm btn-outline-secondary">取消全选</button>
            </div>
        </div>
    </div>
    <div class="card-body py-2">
        <div class="row g-2">
            <div class="col-md-8">
                <select id="batchAction" class="form-select form-select-sm">
                    <option value="">-- 请选择操作 --</option>
                    <option value="assign_roles">批量分配角色</option>
                    {% if current_user.is_admin() or current_user.has_permission('user', 'delete') %}
                    <option value="delete">批量删除用户</option>
                    {% endif %}
                </select>
            </div>
            <div class="col-md-4">
                <button id="applyBatchAction" class="btn btn-primary w-100 btn-sm" disabled>
                    <i class="fas fa-check"></i> 应用操作
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表卡片 -->
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover table-sm mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="40px">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </div>
                        </th>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>真实姓名</th>
                        <th>电子邮箱</th>
                        <th>所属区域</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th width="120px">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input user-checkbox" 
                                       id="user-{{ user.id }}" value="{{ user.id }}">
                            </div>
                        </td>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.real_name or '-' }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.area %}
                            <span class="badge bg-info">{{ user.area.get_level_name() }}</span>
                            {{ user.area.name }}
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                        <td>
                            {% for role in user.roles %}
                            <span class="badge bg-primary">{{ role.name }}</span>
                            {% else %}
                            <span class="text-muted">无角色</span>
                            {% endfor %}
                        </td>
                        <td>
                            {% if user.status == 1 %}
                            <span class="badge bg-success">启用</span>
                            {% else %}
                            <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('system.view_user', id=user.id) }}" 
                                   class="btn btn-outline-info" title="查看">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('system.edit_user', id=user.id) }}" 
                                   class="btn btn-outline-primary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if current_user.is_admin() or current_user.has_permission('user', 'delete') %}
                                <button type="button" class="btn btn-outline-danger" title="删除"
                                        onclick="confirmAction('确定要删除用户 {{ user.username }} 吗？', () => deleteUser({{ user.id }}))">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center py-3">暂无用户数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
{% if users.pages > 1 %}
<nav class="mt-3">
    <ul class="pagination justify-content-center">
        {% if users.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('system.users', page=users.prev_num) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}
        
        {% for page in users.iter_pages() %}
            {% if page %}
                <li class="page-item {% if page == users.page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('system.users', page=page) }}">{{ page }}</a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if users.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('system.users', page=users.next_num) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// 全选/取消全选
document.getElementById('selectAll').addEventListener('change', function() {
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBatchActionButton();
});

document.getElementById('selectAllBtn').addEventListener('click', function() {
    document.getElementById('selectAll').checked = true;
    document.getElementById('selectAll').dispatchEvent(new Event('change'));
});

document.getElementById('deselectAllBtn').addEventListener('click', function() {
    document.getElementById('selectAll').checked = false;
    document.getElementById('selectAll').dispatchEvent(new Event('change'));
});

// 更新批量操作按钮状态
function updateBatchActionButton() {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const batchAction = document.getElementById('batchAction');
    const applyButton = document.getElementById('applyBatchAction');
    
    applyButton.disabled = checkedBoxes.length === 0 || !batchAction.value;
}

// 监听复选框变化
document.querySelectorAll('.user-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBatchActionButton);
});

// 监听批量操作选择
document.getElementById('batchAction').addEventListener('change', updateBatchActionButton);

// 应用批量操作
document.getElementById('applyBatchAction').addEventListener('click', async function() {
    const action = document.getElementById('batchAction').value;
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked'))
        .map(checkbox => checkbox.value);
    
    if (selectedUsers.length === 0) return;
    
    try {
        if (action === 'delete') {
            if (!confirm(`确定要删除选中的 ${selectedUsers.length} 个用户吗？`)) return;
            
            const response = await makeRequest('/api/users/batch-delete', 'POST', {
                user_ids: selectedUsers
            });
            
            if (response.success) {
                location.reload();
            } else {
                alert(response.message || '删除失败');
            }
        } else if (action === 'assign_roles') {
            // 实现角色分配逻辑
        }
    } catch (error) {
        console.error('操作失败:', error);
        alert('操作失败，请重试');
    }
});

// 删除用户
async function deleteUser(userId) {
    try {
        const response = await makeRequest(`/api/users/${userId}`, 'DELETE');
        if (response.success) {
            location.reload();
        } else {
            alert(response.message || '删除失败');
        }
    } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败，请重试');
    }
}
</script>
{% endblock %}