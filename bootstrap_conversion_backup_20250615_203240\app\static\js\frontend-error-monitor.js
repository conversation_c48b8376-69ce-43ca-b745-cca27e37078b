/**
 * 前端错误监控和自动修复脚本
 * 用于检测和修复常见的前端错误
 */

(function() {
    'use strict';
    
    // 错误监控配置
    const ErrorMonitor = {
        errors: [],
        maxErrors: 50,
        
        // 初始化错误监控
        init: function() {
            this.setupGlobalErrorHandler();
            this.setupUnhandledRejectionHandler();
            this.checkDependencies();
            this.monitorResourceLoading();
            
            console.log('🔍 前端错误监控已启动');
        },
        
        // 设置全局错误处理器
        setupGlobalErrorHandler: function() {
            window.addEventListener('error', (event) => {
                this.logError({
                    type: 'JavaScript Error',
                    message: event.error ? event.error.message : event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error ? event.error.stack : null,
                    timestamp: new Date().toISOString()
                });
                
                // 尝试自动修复常见错误
                this.attemptAutoFix(event);
            });
        },
        
        // 设置未处理的 Promise 拒绝处理器
        setupUnhandledRejectionHandler: function() {
            window.addEventListener('unhandledrejection', (event) => {
                this.logError({
                    type: 'Unhandled Promise Rejection',
                    message: event.reason ? event.reason.toString() : 'Unknown rejection',
                    timestamp: new Date().toISOString()
                });
            });
        },
        
        // 检查依赖项
        checkDependencies: function() {
            const dependencies = [
                { name: 'jQuery', check: () => typeof $ !== 'undefined' },
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
                { name: 'Chart.js', check: () => typeof Chart !== 'undefined' }
            ];
            
            dependencies.forEach(dep => {
                if (!dep.check()) {
                    this.logError({
                        type: 'Dependency Missing',
                        message: `${dep.name} is not loaded`,
                        timestamp: new Date().toISOString()
                    });
                    
                    // 尝试重新加载依赖
                    this.reloadDependency(dep.name);
                } else {
                    console.log(`✅ ${dep.name} 已正确加载`);
                }
            });
        },
        
        // 监控资源加载
        monitorResourceLoading: function() {
            // 监控图片加载失败
            document.addEventListener('error', (event) => {
                if (event.target.tagName === 'IMG') {
                    this.logError({
                        type: 'Image Load Error',
                        message: `Failed to load image: ${event.target.src}`,
                        timestamp: new Date().toISOString()
                    });
                    
                    // 设置默认图片
                    event.target.src = '/static/images/placeholder.png';
                }
            }, true);
            
            // 监控脚本加载失败
            document.addEventListener('error', (event) => {
                if (event.target.tagName === 'SCRIPT') {
                    this.logError({
                        type: 'Script Load Error',
                        message: `Failed to load script: ${event.target.src}`,
                        timestamp: new Date().toISOString()
                    });
                }
            }, true);
        },
        
        // 记录错误
        logError: function(error) {
            this.errors.push(error);
            
            // 限制错误数量
            if (this.errors.length > this.maxErrors) {
                this.errors.shift();
            }
            
            // 输出到控制台
            console.error(`🚨 ${error.type}:`, error.message);
            
            // 可以发送到服务器进行分析
            // this.sendErrorToServer(error);
        },
        
        // 尝试自动修复
        attemptAutoFix: function(event) {
            const message = event.error ? event.error.message : event.message;
            
            // 修复 jQuery 未定义错误
            if (message.includes('$ is not defined')) {
                console.log('🔧 尝试修复 jQuery 未定义错误...');
                this.loadJQuery();
            }
            
            // 修复递归调用错误
            if (message.includes('Maximum call stack size exceeded')) {
                console.log('🔧 检测到递归错误，尝试修复...');
                this.fixRecursionError();
            }
        },
        
        // 重新加载依赖
        reloadDependency: function(depName) {
            switch(depName) {
                case 'jQuery':
                    this.loadJQuery();
                    break;
                case 'Bootstrap':
                    this.loadBootstrap();
                    break;
                case 'Chart.js':
                    this.loadChartJS();
                    break;
            }
        },
        
        // 加载 jQuery
        loadJQuery: function() {
            if (typeof $ === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://code.jquery.com/jquery-3.7.1.min.js';
                script.onload = () => {
                    console.log('✅ jQuery 重新加载成功');
                };
                script.onerror = () => {
                    console.error('❌ jQuery 重新加载失败');
                };
                document.head.appendChild(script);
            }
        },
        
        // 加载 Bootstrap
        loadBootstrap: function() {
            if (typeof bootstrap === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                script.onload = () => {
                    console.log('✅ Bootstrap 重新加载成功');
                };
                document.head.appendChild(script);
            }
        },
        
        // 加载 Chart.js
        loadChartJS: function() {
            if (typeof Chart === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js';
                script.onload = () => {
                    console.log('✅ Chart.js 重新加载成功');
                };
                document.head.appendChild(script);
            }
        },
        
        // 修复递归错误
        fixRecursionError: function() {
            // 清除可能导致递归的事件监听器
            const elements = document.querySelectorAll('[data-upload-fixed]');
            elements.forEach(el => {
                const newEl = el.cloneNode(true);
                el.parentNode.replaceChild(newEl, el);
            });
            
            console.log('🔧 已清除可能导致递归的事件监听器');
        },
        
        // 获取错误报告
        getErrorReport: function() {
            return {
                totalErrors: this.errors.length,
                errors: this.errors,
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString()
            };
        },
        
        // 清除错误日志
        clearErrors: function() {
            this.errors = [];
            console.log('🧹 错误日志已清除');
        },
        
        // 发送错误到服务器（可选）
        sendErrorToServer: function(error) {
            // 这里可以实现发送错误到服务器的逻辑
            // fetch('/api/log-error', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(error)
            // });
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            ErrorMonitor.init();
        });
    } else {
        ErrorMonitor.init();
    }
    
    // 将错误监控器暴露到全局，方便调试
    window.ErrorMonitor = ErrorMonitor;
    
    // 添加一些有用的调试方法
    window.debugFrontend = function() {
        console.log('🔍 前端调试信息:');
        console.log('jQuery:', typeof $ !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
        console.log('Bootstrap:', typeof bootstrap !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
        console.log('Chart.js:', typeof Chart !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
        console.log('错误数量:', ErrorMonitor.errors.length);
        
        if (ErrorMonitor.errors.length > 0) {
            console.log('最近的错误:', ErrorMonitor.errors.slice(-5));
        }
    };
    
})();
