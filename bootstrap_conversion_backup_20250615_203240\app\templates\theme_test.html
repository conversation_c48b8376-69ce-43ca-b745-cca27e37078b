{% extends "base.html" %}

{% block title %}主题切换功能测试{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-palette"></i> 增强版主题切换器测试
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 功能说明</h5>
                        <ul class="mb-0">
                            <li><strong>主题切换面板</strong>：现在向右侧打开，提供更好的用户体验</li>
                            <li><strong>主题收藏</strong>：点击主题选项右侧的星星图标可以收藏喜欢的主题</li>
                            <li><strong>预览模式</strong>：开启后点击主题会预览3秒，然后自动恢复</li>
                            <li><strong>自动切换</strong>：根据时间自动切换合适的主题</li>
                            <li><strong>主题统计</strong>：查看主题使用情况和获得推荐</li>
                            <li><strong>设置导入导出</strong>：备份和恢复主题设置</li>
                            <li><strong>快捷键</strong>：Ctrl+Alt+T 打开主题面板，Ctrl+Alt+P 切换预览模式</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>测试按钮</h5>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-primary mb-2" onclick="testThemeSwitch()">
                                        <i class="fas fa-sync"></i> 测试主题切换
                                    </button>
                                    <button class="btn btn-secondary mb-2" onclick="testPreviewMode()">
                                        <i class="fas fa-eye"></i> 测试预览模式
                                    </button>
                                    <button class="btn btn-success mb-2" onclick="testAutoTheme()">
                                        <i class="fas fa-clock"></i> 测试自动主题
                                    </button>
                                    <button class="btn btn-info mb-2" onclick="showThemeStats()">
                                        <i class="fas fa-chart-bar"></i> 显示统计
                                    </button>
                                    <button class="btn btn-warning mb-2" onclick="testFavorites()">
                                        <i class="fas fa-star"></i> 测试收藏功能
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>当前状态</h5>
                                </div>
                                <div class="card-body">
                                    <div id="currentStatus">
                                        <p><strong>当前主题：</strong> <span id="currentTheme">-</span></p>
                                        <p><strong>收藏主题：</strong> <span id="favoriteThemes">-</span></p>
                                        <p><strong>预览模式：</strong> <span id="previewMode">-</span></p>
                                        <p><strong>自动切换：</strong> <span id="autoTheme">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>主题效果展示</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="alert alert-primary">
                                                <h6>主要色彩</h6>
                                                <p>这是主要色彩的展示效果</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="alert alert-success">
                                                <h6>成功色彩</h6>
                                                <p>这是成功色彩的展示效果</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="alert alert-warning">
                                                <h6>警告色彩</h6>
                                                <p>这是警告色彩的展示效果</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-primary">主要按钮</button>
                                        <button type="button" class="btn btn-secondary">次要按钮</button>
                                        <button type="button" class="btn btn-success">成功按钮</button>
                                        <button type="button" class="btn btn-danger">危险按钮</button>
                                        <button type="button" class="btn btn-warning">警告按钮</button>
                                        <button type="button" class="btn btn-info">信息按钮</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 测试函数
function testThemeSwitch() {
    const themes = ['primary', 'secondary', 'success', 'warning', 'info', 'danger'];
    const randomTheme = themes[Math.floor(Math.random() * themes.length)];
    
    if (window.enhancedThemeSwitcher) {
        window.enhancedThemeSwitcher.switchTheme(randomTheme);
    } else if (window.switchTheme) {
        window.switchTheme(randomTheme);
    }
    
    updateStatus();
}

function testPreviewMode() {
    if (window.enhancedThemeSwitcher) {
        window.enhancedThemeSwitcher.togglePreviewMode();
    }
    updateStatus();
}

function testAutoTheme() {
    if (window.enhancedThemeSwitcher) {
        window.enhancedThemeSwitcher.toggleAutoTheme();
    }
    updateStatus();
}

function showThemeStats() {
    if (window.enhancedThemeSwitcher) {
        window.enhancedThemeSwitcher.showThemeStats();
    }
}

function testFavorites() {
    const themes = ['primary', 'success', 'dark-neon'];
    themes.forEach(theme => {
        if (window.enhancedThemeSwitcher) {
            window.enhancedThemeSwitcher.toggleFavorite(theme);
        }
    });
    updateStatus();
}

function updateStatus() {
    setTimeout(() => {
        const currentTheme = document.body.getAttribute('data-theme') || 'unknown';
        const favoriteThemes = JSON.parse(localStorage.getItem('favorite-themes') || '[]');
        const previewMode = window.enhancedThemeSwitcher ? window.enhancedThemeSwitcher.previewMode : false;
        const autoTheme = localStorage.getItem('auto-theme') === 'true';
        
        document.getElementById('currentTheme').textContent = currentTheme;
        document.getElementById('favoriteThemes').textContent = favoriteThemes.length > 0 ? favoriteThemes.join(', ') : '无';
        document.getElementById('previewMode').textContent = previewMode ? '开启' : '关闭';
        document.getElementById('autoTheme').textContent = autoTheme ? '开启' : '关闭';
    }, 100);
}

// 页面加载时更新状态
document.addEventListener('DOMContentLoaded', function() {
    updateStatus();
    
    // 监听主题变化
    window.addEventListener('themeChanged', updateStatus);
    
    // 定期更新状态
    setInterval(updateStatus, 2000);
});
</script>
{% endblock %}
