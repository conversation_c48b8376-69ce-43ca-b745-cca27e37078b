2025-06-15 20:19:54,575 INFO: 应用启动 - PID: 4840 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-15 20:21:23,408 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-15 20:21:23,441 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-15 20:23:09,089 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-15 20:23:09,111 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-15 20:23:56,354 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-15 20:23:56,366 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-15 20:24:01,906 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-15 20:24:01,927 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-15 20:26:36,125 INFO: 当前用户: 18373062333 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-15 20:26:36,125 INFO: 用户区域ID: 42 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-15 20:26:36,126 INFO: 用户区域名称: 朝阳区实验中学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-15 20:26:36,126 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-15 20:27:07,363 INFO: 使用消耗计划的区域ID: 42 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-15 20:27:07,363 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-03, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-15 20:27:07,363 INFO: 查询周菜单：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-15 20:27:07,365 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-15 20:27:07,365 INFO:   - 周菜单ID: 37, 开始日期: 2025-06-02, 结束日期: 2025-06-08, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-15 20:27:07,366 INFO: 周菜单 37 总共有 77 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-15 20:27:07,366 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,367 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,367 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,367 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,367 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,367 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,368 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,368 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,368 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,368 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,369 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,369 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,369 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,370 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,370 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,370 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,371 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,371 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,371 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,371 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,371 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,372 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,372 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,372 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,372 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,372 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,372 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,373 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,374 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,375 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=371 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,376 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=367 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=370 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=153 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=152 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=154 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,377 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,378 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=369 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,378 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,378 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,378 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=368 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-15 20:27:07,382 INFO: 匹配条件 day_of_week=2, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-15 20:27:07,382 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-15 20:27:07,382 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-15 20:27:07,387 INFO: 步骤1: 读取消耗日期: 2025-06-03, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-15 20:27:07,393 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-15 20:27:07,395 INFO: 步骤2: 为出库食材 '鸡肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,396 INFO: 步骤2: 为出库食材 '羊肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,396 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,397 INFO: 步骤2: 为出库食材 '莲藕' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,398 INFO: 步骤2: 为出库食材 '韭菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,399 INFO: 步骤2: 为出库食材 '木耳' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,399 INFO: 步骤2: 为出库食材 '蒜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,400 INFO: 步骤2: 为出库食材 '吐司' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,402 INFO: 步骤2: 为出库食材 '芹菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,403 INFO: 步骤2: 为出库食材 '南瓜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,405 INFO: 步骤2: 为出库食材 '黄瓜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,406 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,406 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-15 20:27:07,406 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
