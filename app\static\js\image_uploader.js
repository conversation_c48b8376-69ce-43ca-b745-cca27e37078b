/**
 * 图片上传器
 * 提供图片上传、评分和管理功能
 */
class ImageUploader {
    /**
     * 构造函数
     * @param {HTMLElement} container - 容器元素
     * @param {Object} options - 配置选项
     */
    constructor(container, options) {
        this.container = container;
        this.options = Object.assign({
            referenceType: '',
            referenceId: '',
            apiBaseUrl: '/daily-management/image-api',
            maxWidth: 1200,
            quality: 0.8
        }, options);

        // 从容器中获取引用类型和ID（如果未在选项中提供）
        if (!this.options.referenceType && this.container.dataset.referenceType) {
            this.options.referenceType = this.container.dataset.referenceType;
        }
        if (!this.options.referenceId && this.container.dataset.referenceId) {
            this.options.referenceId = this.container.dataset.referenceId;
        }

        // 存储事件监听器引用，以便销毁时移除
        this.eventListeners = [];

        // 初始化
        this.init();
    }

    /**
     * 销毁实例，移除事件监听器
     */
    destroy() {
        // 移除所有事件监听器
        this.eventListeners.forEach(listener => {
            const { element, event, callback } = listener;
            element.removeEventListener(event, callback);
        });

        // 清空事件监听器数组
        this.eventListeners = [];

        console.log(`销毁图片上传组件: ${this.options.referenceType}-${this.options.referenceId}`);
    }

    /**
     * 初始化
     */
    init() {
        // 添加样式
        this.addStyles();

        // 初始化添加图片按钮
        this.initAddButton();

        // 加载图片列表
        this.loadImages();

        // 创建预览模态框（如果不存在）
        this.createPreviewModal();
    }

    /**
     * 添加样式
     */
    addStyles() {
        // 检查是否已添加样式
        if (document.getElementById('image-uploader-styles')) {
            return;
        }

        // 创建样式元素
        const style = document.createElement('style');
        style.id = 'image-uploader-styles';
        style.textContent = `
            .image-gallery {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                margin-bottom: 15px;
            }
            .image-container {
                width: 200px;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                position: relative;
            }
            .inspection-image {
                width: 100%;
                height: 150px;
                object-fit: cover;
                border-radius: 3px;
                cursor: pointer;
            }
            .rating {
                text-align: center;
                margin: 5px 0;
                font-size: 20px;
            }
            .star {
                color: #ccc;
                cursor: pointer;
            }
            .star.active {
                color: #ffc107;
            }
            .image-actions {
                display: flex;
                justify-content: space-between;
                margin-top: 5px;
            }
            .add-image-container {
                width: 200px;
                height: 200px;
                border: 2px dashed #ddd;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .add-image-btn {
                background: none;
                border: none;
                color: #6c757d;
                display: flex;
                flex-direction: column;
                align-items: center;
                cursor: pointer;
            }
            .add-image-btn i {
                font-size: 24px;
                margin-bottom: 5px;
            }
            .loading-indicator {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                border-radius: 4px;
            }
            .upload-progress {
                width: 80%;
                margin-top: 10px;
            }
            .image-preview-modal .modal-body {
                text-align: center;
            }
            .image-preview-modal img {
                max-width: 100%;
                max-height: 70vh;
            }
        `;

        // 添加到文档头部
        document.head.appendChild(style);
    }

    /**
     * 初始化添加图片按钮
     */
    initAddButton() {
        const addButton = this.container.querySelector('.add-image-btn');
        const fileInput = this.container.querySelector('.file-input');

        if (!addButton || !fileInput) {
            console.error('找不到添加图片按钮或文件输入框');
            return;
        }

        // 标记是否正在上传，避免重复触发
        let isUploading = false;

        // 使用自定义的addEventListener方法添加事件监听器
        this.addEventListener(addButton, 'click', () => {
            // 如果正在上传，不再触发文件选择
            if (isUploading) {
                console.log('正在上传中，请等待...');
                return;
            }
            fileInput.click();
        });

        this.addEventListener(fileInput, 'change', async (e) => {
            if (e.target.files.length > 0 && !isUploading) {
                try {
                    // 设置上传标志
                    isUploading = true;

                    // 禁用添加按钮
                    addButton.disabled = true;
                    addButton.style.opacity = '0.5';
                    addButton.style.cursor = 'not-allowed';

                    // 上传图片
                    await this.uploadImage(e.target.files[0]);
                } catch (error) {
                    console.error('上传图片失败:', error);
                } finally {
                    // 清空文件输入
                    fileInput.value = '';

                    // 重置上传标志
                    isUploading = false;

                    // 恢复添加按钮
                    addButton.disabled = false;
                    addButton.style.opacity = '1';
                    addButton.style.cursor = 'pointer';
                }
            }
        });
    }

    /**
     * 添加事件监听器并跟踪它
     * @param {HTMLElement} element - 要添加事件监听器的元素
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    addEventListener(element, event, callback) {
        // 添加事件监听器
        element.addEventListener(event, callback);

        // 跟踪事件监听器
        this.eventListeners.push({
            element,
            event,
            callback
        });
    }

    /**
     * 加载图片列表
     */
    async loadImages() {
        if (!this.options.referenceType || !this.options.referenceId) {
            console.error('缺少引用类型或ID');
            return;
        }

        try {
            const response = await fetch(`${this.options.apiBaseUrl}/list?reference_type=${this.options.referenceType}&reference_id=${this.options.referenceId}`);
            const data = await response.json();

            if (data.success) {
                // 清除现有图片（保留添加按钮）
                const addContainer = this.container.querySelector('.add-image-container');
                const existingImages = this.container.querySelectorAll('.image-container');
                existingImages.forEach(img => img.remove());

                // 添加图片 - 按照上传时间倒序排列，最新的在最左边

                // 先移动添加按钮到最左侧
                if (addContainer && addContainer.parentNode) {
                    addContainer.parentNode.insertBefore(addContainer, addContainer.parentNode.firstChild);
                }

                // 然后添加所有图片到添加按钮的右侧
                data.photos.forEach(photo => {
                    const imageContainer = this.createImageContainer(photo);
                    if (addContainer && addContainer.nextSibling) {
                        addContainer.parentNode.insertBefore(imageContainer, addContainer.nextSibling);
                    } else {
                        this.container.appendChild(imageContainer);
                    }
                });
            }
        } catch (error) {
            console.error('加载图片失败:', error);
        }
    }

    /**
     * 上传图片
     * @param {File} file - 图片文件
     */
    async uploadImage(file) {
        const addContainer = this.container.querySelector('.add-image-container');

        // 创建加载指示器
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = `
            <div>上传中...</div>
            <div class="progress upload-progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
        `;
        addContainer.appendChild(loadingIndicator);

        try {
            // 直接上传原始图片，不进行前端压缩
            const formData = new FormData();
            formData.append('photo', file);
            formData.append('reference_type', this.options.referenceType);
            formData.append('reference_id', this.options.referenceId);

            const xhr = new XMLHttpRequest();
            xhr.open('POST', `${this.options.apiBaseUrl}/upload`, true);

            // 添加CSRF令牌
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                xhr.setRequestHeader('X-CSRFToken', csrfToken.getAttribute('content'));
            }

            // 进度监听
            xhr.upload.onprogress = (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    loadingIndicator.querySelector('.progress-bar').style.width = `${percentComplete}%`;
                    loadingIndicator.querySelector('div').textContent = `上传中: ${Math.round(percentComplete)}%`;
                }
            };

            xhr.onload = () => {
                try {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            // 创建图片容器
                            const imageContainer = this.createImageContainer({
                                id: response.id,
                                file_path: response.file_path,
                                rating: response.rating
                            });

                            // 插入到图片库 - 放在添加按钮的右侧
                            addContainer.parentNode.insertBefore(imageContainer, addContainer.nextSibling);

                            console.log('图片上传成功:', response);
                        } else {
                            console.error('上传失败:', response.error);
                            alert(`上传失败: ${response.error}`);
                        }
                    } else {
                        console.error('上传失败状态码:', xhr.status);
                        alert(`上传失败: ${xhr.status}`);
                    }
                } catch (error) {
                    console.error('处理上传响应时出错:', error);
                    alert(`处理上传响应时出错: ${error.message}`);
                } finally {
                    // 移除加载指示器
                    if (loadingIndicator && loadingIndicator.parentNode) {
                        loadingIndicator.remove();
                    }
                }
            };

            xhr.onerror = (e) => {
                console.error('网络错误，上传失败:', e);
                alert('网络错误，上传失败');

                // 移除加载指示器
                if (loadingIndicator && loadingIndicator.parentNode) {
                    loadingIndicator.remove();
                }
            };

            xhr.send(formData);

        } catch (error) {
            console.error('处理图片错误:', error);
            alert('图片处理失败: ' + error.message);
            loadingIndicator.remove();
        }
    }

    /**
     * 压缩图片
     * @param {File} file - 图片文件
     * @param {number} maxWidth - 最大宽度
     * @param {number} quality - 质量 (0-1)
     * @returns {Promise<Object>} - 压缩结果
     */
    async compressImage(file, maxWidth = null, quality = null) {
        maxWidth = maxWidth || this.options.maxWidth;
        quality = quality || this.options.quality;

        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    let width = img.width;
                    let height = img.height;

                    // 调整尺寸
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }

                    canvas.width = width;
                    canvas.height = height;

                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, width, height);

                    // 转换为Blob
                    canvas.toBlob((blob) => {
                        resolve({
                            blob: blob,
                            originalSize: file.size,
                            compressedSize: blob.size,
                            width: width,
                            height: height
                        });
                    }, 'image/jpeg', quality);
                };
                img.src = event.target.result;
            };
            reader.readAsDataURL(file);
        });
    }

    /**
     * 创建图片容器
     * @param {Object} photo - 照片信息
     * @returns {HTMLElement} - 图片容器元素
     */
    createImageContainer(photo) {
        const container = document.createElement('div');
        container.className = 'image-container';
        container.dataset.photoId = photo.id;

        container.innerHTML = `
            <img src="${photo.file_path}" class="inspection-image" alt="照片">
            <div class="rating" data-value="${photo.rating}">
                <span class="star ${photo.rating >= 1 ? 'active' : ''}">★</span>
                <span class="star ${photo.rating >= 2 ? 'active' : ''}">★</span>
                <span class="star ${photo.rating >= 3 ? 'active' : ''}">★</span>
                <span class="star ${photo.rating >= 4 ? 'active' : ''}">★</span>
                <span class="star ${photo.rating >= 5 ? 'active' : ''}">★</span>
            </div>
            <div class="image-actions">
                <button type="button" class="btn btn-sm btn-danger delete-photo">删除</button>
                <button type="button" class="btn btn-sm btn-secondary rotate-photo">旋转</button>
            </div>
        `;

        // 初始化评分功能
        this.initRating(container.querySelector('.rating'));

        // 初始化删除按钮
        this.addEventListener(container.querySelector('.delete-photo'), 'click', () => {
            this.deletePhoto(photo.id, container);
        });

        // 初始化旋转按钮
        this.addEventListener(container.querySelector('.rotate-photo'), 'click', () => {
            this.rotateImage(container.querySelector('.inspection-image'));
        });

        // 初始化图片预览
        this.addEventListener(container.querySelector('.inspection-image'), 'click', () => {
            this.previewImage(photo.file_path);
        });

        return container;
    }

    /**
     * 初始化评分功能
     * @param {HTMLElement} ratingElement - 评分元素
     */
    initRating(ratingElement) {
        const stars = ratingElement.querySelectorAll('.star');
        const photoId = ratingElement.closest('.image-container').dataset.photoId;

        stars.forEach((star, index) => {
            // 鼠标悬停效果
            this.addEventListener(star, 'mouseover', () => {
                for (let i = 0; i <= index; i++) {
                    stars[i].classList.add('active');
                }
                for (let i = index + 1; i < stars.length; i++) {
                    stars[i].classList.remove('active');
                }
            });

            // 点击设置评分
            this.addEventListener(star, 'click', () => {
                const newValue = index + 1;
                this.updateRating(photoId, newValue, ratingElement, stars);
            });
        });

        // 鼠标离开效果
        this.addEventListener(ratingElement, 'mouseleave', () => {
            const value = parseInt(ratingElement.dataset.value);
            stars.forEach((s, i) => {
                if (i < value) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });
    }

    /**
     * 更新评分
     * @param {number} photoId - 照片ID
     * @param {number} rating - 评分
     * @param {HTMLElement} ratingElement - 评分元素
     * @param {NodeList} stars - 星星元素列表
     */
    async updateRating(photoId, rating, ratingElement, stars) {
        try {
            // 获取CSRF令牌
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            const headers = {
                'Content-Type': 'application/json'
            };

            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken.getAttribute('content');
            }

            const response = await fetch(`${this.options.apiBaseUrl}/update-rating`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    photo_id: photoId,
                    rating: rating
                })
            });

            const data = await response.json();

            if (data.success) {
                // 更新UI
                ratingElement.dataset.value = rating;
                stars.forEach((s, i) => {
                    if (i < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            } else {
                alert(`评分更新失败: ${data.error}`);
            }
        } catch (error) {
            console.error('评分更新错误:', error);
            alert('评分更新失败，请重试');
        }
    }

    /**
     * 删除照片
     * @param {number} photoId - 照片ID
     * @param {HTMLElement} containerElement - 容器元素
     */
    async deletePhoto(photoId, containerElement) {
        if (confirm('确定要删除这张照片吗？')) {
            try {
                // 获取CSRF令牌
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                const headers = {
                    'Content-Type': 'application/json'
                };

                if (csrfToken) {
                    headers['X-CSRFToken'] = csrfToken.getAttribute('content');
                }

                const response = await fetch(`${this.options.apiBaseUrl}/delete`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        photo_id: photoId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    containerElement.remove();
                } else {
                    alert(`删除失败: ${data.error}`);
                }
            } catch (error) {
                console.error('删除照片错误:', error);
                alert('删除失败，请重试');
            }
        }
    }

    /**
     * 旋转图片
     * @param {HTMLImageElement} imgElement - 图片元素
     */
    rotateImage(imgElement) {
        const currentRotation = parseInt(imgElement.dataset.rotation || 0);
        const newRotation = (currentRotation + 90) % 360;
        imgElement.style.transform = `rotate(${newRotation}deg)`;
        imgElement.dataset.rotation = newRotation;
    }

    /**
     * 创建预览模态框
     */
    createPreviewModal() {
        // 检查是否已存在预览模态框
        if (document.getElementById('imagePreviewModal')) {
            return;
        }

        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade image-preview-modal';
        modal.id = 'imagePreviewModal';
        modal.tabIndex = -1;
        modal.role = 'dialog';
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">图片预览</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <img src="" id="previewImage" alt="预览图片">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        `;

        // 添加到文档
        document.body.appendChild(modal);
    }

    /**
     * 预览图片
     * @param {string} imagePath - 图片路径
     */
    previewImage(imagePath) {
        this.createPreviewModal();

        const previewModal = document.getElementById('imagePreviewModal');
        const previewImage = document.getElementById('previewImage');

        previewImage.src = imagePath;

        // 使用jQuery显示模态框（如果可用）
        if (typeof $ !== 'undefined') {
            $(previewModal).modal('show');
        } else {
            // 备用方法
            previewModal.classList.add('show');
            previewModal.style.display = 'block';
        }
    }
}
