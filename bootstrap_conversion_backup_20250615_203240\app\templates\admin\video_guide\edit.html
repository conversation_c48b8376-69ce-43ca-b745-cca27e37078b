{% extends "base.html" %}

{% block title %}编辑视频{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit me-2"></i>编辑引导视频
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('guide_management.video_management') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.step_name.label(class="form-label") }}
                                    {{ form.step_name(class="form-control") }}
                                    {% if form.step_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.step_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="3") }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- 当前视频信息 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">当前视频</h6>
                                    </div>
                                    <div class="card-body">
                                        {% if video.file_path %}
                                            <video width="100%" height="200" controls>
                                                <source src="{{ video.file_path }}" type="video/mp4">
                                                您的浏览器不支持视频播放。
                                            </video>
                                        {% else %}
                                            <p class="text-muted">暂无视频</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">当前缩略图</h6>
                                    </div>
                                    <div class="card-body">
                                        {% if video.thumbnail %}
                                            <img src="{{ video.thumbnail }}" class="img-fluid" alt="缩略图">
                                        {% else %}
                                            <p class="text-muted">暂无缩略图</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.video_file.label(class="form-label") }}
                                    {{ form.video_file(class="form-control-file") }}
                                    <small class="form-text text-muted">
                                        可选，如不选择则保持原视频不变<br>
                                        支持格式：MP4、AVI、MOV、WMV、FLV、WebM，文件大小限制50MB
                                    </small>
                                    {% if form.video_file.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.video_file.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.thumbnail_file.label(class="form-label") }}
                                    {{ form.thumbnail_file(class="form-control-file") }}
                                    <small class="form-text text-muted">
                                        可选，如不选择则保持原缩略图不变<br>
                                        支持JPG、PNG、GIF格式
                                    </small>
                                    {% if form.thumbnail_file.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.thumbnail_file.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存修改
                            </button>
                            <a href="{{ url_for('guide_management.video_management') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 文件大小检查
    $('#video_file').on('change', function() {
        const file = this.files[0];
        if (file) {
            const maxSize = 50 * 1024 * 1024; // 50MB
            if (file.size > maxSize) {
                alert('视频文件大小不能超过50MB');
                $(this).val('');
            }
        }
    });
});
</script>
{% endblock %}
