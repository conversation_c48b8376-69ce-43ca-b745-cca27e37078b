/* 自定义 CSS 类 - 用于替换内联样式 */

/* 宽度类 */
.w-15 { width: 15% !important; }
.w-20 { width: 20% !important; }
.w-30 { width: 30% !important; }
.w-35 { width: 35% !important; }
.w-40 { width: 40% !important; }
.w-60 { width: 60% !important; }
.w-70 { width: 70% !important; }
.w-80 { width: 80% !important; }
.w-85 { width: 85% !important; }
.w-90 { width: 90% !important; }

/* 高度类 */
.h-auto { height: auto !important; }
.h-50px { height: 50px !important; }
.h-100px { height: 100px !important; }
.h-200px { height: 200px !important; }
.h-300px { height: 300px !important; }

/* 最大宽度 */
.max-w-200 { max-width: 200px !important; }
.max-w-300 { max-width: 300px !important; }
.max-w-400 { max-width: 400px !important; }
.max-w-500 { max-width: 500px !important; }

/* 最大高度 */
.max-h-200 { max-height: 200px !important; }
.max-h-300 { max-height: 300px !important; }
.max-h-400 { max-height: 400px !important; }

/* 溢出处理 */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-scroll { overflow: scroll !important; }

/* 位置 */
.pos-relative { position: relative !important; }
.pos-absolute { position: absolute !important; }
.pos-fixed { position: fixed !important; }

/* Z-index - 遵循新的层级体系 */
.z-1 { z-index: 1 !important; }
.z-2 { z-index: 2 !important; }
.z-3 { z-index: 3 !important; }
.z-10 { z-index: 10 !important; }
.z-50 { z-index: 50 !important; }
.z-100 { z-index: 100 !important; }
.z-999 { z-index: 999 !important; }
.z-1000 { z-index: 1000 !important; } /* 保留但不推荐使用 */

/* 光标 */
.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* 用户选择 */
.user-select-none { user-select: none !important; }
.user-select-all { user-select: all !important; }

/* 空白处理 */
.white-space-nowrap { white-space: nowrap !important; }
.white-space-pre { white-space: pre !important; }
.white-space-pre-wrap { white-space: pre-wrap !important; }

/* 文本溢出 */
.text-ellipsis {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* 垂直对齐 */
.vertical-align-top { vertical-align: top !important; }
.vertical-align-middle { vertical-align: middle !important; }
.vertical-align-bottom { vertical-align: bottom !important; }

/* 打印相关 */
@media print {
    .print-hidden { display: none !important; }
    .print-block { display: block !important; }
    .print-break-before { page-break-before: always !important; }
    .print-break-after { page-break-after: always !important; }
}

/* 表格相关 */
.table-layout-fixed { table-layout: fixed !important; }
.table-layout-auto { table-layout: auto !important; }

/* 边框圆角 */
.rounded-sm { border-radius: 0.125rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }

/* 阴影 */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important; }

/* 透明度 */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }