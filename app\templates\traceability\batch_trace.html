{% extends 'base.html' %}

{% block title %}批次溯源详情{% endblock %}

{% block content %}
<div class=\"container-fluid'>
    <div class='row\">
        <div class=\"col-12'>
            <div class='card\">
                <div class=\"card-header'>
                    <h3 class='card-title\">批次溯源详情</h3>
                    <div class=\"card-tools'>
                        <a href='{{ url_for('traceability.index') }}\" class=\"btn btn-secondary btn-sm'>
                            <i class='fas fa-arrow-left\"></i> 返回溯源查询
                        </a>
                        <a href="{{ url_for('material_batch.view', id=batch.id) }}" class=\"btn btn-primary btn-sm'>
                            <i class='fas fa-eye\"></i> 查看批次详情
                        </a>
                    </div>
                </div>
                <div class=\"card-body'>
                    <!-- 溯源链可视化 -->
                    <div class='row mb-4\">
                        <div class=\"col-12'>
                            <div class='card bg-light\">
                                <div class=\"card-body'>
                                    <div class='trace-chain\">
                                        <div class=\"trace-step'>
                                            <div class='trace-icon bg-primary\">
                                                <i class=\"fas fa-industry'></i>
                                            </div>
                                            <div class='trace-content\">
                                                <h5>供应商</h5>
                                                <p>{{ supplier.name }}</p>
                                            </div>
                                        </div>
                                        <div class=\"trace-arrow'>
                                            <i class='fas fa-arrow-right\"></i>
                                        </div>
                                        <div class=\"trace-step'>
                                            <div class='trace-icon bg-success\">
                                                <i class=\"fas fa-box'></i>
                                            </div>
                                            <div class='trace-content\">
                                                <h5>食材批次</h5>
                                                <p>{{ batch.ingredient.name }} ({{ batch.batch_number }})</p>
                                            </div>
                                        </div>
                                        {% if downstream_info %}
                                        <div class=\"trace-arrow'>
                                            <i class='fas fa-arrow-right\"></i>
                                        </div>
                                        <div class=\"trace-step'>
                                            <div class='trace-icon bg-warning\">
                                                <i class=\"fas fa-utensils'></i>
                                            </div>
                                            <div class='trace-content\">
                                                <h5>菜单计划</h5>
                                                <p>{{ downstream_info[0].menu_plan.plan_date }} ({{ downstream_info[0].menu_plan.meal_type }})</p>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批次基本信息 -->
                    <div class=\"row'>
                        <div class='col-md-6\">
                            <div class=\"card'>
                                <div class='card-header bg-primary\">
                                    <h4 class=\"card-title text-white'>批次基本信息</h4>
                                </div>
                                <div class='card-body\">
                                    <table class=\"table table-bordered'>
                                        <tr>
                                            <th class='w-30\">批次号</th>
                                            <td>{{ batch.batch_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>食材</th>
                                            <td>{{ batch.ingredient.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>生产日期</th>
                                            <td>{{ batch.production_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>过期日期</th>
                                            <td>{{ batch.expiry_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>生产批号</th>
                                            <td>{{ batch.production_batch_no or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>产地</th>
                                            <td>{{ batch.origin_place or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>检验编号</th>
                                            <td>{{ batch.inspection_no or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>合格证编号</th>
                                            <td>{{ batch.certificate_no or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>当前库存</th>
                                            <td>{{ batch.current_quantity }} {{ batch.unit }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6'>
                            <div class='card\">
                                <div class=\"card-header bg-success'>
                                    <h4 class='card-title text-white\">供应商信息</h4>
                                </div>
                                <div class=\"card-body'>
                                    <table class='table table-bordered\">
                                        <tr>
                                            <th class=\"w-30'>供应商名称</th>
                                            <td>{{ supplier.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>联系人</th>
                                            <td>{{ supplier.contact_person }}</td>
                                        </tr>
                                        <tr>
                                            <th>联系电话</th>
                                            <td>{{ supplier.contact_phone }}</td>
                                        </tr>
                                        <tr>
                                            <th>地址</th>
                                            <td>{{ supplier.address }}</td>
                                        </tr>
                                        <tr>
                                            <th>营业执照</th>
                                            <td>{{ supplier.business_license or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>食品经营许可证</th>
                                            <td>{{ supplier.food_license or '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>评级</th>
                                            <td>
                                                {% for i in range(supplier.rating|int) %}
                                                <i class='fas fa-star text-warning\"></i>
                                                {% endfor %}
                                                {% for i in range(5 - supplier.rating|int) %}
                                                <i class=\"far fa-star text-warning'></i>
                                                {% endfor %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 溯源文档 -->
                    <div class='row mt-4\">
                        <div class=\"col-12'>
                            <div class='card\">
                                <div class=\"card-header bg-info'>
                                    <h4 class='card-title text-white\">溯源文档</h4>
                                </div>
                                <div class=\"card-body'>
                                    <div class='table-responsive\">
                                        <table class=\"table table-bordered table-striped'>
                                            <thead>
                                                <tr>
                                                    <th>文档类型</th>
                                                    <th>文档编号</th>
                                                    <th>上传时间</th>
                                                    <th>上传人</th>
                                                    <th>备注</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for document in documents %}
                                                <tr>
                                                    <td>{{ document.document_type }}</td>
                                                    <td>{{ document.document_no or '无' }}</td>
                                                    <td>{{ document.upload_time }}</td>
                                                    <td>{{ document.uploader.real_name or document.uploader.username }}</td>
                                                    <td>{{ document.remark or '无' }}</td>
                                                    <td>
                                                        <a href='{{ url_for('static', filename=document.document_path) }}\" target="_blank" class=\"btn btn-info btn-sm'>
                                                            <i class='fas fa-eye\"></i> 查看
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="6" class=\"text-center'>暂无文档</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批次流水 -->
                    <div class='row mt-4\">
                        <div class=\"col-12'>
                            <div class='card\">
                                <div class=\"card-header bg-warning'>
                                    <h4 class='card-title text-white\">批次流水</h4>
                                </div>
                                <div class=\"card-body'>
                                    <div class='table-responsive\">
                                        <table class=\"table table-bordered table-striped'>
                                            <thead>
                                                <tr>
                                                    <th>流水类型</th>
                                                    <th>流向</th>
                                                    <th>数量</th>
                                                    <th>单位</th>
                                                    <th>操作人</th>
                                                    <th>流水日期</th>
                                                    <th>备注</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for flow in flows %}
                                                <tr>
                                                    <td>{{ flow.flow_type }}</td>
                                                    <td>
                                                        {% if flow.flow_direction == '增加' %}
                                                        <span class='text-success\">增加</span>
                                                        {% else %}
                                                        <span class=\"text-danger'>减少</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ flow.quantity }}</td>
                                                    <td>{{ flow.unit }}</td>
                                                    <td>{{ flow.operator.real_name or flow.operator.username }}</td>
                                                    <td>{{ flow.flow_date }}</td>
                                                    <td>{{ flow.remark or '无' }}</td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan='7\" class=\"text-center'>暂无流水记录</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 下游信息 -->
                    {% if downstream_info %}
                    <div class='row mt-4\">
                        <div class=\"col-12'>
                            <div class='card\">
                                <div class=\"card-header bg-danger'>
                                    <h4 class='card-title text-white\">下游信息</h4>
                                </div>
                                <div class=\"card-body'>
                                    <div class='table-responsive\">
                                        <table class=\"table table-bordered table-striped'>
                                            <thead>
                                                <tr>
                                                    <th>出库单号</th>
                                                    <th>出库日期</th>
                                                    <th>出库数量</th>
                                                    <th>消耗计划ID</th>
                                                    <th>菜单计划日期</th>
                                                    <th>餐次</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for info in downstream_info %}
                                                <tr>
                                                    <td>{{ info.stock_out.stock_out_number }}</td>
                                                    <td>{{ info.stock_out.stock_out_date }}</td>
                                                    <td>{{ info.stock_out_item.quantity }} {{ info.stock_out_item.unit }}</td>
                                                    <td>{{ info.consumption_plan.id }}</td>
                                                    <td>{{ info.menu_plan.plan_date }}</td>
                                                    <td>{{ info.menu_plan.meal_type }}</td>
                                                    <td>
                                                        <a href='{{ url_for('traceability.trace_menu', id=info.menu_plan.id) }}\" class=\"btn btn-info btn-sm'>
                                                            <i class='fas fa-eye\"></i> 查看菜单
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .trace-chain {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }
    
    .trace-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        width: 200px;
    }
    
    .trace-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }
    
    .trace-icon i {
        font-size: 24px;
        color: white;
    }
    
    .trace-content h5 {
        margin-bottom: 5px;
    }
    
    .trace-content p {
        margin-bottom: 0;
    }
    
    .trace-arrow {
        margin: 0 20px;
        font-size: 24px;
        color: #6c757d;
    }
</style>
{% endblock %}
