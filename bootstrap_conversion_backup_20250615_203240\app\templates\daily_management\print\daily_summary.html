{% extends 'daily_management/print/base_print.html' %}

{% block title %}食堂日志汇总 - {{ log.log_date|format_datetime('%Y-%m-%d') }}{% endblock %}

{% block document_title %}食堂日志汇总{% endblock %}

{% block document_subtitle %}{{ log.log_date|format_datetime('%Y年%m月%d日') }}{% endblock %}

{% block document_info %}
<div class="info-row">
    <div class="info-label">日期：</div>
    <div class="info-value">{{ log.log_date|format_datetime('%Y-%m-%d') }}</div>
</div>
<div class="info-row">
    <div class="info-label">管理员：</div>
    <div class="info-value">{{ log.manager or '未设置' }}</div>
</div>
<div class="info-row">
    <div class="info-label">天气：</div>
    <div class="info-value">{{ log.weather or '未记录' }}</div>
</div>
<div class="info-row">
    <div class="info-label">就餐人数：</div>
    <div class="info-value">学生：{{ log.student_count or 0 }}人，教师：{{ log.teacher_count or 0 }}人，其他：{{ log.other_count or 0 }}人，共{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}人</div>
</div>
{% endblock %}

{% block content %}
<!-- 菜单信息 -->
<div class="section-title">菜单信息</div>
<table>
    <thead>
        <tr>
            <th width="20%">餐次</th>
            <th width="80%">菜单内容</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>早餐</td>
            <td>{{ log.breakfast_menu or '未记录' }}</td>
        </tr>
        <tr>
            <td>午餐</td>
            <td>{{ log.lunch_menu or '未记录' }}</td>
        </tr>
        <tr>
            <td>晚餐</td>
            <td>{{ log.dinner_menu or '未记录' }}</td>
        </tr>
    </tbody>
</table>

<!-- 检查记录摘要 -->
<div class="section-title">检查记录摘要</div>
<table>
    <thead>
        <tr>
            <th width="20%">检查类型</th>
            <th width="20%">检查项目数</th>
            <th width="60%">检查结果</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>晨检</td>
            <td>{{ morning_inspections|length }}</td>
            <td>
                {% if morning_inspections %}
                    {% set abnormal_count = 0 %}
                    {% for inspection in morning_inspections %}
                        {% if inspection.status == 'abnormal' %}
                            {% set abnormal_count = abnormal_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {% if abnormal_count > 0 %}
                        <span class="abnormal">异常项：{{ abnormal_count }}项</span>
                    {% else %}
                        <span class="normal">全部正常</span>
                    {% endif %}
                {% else %}
                    未进行检查
                {% endif %}
            </td>
        </tr>
        <tr>
            <td>午检</td>
            <td>{{ noon_inspections|length }}</td>
            <td>
                {% if noon_inspections %}
                    {% set abnormal_count = 0 %}
                    {% for inspection in noon_inspections %}
                        {% if inspection.status == 'abnormal' %}
                            {% set abnormal_count = abnormal_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {% if abnormal_count > 0 %}
                        <span class="abnormal">异常项：{{ abnormal_count }}项</span>
                    {% else %}
                        <span class="normal">全部正常</span>
                    {% endif %}
                {% else %}
                    未进行检查
                {% endif %}
            </td>
        </tr>
        <tr>
            <td>晚检</td>
            <td>{{ evening_inspections|length }}</td>
            <td>
                {% if evening_inspections %}
                    {% set abnormal_count = 0 %}
                    {% for inspection in evening_inspections %}
                        {% if inspection.status == 'abnormal' %}
                            {% set abnormal_count = abnormal_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {% if abnormal_count > 0 %}
                        <span class="abnormal">异常项：{{ abnormal_count }}项</span>
                    {% else %}
                        <span class="normal">全部正常</span>
                    {% endif %}
                {% else %}
                    未进行检查
                {% endif %}
            </td>
        </tr>
    </tbody>
</table>

<!-- 陪餐记录摘要 -->
<div class="section-title">陪餐记录摘要</div>
{% if companions %}
<table>
    <thead>
        <tr>
            <th width="15%">姓名</th>
            <th width="15%">职务</th>
            <th width="15%">陪餐时间</th>
            <th width="15%">陪餐餐次</th>
            <th width="40%">反馈意见</th>
        </tr>
    </thead>
    <tbody>
        {% for companion in companions %}
        <tr>
            <td>{{ companion.name }}</td>
            <td>{{ companion.position }}</td>
            <td>{{ companion.dining_time.strftime('%H:%M') if companion.dining_time else '' }}</td>
            <td>{{ companion.meal_type|replace('breakfast', '早餐')|replace('lunch', '午餐')|replace('dinner', '晚餐') }}</td>
            <td>{{ companion.feedback or '' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<p>暂无陪餐记录</p>
{% endif %}

<!-- 培训记录摘要 -->
<div class="section-title">培训记录摘要</div>
{% if trainings %}
<table>
    <thead>
        <tr>
            <th width="20%">培训主题</th>
            <th width="15%">培训时间</th>
            <th width="15%">培训人员</th>
            <th width="15%">参与人数</th>
            <th width="35%">培训内容</th>
        </tr>
    </thead>
    <tbody>
        {% for training in trainings %}
        <tr>
            <td>{{ training.topic }}</td>
            <td>{{ training.training_time.strftime('%H:%M') if training.training_time else '' }}</td>
            <td>{{ training.trainer_name }}</td>
            <td>{{ training.participant_count }}</td>
            <td>{{ training.content }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<p>暂无培训记录</p>
{% endif %}

<!-- 特殊事件摘要 -->
<div class="section-title">特殊事件摘要</div>
{% if events %}
<table>
    <thead>
        <tr>
            <th width="20%">事件类型</th>
            <th width="20%">事件时间</th>
            <th width="60%">事件描述</th>
        </tr>
    </thead>
    <tbody>
        {% for event in events %}
        <tr>
            <td>{{ event.event_type }}</td>
            <td>{{ event.event_time.strftime('%H:%M') if event.event_time else '' }}</td>
            <td>{{ event.description }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<p>暂无特殊事件记录</p>
{% endif %}

<!-- 问题记录摘要 -->
<div class="section-title">问题记录摘要</div>
{% if issues %}
<table>
    <thead>
        <tr>
            <th width="20%">问题类型</th>
            <th width="15%">发现时间</th>
            <th width="15%">状态</th>
            <th width="50%">问题描述</th>
        </tr>
    </thead>
    <tbody>
        {% for issue in issues %}
        <tr>
            <td>{{ issue.issue_type }}</td>
            <td>{{ issue.found_time.strftime('%H:%M') if issue.found_time else '' }}</td>
            <td>
                {% if issue.status == 'pending' %}
                <span class="abnormal">待处理</span>
                {% elif issue.status == 'processing' %}
                <span class="abnormal">处理中</span>
                {% elif issue.status == 'resolved' %}
                <span class="normal">已解决</span>
                {% else %}
                {{ issue.status }}
                {% endif %}
            </td>
            <td>{{ issue.description }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<p>暂无问题记录</p>
{% endif %}

<!-- 运营总结 -->
<div class="section-title">运营总结</div>
<div style="border: 1px solid #000; padding: 10px; min-height: 100px;">
    {{ log.operation_summary or '暂无运营总结' }}
</div>

<!-- 食物浪费情况 -->
<div class="section-title">食物浪费情况</div>
<div style="border: 1px solid #000; padding: 10px; min-height: 50px;">
    食物浪费量：{{ log.food_waste or 0 }} 千克
</div>
{% endblock %}

{% block signature %}
<div class="signature-item">
    <div class="signature-line"></div>
    <div>记录人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>食堂负责人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>学校负责人</div>
</div>
{% endblock %}
