{% extends 'base.html' %}

{% block title %}系统数据管理 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .module-card {
        margin-bottom: 20px;
        border-start: 5px solid #007bff;
    }
    .module-card.ingredient {
        border-start-color: #28a745;
    }
    .module-card.recipe {
        border-start-color: #fd7e14;
    }
    .module-card.supplier {
        border-start-color: #6f42c1;
    }
    .module-card.inventory {
        border-start-color: #20c997;
    }
    .module-card.menu {
        border-start-color: #e83e8c;
    }
    .module-card.food-sample {
        border-start-color: #ffc107;
    }
    .module-card.employee {
        border-start-color: #17a2b8;
    }
    .module-card.notification {
        border-start-color: #6c757d;
    }
    .module-card.audit-log {
        border-start-color: #dc3545;
    }
    .confirmation-input {
        font-family: monospace;
        text-transform: uppercase;
    }
    .stats-badge {
        font-size: 1rem;
    }
    .warning-text {
        color: #dc3545;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>系统数据管理</h2>
    </div>
    <div class="col-md-4 text-end">
        {% if current_user.has_permission('setting', 'super_delete') %}
        <a href="{{ url_for('admin_data.super_delete_index') }}" class="btn btn-primary">
            <i class="fas fa-trash"></i> 超级删除工具
        </a>
        {% endif %}
        <button id="refreshStats" class="btn btn-info">
            <i class="fas fa-sync-alt"></i> 刷新统计
        </button>
        <a href="{{ url_for('system.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回管理首页
        </a>
    </div>
</div>

<div class="alert alert-danger">
    <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 警告！</h4>
    <p>此页面提供的数据清空功能将<strong>永久删除</strong>相关模块的所有数据，此操作<strong>不可撤销</strong>。</p>
    <p>请确保您已经：</p>
    <ul>
        <li>完全了解此操作的影响范围</li>
        <li>已备份重要数据</li>
        <li>确认当前没有其他用户正在使用系统</li>
    </ul>
    <p class="mb-0">操作前请仔细阅读每个模块的说明，并正确输入确认码。</p>
</div>

<div class="row">
    <div class="col-md-6">
        <!-- 食材数据 -->
        <div class="card module-card ingredient">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">食材数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>食材总数：<span class="badge badge-success stats-badge" id="ingredients-total">{{ stats.ingredients.total }}</span></li>
                        <li>食材分类：<span class="badge badge-success stats-badge" id="ingredients-categories">{{ stats.ingredients.categories }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_ingredient_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <div class="custom-control custom-checkbox mb-3">
                            <input type="checkbox" class="form-check-input" id="includeIngredientCategories" name="include_categories">
                            <label class="form-check-label" for="includeIngredientCategories">同时清空食材分类</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="ingredientConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="ingredientConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-INGREDIENT-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-INGREDIENT-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="食材">
                            <i class="fas fa-trash-alt"></i> 清空食材数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有食材数据，同时会清空与食材相关的食谱配料、库存、入库和出库记录。
                        </small>
                    </div>
                </form>
            </div>
        </div>

        <!-- 食谱数据 -->
        <div class="card module-card recipe">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">食谱数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>食谱总数：<span class="badge badge-warning stats-badge" id="recipes-total">{{ stats.recipes.total }}</span></li>
                        <li>食谱配料记录：<span class="badge badge-warning stats-badge" id="recipes-ingredients">{{ stats.recipes.ingredients }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_recipe_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="recipeConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="recipeConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-RECIPE-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-RECIPE-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="食谱">
                            <i class="fas fa-trash-alt"></i> 清空食谱数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有食谱数据和食谱配料关联，同时会影响菜单计划中的食谱引用。
                        </small>
                    </div>
                </form>
            </div>
        </div>

        <!-- 供应商数据 -->
        <div class="card module-card supplier">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">供应商数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>供应商总数：<span class="badge badge-primary stats-badge" id="suppliers-total">{{ stats.suppliers.total }}</span></li>
                        <li>供应商产品：<span class="badge badge-primary stats-badge" id="suppliers-products">{{ stats.suppliers.products }}</span></li>
                        <li>供应商证书：<span class="badge badge-primary stats-badge" id="suppliers-certificates">{{ stats.suppliers.certificates }}</span></li>
                        <li>供应商分类：<span class="badge badge-primary stats-badge" id="suppliers-categories">{{ stats.suppliers.categories }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_supplier_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <div class="custom-control custom-checkbox mb-3">
                            <input type="checkbox" class="form-check-input" id="includeSupplierCategories" name="include_categories">
                            <label class="form-check-label" for="includeSupplierCategories">同时清空供应商分类</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="supplierConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="supplierConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-SUPPLIER-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-SUPPLIER-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="供应商">
                            <i class="fas fa-trash-alt"></i> 清空供应商数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有供应商数据，包括供应商产品和证书信息。
                        </small>
                    </div>
                </form>
            </div>
        </div>

        <!-- 员工数据 -->
        <div class="card module-card employee">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">员工数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>员工总数：<span class="badge badge-info stats-badge" id="employees-total">{{ stats.employees.total }}</span></li>
                        <li>健康证：<span class="badge badge-info stats-badge" id="employees-health-certificates">{{ stats.employees.health_certificates }}</span></li>
                        <li>体检记录：<span class="badge badge-info stats-badge" id="employees-medical-examinations">{{ stats.employees.medical_examinations }}</span></li>
                        <li>健康检查：<span class="badge badge-info stats-badge" id="employees-health-checks">{{ stats.employees.health_checks }}</span></li>
                        <li>培训记录：<span class="badge badge-info stats-badge" id="employees-training-records">{{ stats.employees.training_records }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_employee_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="employeeConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="employeeConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-EMPLOYEE-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-EMPLOYEE-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="员工">
                            <i class="fas fa-trash-alt"></i> 清空员工数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有员工数据，包括健康证、体检记录、健康检查和培训记录。
                        </small>
                    </div>
                </form>
            </div>
        </div>

        <!-- 审计日志 -->
        <div class="card module-card audit-log">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">审计日志管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>审计日志总数：<span class="badge badge-danger stats-badge" id="audit-logs">{{ stats.audit_logs }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_audit_log') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="auditLogConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="auditLogConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-AUDIT-LOG-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-AUDIT-LOG-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="审计日志">
                            <i class="fas fa-trash-alt"></i> 清空审计日志
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有审计日志记录，这可能会影响系统操作的可追溯性。
                        </small>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <!-- 库存数据 -->
        <div class="card module-card inventory">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">库存数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>库存记录：<span class="badge badge-success stats-badge" id="inventory-records">{{ stats.inventory.records }}</span></li>
                        <li>入库记录：<span class="badge badge-success stats-badge" id="inventory-stock-in">{{ stats.inventory.stock_in }}</span></li>
                        <li>出库记录：<span class="badge badge-success stats-badge" id="inventory-stock-out">{{ stats.inventory.stock_out }}</span></li>
                        <li>仓库数量：<span class="badge badge-success stats-badge" id="inventory-warehouses">{{ stats.inventory.warehouses }}</span></li>
                        <li>存储位置：<span class="badge badge-success stats-badge" id="inventory-locations">{{ stats.inventory.locations }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_inventory_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <div class="custom-control custom-checkbox mb-3">
                            <input type="checkbox" class="form-check-input" id="includeLocations" name="include_locations">
                            <label class="form-check-label" for="includeLocations">同时清空仓库和存储位置</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="inventoryConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="inventoryConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-INVENTORY-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-INVENTORY-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="库存">
                            <i class="fas fa-trash-alt"></i> 清空库存数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有库存记录、入库记录和出库记录。如果选择同时清空仓库和存储位置，将一并删除这些基础数据。
                        </small>
                    </div>
                </form>
            </div>
        </div>

        <!-- 菜单计划数据 -->
        <div class="card module-card menu">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">菜单计划数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>菜单计划：<span class="badge badge-warning stats-badge" id="menu-plans">{{ stats.menu.plans }}</span></li>
                        <li>菜单食谱：<span class="badge badge-warning stats-badge" id="menu-recipes">{{ stats.menu.recipes }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_menu_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="menuConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="menuConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-MENU-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-MENU-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="菜单计划">
                            <i class="fas fa-trash-alt"></i> 清空菜单计划数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有菜单计划数据和菜单食谱关联。
                        </small>
                    </div>
                </form>
            </div>
        </div>

        <!-- 留样数据 -->
        <div class="card module-card food-sample">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">留样数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>留样记录：<span class="badge badge-primary stats-badge" id="food-samples">{{ stats.food_samples }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_food_sample_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="foodSampleConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="foodSampleConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-FOOD-SAMPLE-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-FOOD-SAMPLE-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="留样">
                            <i class="fas fa-trash-alt"></i> 清空留样数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有留样记录数据。
                        </small>
                    </div>
                </form>
            </div>
        </div>

        <!-- 通知数据 -->
        <div class="card module-card notification">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">通知数据管理</h5>
            </div>
            <div class="card-body">
                <div class="stats mb-3">
                    <p><strong>当前统计：</strong></p>
                    <ul>
                        <li>通知记录：<span class="badge badge-secondary stats-badge" id="notifications">{{ stats.notifications }}</span></li>
                    </ul>
                </div>
                <form action="{{ url_for('admin_data.clear_notification_data') }}" method="POST" class="clear-data-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="notificationConfirmationCode">确认码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control confirmation-input" id="notificationConfirmationCode" name="confirmation_code" placeholder="输入 CLEAR-NOTIFICATION-DATA" required>
                        <small class="form-text text-muted">请输入确认码 <code>CLEAR-NOTIFICATION-DATA</code> 以确认操作</small>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-danger clear-data-btn" data-module="通知">
                            <i class="fas fa-trash-alt"></i> 清空通知数据
                        </button>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-info-circle"></i> 此操作将删除所有系统通知记录。
                        </small>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="confirmModalLabel">确认清空数据</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要清空<span id="moduleNameSpan" class="warning-text"></span>数据吗？</p>
                <p class="warning-text">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmClearBtn">确认清空</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 表单提交前显示确认对话框
        $('.clear-data-form').on('submit', function(e) {
            e.preventDefault();

            var form = $(this);
            var moduleName = form.find('.clear-data-btn').data('module');

            // 设置模态框内容
            $('#moduleNameSpan').text(moduleName);

            // 显示模态框
            $('#confirmModal').modal('show');

            // 确认按钮点击事件
            $('#confirmClearBtn').off('click').on('click', function() {
                $('#confirmModal').modal('hide');
                form.off('submit').submit();
            });
        });

        // 刷新统计数据
        $('#refreshStats').click(function() {
            var btn = $(this);
            btn.prop('disabled', true);
            btn.html('<i class="fas fa-spinner fa-spin"></i> 刷新中...');

            $.ajax({
                url: "{{ url_for('admin_data.refresh_stats') }}",
                type: 'GET',
                success: function(data) {
                    // 更新食材统计
                    $('#ingredients-total').text(data.ingredients.total);
                    $('#ingredients-categories').text(data.ingredients.categories);

                    // 更新食谱统计
                    $('#recipes-total').text(data.recipes.total);
                    $('#recipes-ingredients').text(data.recipes.ingredients);

                    // 更新供应商统计
                    $('#suppliers-total').text(data.suppliers.total);
                    $('#suppliers-products').text(data.suppliers.products);
                    $('#suppliers-certificates').text(data.suppliers.certificates);
                    $('#suppliers-categories').text(data.suppliers.categories);

                    // 更新库存统计
                    $('#inventory-records').text(data.inventory.records);
                    $('#inventory-stock-in').text(data.inventory.stock_in);
                    $('#inventory-stock-out').text(data.inventory.stock_out);
                    $('#inventory-warehouses').text(data.inventory.warehouses);
                    $('#inventory-locations').text(data.inventory.locations);

                    // 更新菜单计划统计
                    $('#menu-plans').text(data.menu.plans);
                    $('#menu-recipes').text(data.menu.recipes);

                    // 更新留样统计
                    $('#food-samples').text(data.food_samples);

                    // 更新员工统计
                    $('#employees-total').text(data.employees.total);
                    $('#employees-health-certificates').text(data.employees.health_certificates);
                    $('#employees-medical-examinations').text(data.employees.medical_examinations);
                    $('#employees-health-checks').text(data.employees.health_checks);
                    $('#employees-training-records').text(data.employees.training_records);

                    // 更新通知统计
                    $('#notifications').text(data.notifications);

                    // 更新审计日志统计
                    $('#audit-logs').text(data.audit_logs);

                    // 显示成功消息
                    toastr.success('统计数据已刷新');
                },
                error: function(xhr, status, error) {
                    toastr.error('刷新统计数据失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : error));
                },
                complete: function() {
                    btn.prop('disabled', false);
                    btn.html('<i class="fas fa-sync-alt"></i> 刷新统计');
                }
            });
        });
    });
</script>
{% endblock %}
