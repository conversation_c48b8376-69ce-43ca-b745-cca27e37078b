// 初始化图表
document.addEventListener('DOMContentLoaded', function() {
    // 食堂运营效率图表
    const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
    new Chart(efficiencyCtx, {
        type: 'line',
        data: {
            labels: ['8:00', '10:00', '12:00', '14:00', '16:00', '18:00'],
            datasets: [{
                label: '运营效率',
                data: [85, 90, 95, 92, 88, 85],
                borderColor: '#165DFF',
                tension: 0.4,
                fill: true,
                backgroundColor: 'rgba(22, 93, 255, 0.1)'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // 食品安全检测图表
    const safetyCtx = document.getElementById('safetyChart').getContext('2d');
    new Chart(safetyCtx, {
        type: 'doughnut',
        data: {
            labels: ['合格', '待检', '不合格'],
            datasets: [{
                data: [95, 3, 2],
                backgroundColor: ['#36CBCB', '#FFB800', '#FF4D4F']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 用户满意度图表
    const satisfactionCtx = document.getElementById('satisfactionChart').getContext('2d');
    new Chart(satisfactionCtx, {
        type: 'bar',
        data: {
            labels: ['菜品质量', '服务态度', '环境卫生', '价格合理', '营养均衡'],
            datasets: [{
                label: '满意度',
                data: [92, 88, 95, 85, 90],
                backgroundColor: '#722ED1'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
});

// 移动端菜单切换
document.getElementById('menu-toggle')?.addEventListener('click', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenu.classList.toggle('active');
});

// 返回顶部按钮
const backToTopButton = document.getElementById('back-to-top');
window.addEventListener('scroll', function() {
    if (window.pageYOffset > 300) {
        backToTopButton.classList.add('show');
    } else {
        backToTopButton.classList.remove('show');
    }
});

backToTopButton?.addEventListener('click', function() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// 平滑滚动
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});

// 表单提交处理
document.querySelector('.contact-form')?.addEventListener('submit', function(e) {
    e.preventDefault();
    // 这里添加表单提交逻辑
    alert('感谢您的留言，我们会尽快与您联系！');
    this.reset();
}); 