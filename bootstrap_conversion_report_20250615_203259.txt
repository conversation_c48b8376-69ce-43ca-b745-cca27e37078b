Bootstrap 4 到 5 转换报告
==================================================

转换时间: 2025-06-15T20:32:40.081964
处理文件数: 425
修改文件数: 220
总替换次数: 2344

替换类型统计:
  class_d-md-none: 2
  class_d-lg-none: 1
  class_shadow-sm: 1
  class_shadow: 1
  class_shadow-lg: 1
  class_overflow-auto: 1
  class_overflow-hidden: 1
  class_badge-success: 3
  class_badge-warning: 1
  class_btn-outline-primary: 2
  class_btn-outline-success: 1
  structure_1: 1
  class_badge-primary: 2
  class_btn-outline-secondary: 1
  class_btn-outline-danger: 1
  class_btn-outline-warning: 1
  class_custom-file: 2
  class_btn-outline-info: 1
  js_'hidden_bs_modal': 1
  class_custom-checkbox: 2
  class_custom-control: 1
  class_badge-secondary: 1
  class_badge-danger: 2
  class_badge-info: 3
  js__modal('show'): 1
  js__modal('hide'): 1
  class_cleanup: 1
  class_badge-dark: 1
  class_badge-light: 1
  structure_4: 4
  class_badge-pill: 1
  class_custom-radio: 2
  structure_3: 3
  class_form-control-file: 1
  structure_2: 2
  class_custom-switch: 2
  class_nav-tabs: 4
  class_btn-default: 1
  class_form-control-plaintext: 1
  class_modal-dialog-centered: 1
  class_btn-outline-dark: 3
  js_'show_bs_modal': 1
  class_border-top-0: 1
  class_border-bottom-0: 1
  class_btn-outline-light: 4
  class_fixed-top: 1
  class_nav-pills: 1
  structure_8: 8
  structure_5: 5
  structure_6: 6
  js_'shown_bs_modal': 1
  class_text-right: 1
  class_border-left: 1
  class_border-right: 1
  class_mr-(d+): 10
  class_ml-(d+): 1
  class_text-left: 1
  attr_dismiss=: 2
  attr_toggle=: 1
  class_font-weight-bold: 2
  js_'hide_bs_modal': 1
  class_pl-(d+): 2
  class_no-gutters: 2
  class_sr-only: 1

修改的文件:
  app\templates\mobile-demo.html: 6 处修改
  app\templates\mobile_test.html: 6 处修改
  app\templates\theme_demo.html: 9 处修改
  app\templates\_formhelpers.html: 3 处修改
  app\templates\admin\data_management.html: 33 处修改
  app\templates\admin\permission_help.html: 45 处修改
  app\templates\admin\roles.html: 9 处修改
  app\templates\admin\role_permissions.html: 13 处修改
  app\templates\admin\role_templates.html: 20 处修改
  app\templates\admin\users.html: 26 处修改
  app\templates\admin\user_permissions.html: 5 处修改
  app\templates\admin\video_management.html: 10 处修改
  app\templates\admin\view_role.html: 26 处修改
  app\templates\admin\view_user.html: 58 处修改
  app\templates\admin\guide_management\demo_data.html: 17 处修改
  app\templates\admin\guide_management\scenarios.html: 9 处修改
  app\templates\admin\guide_management\users.html: 7 处修改
  app\templates\admin\super_delete\index.html: 3 处修改
  app\templates\admin\system\module_visibility.html: 10 处修改
  app\templates\admin\system\monitor.html: 1 处修改
  app\templates\admin\video_guide\create.html: 2 处修改
  app\templates\admin\video_guide\edit.html: 2 处修改
  app\templates\area\index.html: 5 处修改
  app\templates\area\view_area.html: 40 处修改
  app\templates\batch_flow\form.html: 6 处修改
  app\templates\consultation\detail.html: 4 处修改
  app\templates\consultation\list.html: 4 处修改
  app\templates\consumption_plan\create.html: 3 处修改
  app\templates\consumption_plan\create_from_weekly.html: 22 处修改
  app\templates\consumption_plan\edit.html: 16 处修改
  app\templates\consumption_plan\index.html: 20 处修改
  app\templates\consumption_plan\new.html: 3 处修改
  app\templates\consumption_plan\select_weekly_menu.html: 5 处修改
  app\templates\consumption_plan\super_editor.html: 32 处修改
  app\templates\daily_management\add_companion.html: 2 处修改
  app\templates\daily_management\add_companion_iframe.html: 1 处修改
  app\templates\daily_management\add_event.html: 2 处修改
  app\templates\daily_management\add_inspection_photo.html: 2 处修改
  app\templates\daily_management\add_issue.html: 2 处修改
  app\templates\daily_management\add_training.html: 11 处修改
  app\templates\daily_management\check_photos.html: 6 处修改
  app\templates\daily_management\companions.html: 2 处修改
  app\templates\daily_management\edit_event.html: 2 处修改
  app\templates\daily_management\edit_inspection.html: 5 处修改
  app\templates\daily_management\edit_inspection_new.html: 17 处修改
  app\templates\daily_management\edit_inspection_photo.html: 2 处修改
  app\templates\daily_management\edit_issue.html: 2 处修改
  app\templates\daily_management\edit_training.html: 2 处修改
  app\templates\daily_management\events.html: 2 处修改
  app\templates\daily_management\fixed_inspection_qrcode.html: 10 处修改
  app\templates\daily_management\inspections.html: 28 处修改
  app\templates\daily_management\inspections_new.html: 16 处修改
  app\templates\daily_management\inspection_display.html: 2 处修改
  app\templates\daily_management\inspection_form.html: 10 处修改
  app\templates\daily_management\inspection_templates.html: 22 处修改
  app\templates\daily_management\issues.html: 2 处修改
  app\templates\daily_management\optimized_dashboard.html: 42 处修改
  app\templates\daily_management\public_rate_inspection_photos.html: 6 处修改
  app\templates\daily_management\simplified_dashboard.html: 6 处修改
  app\templates\daily_management\simplified_inspection.html: 10 处修改
  app\templates\daily_management\trainings.html: 4 处修改
  app\templates\daily_management\view_companion.html: 3 处修改
  app\templates\daily_management\view_inspection_photo.html: 3 处修改
  app\templates\daily_management\view_training.html: 3 处修改
  app\templates\daily_management\components\data_visualization.html: 8 处修改
  app\templates\daily_management\components\enhanced_image_uploader.html: 3 处修改
  app\templates\daily_management\components\navigation.html: 14 处修改
  app\templates\daily_management\widgets\image_widget.html: 2 处修改
  app\templates\data_repair\index.html: 5 处修改
  app\templates\data_repair\tools.html: 1 处修改
  app\templates\employee\daily_health_check.html: 2 处修改
  app\templates\employee\employee_form.html: 5 处修改
  app\templates\employee\health_certificates.html: 4 处修改
  app\templates\employee\health_certificate_form.html: 1 处修改
  app\templates\employee\index.html: 32 处修改
  app\templates\employee\medical_examination_form.html: 1 处修改
  app\templates\employee\training_record_form.html: 1 处修改
  app\templates\employee\view_employee.html: 22 处修改
  app\templates\financial\accounting_subjects\form.html: 6 处修改
  app\templates\financial\accounting_subjects\text_tree.html: 2 处修改
  app\templates\financial\payables\pending_stock_ins.html: 2 处修改
  app\templates\financial\reports\trial_balance.html: 2 处修改
  app\templates\financial\reports\voucher_summary.html: 10 处修改
  app\templates\financial\vouchers\edit.html: 1 处修改
  app\templates\financial\vouchers\edit_professional.html: 8 处修改
  app\templates\financial\vouchers\index.html: 8 处修改
  app\templates\financial\vouchers\text_view.html: 6 处修改
  app\templates\financial\vouchers\view.html: 5 处修改
  app\templates\food_sample\create.html: 7 处修改
  app\templates\food_sample\index.html: 3 处修改
  app\templates\food_sample\view.html: 4 处修改
  app\templates\food_trace\index.html: 8 处修改
  app\templates\food_trace\qr_scan.html: 1 处修改
  app\templates\guide\scenario_selection.html: 23 处修改
  app\templates\guide\step_modal.html: 17 处修改
  app\templates\includes\modal.html: 3 处修改
  app\templates\ingredient\categories.html: 14 处修改
  app\templates\ingredient\form.html: 5 处修改
  app\templates\ingredient\index_category.html: 13 处修改
  app\templates\ingredient\turnover.html: 16 处修改
  app\templates\ingredient\view.html: 4 处修改
  app\templates\inspection\direct_index.html: 3 处修改
  app\templates\inspection\edit.html: 13 处修改
  app\templates\inspection\simplified_index.html: 6 处修改
  app\templates\inspection\view.html: 7 处修改
  app\templates\inventory\detail.html: 20 处修改
  app\templates\inventory\expiry.html: 6 处修改
  app\templates\inventory\index.html: 28 处修改
  app\templates\inventory\ingredient.html: 11 处修改
  app\templates\inventory\statistics.html: 41 处修改
  app\templates\inventory_alert\batch_create_requisition.html: 5 处修改
  app\templates\inventory_alert\check.html: 14 处修改
  app\templates\inventory_alert\create_requisition.html: 7 处修改
  app\templates\inventory_alert\index.html: 6 处修改
  app\templates\inventory_alert\view.html: 7 处修改
  app\templates\macros\progress_steps.html: 2 处修改
  app\templates\macros\purchase_order_status.html: 25 处修改
  app\templates\main\canteen_dashboard.html: 2 处修改
  app\templates\main\canteen_dashboard_new.html: 27 处修改
  app\templates\main\food_samples.html: 2 处修改
  app\templates\main\index.html: 48 处修改
  app\templates\main\purchase_orders.html: 4 处修改
  app\templates\main\recipes.html: 5 处修改
  app\templates\main\suppliers.html: 2 处修改
  app\templates\material_batch\form.html: 2 处修改
  app\templates\material_batch\index.html: 5 处修改
  app\templates\material_batch\view.html: 8 处修改
  app\templates\notification\index.html: 10 处修改
  app\templates\notification\view.html: 3 处修改
  app\templates\product_batch\adjust_products.html: 5 处修改
  app\templates\product_batch\approve.html: 10 处修改
  app\templates\product_batch\confirm.html: 2 处修改
  app\templates\product_batch\create.html: 2 处修改
  app\templates\product_batch\index.html: 4 处修改
  app\templates\product_batch\select_ingredients.html: 5 处修改
  app\templates\product_batch\set_attributes.html: 2 处修改
  app\templates\product_batch\view.html: 9 处修改
  app\templates\purchase_order\create_form.html: 24 处修改
  app\templates\purchase_order\create_from_menu.html: 19 处修改
  app\templates\purchase_order\index.html: 61 处修改
  app\templates\purchase_order\view.html: 39 处修改
  app\templates\recipe\categories.html: 4 处修改
  app\templates\recipe\create.html: 2 处修改
  app\templates\recipe\favorites.html: 4 处修改
  app\templates\recipe\form.html: 7 处修改
  app\templates\recipe\form_new.html: 7 处修改
  app\templates\recipe\form_simplified.html: 9 处修改
  app\templates\recipe\index.html: 25 处修改
  app\templates\recipe\view.html: 10 处修改
  app\templates\school_admin\users.html: 3 处修改
  app\templates\school_admin\user_form.html: 1 处修改
  app\templates\security\dashboard.html: 5 处修改
  app\templates\stock_in\batch_editor.html: 4 处修改
  app\templates\stock_in\batch_editor_simplified.html: 19 处修改
  app\templates\stock_in\batch_editor_simplified_scripts.html: 3 处修改
  app\templates\stock_in\batch_editor_step1.html: 4 处修改
  app\templates\stock_in\batch_editor_step2.html: 9 处修改
  app\templates\stock_in\by_ingredient.html: 1 处修改
  app\templates\stock_in\create_from_purchase_order.html: 3 处修改
  app\templates\stock_in\edit.html: 33 处修改
  app\templates\stock_in\form.html: 32 处修改
  app\templates\stock_in\index.html: 24 处修改
  app\templates\stock_in\trace.html: 1 处修改
  app\templates\stock_in\view.html: 37 处修改
  app\templates\stock_in\wizard.html: 15 处修改
  app\templates\stock_in_detail\view.html: 1 处修改
  app\templates\stock_out\edit.html: 2 处修改
  app\templates\stock_out\form.html: 2 处修改
  app\templates\stock_out\index.html: 14 处修改
  app\templates\stock_out\view.html: 18 处修改
  app\templates\stock_out_item\detail.html: 1 处修改
  app\templates\storage_location\form.html: 2 处修改
  app\templates\storage_location\view.html: 9 处修改
  app\templates\supplier\category_index.html: 6 处修改
  app\templates\supplier\certificate_form.html: 5 处修改
  app\templates\supplier\certificate_index.html: 7 处修改
  app\templates\supplier\certificate_view.html: 9 处修改
  app\templates\supplier\form.html: 4 处修改
  app\templates\supplier\index.html: 15 处修改
  app\templates\supplier\product_form.html: 6 处修改
  app\templates\supplier\product_index.html: 26 处修改
  app\templates\supplier\product_view.html: 17 处修改
  app\templates\supplier\school_index.html: 15 处修改
  app\templates\supplier\view.html: 12 处修改
  app\templates\system_fix\permission_audit.html: 10 处修改
  app\templates\system_fix\permission_migration.html: 10 处修改
  app\templates\traceability\batch_trace.html: 1 处修改
  app\templates\traceability\ingredient_trace.html: 5 处修改
  app\templates\trace_document\upload.html: 13 处修改
  app\templates\warehouse\view.html: 8 处修改
  app\templates\warehouse_new\index.html: 3 处修改
  app\templates\warehouse_new\view.html: 3 处修改
  app\templates\weekly_menu\plan.html: 17 处修改
  app\templates\weekly_menu\plan_time_aware.html: 5 处修改
  app\templates\weekly_menu\plan_v2.html: 18 处修改
  app\templates\weekly_menu\view.html: 7 处修改
  app\templates\weekly_menu\weekly_menu(new)\index.html: 3 处修改
  app\templates\weekly_menu\weekly_menu(new)\view.html: 14 处修改
  app\static\css\color-contrast-fix.css: 7 处修改
  app\static\css\dashboard-optimization.css: 28 处修改
  app\static\css\file-upload-fix.css: 28 处修改
  app\static\css\homepage.css: 17 处修改
  app\static\css\mobile-optimization.css: 41 处修改
  app\static\css\process_navigation.css: 8 处修改
  app\static\css\sidebar-layout.css: 5 处修改
  app\static\css\table-optimization.css: 6 处修改
  app\static\css\theme-colors.css: 91 处修改
  app\static\css\weekly_menu_modal.css: 1 处修改
  app\static\js\categorized-ingredient-select.js: 1 处修改
  app\static\js\file-upload-fix.js: 2 处修改
  app\static\js\image_uploader.js: 4 处修改
  app\static\js\main.js: 4 处修改
  app\static\js\menu-switcher.js: 2 处修改
  app\static\js\mobile-table-cards.js: 3 处修改
  app\static\js\progress-steps.js: 2 处修改
  app\static\js\quick_fix_recursion.js: 4 处修改
  app\static\js\stock-in-detail-enhancement.js: 5 处修改
  app\static\js\user_guide.js: 36 处修改
  app\static\js\weekly_menu_modal.js: 11 处修改
  app\static\js\weekly_menu_v2.js: 11 处修改

