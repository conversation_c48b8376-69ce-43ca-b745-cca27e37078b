// 流程导航交互功能
document.addEventListener('DOMContentLoaded', function() {
    // 过滤功能
    const filterButtons = document.querySelectorAll('.process-filter button');
    const processSteps = document.querySelectorAll('.process-step');
    const processConnectors = document.querySelectorAll('.process-connector');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的active类
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // 添加当前按钮的active类
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');

            // 显示或隐藏流程步骤
            processSteps.forEach(step => {
                if (filter === 'all') {
                    step.style.display = 'flex';
                } else {
                    if (step.classList.contains(filter)) {
                        step.style.display = 'flex';
                    } else {
                        step.style.display = 'none';
                    }
                }
            });

            // 处理连接器的显示
            // 先隐藏所有连接器
            processConnectors.forEach(connector => {
                connector.style.display = 'none';
            });

            // 显示需要的连接器
            let visibleSteps = Array.from(processSteps).filter(step => step.style.display !== 'none');
            for (let i = 0; i < visibleSteps.length - 1; i++) {
                let currentStep = visibleSteps[i];
                let nextElement = currentStep.nextElementSibling;

                while (nextElement && !nextElement.classList.contains('process-step')) {
                    if (nextElement.classList.contains('process-connector')) {
                        nextElement.style.display = 'flex';
                        break;
                    }
                    nextElement = nextElement.nextElementSibling;
                }
            }
        });
    });

    // 悬停效果增强
    processSteps.forEach(step => {
        step.addEventListener('mouseenter', function() {
            this.style.zIndex = '20';
        });

        step.addEventListener('mouseleave', function() {
            this.style.zIndex = '10';
        });
    });

    // 响应式调整
    function adjustForMobile() {
        const processStepsContainer = document.querySelector('.process-steps');

        if (window.innerWidth <= 768) {
            // 移动端视图调整
            processStepsContainer.classList.add('mobile-view');
        } else {
            processStepsContainer.classList.remove('mobile-view');
        }
    }

    // 初始调整
    adjustForMobile();

    // 窗口大小变化时调整
    window.addEventListener('resize', adjustForMobile);
});
