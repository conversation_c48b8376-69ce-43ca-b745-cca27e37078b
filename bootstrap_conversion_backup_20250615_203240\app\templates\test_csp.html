{% extends "base.html" %}

{% block title %}CSP Nonce 测试页面{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Content Security Policy (CSP) Nonce 测试</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5>测试说明</h5>
                        <p>这个页面用于测试 CSP nonce 功能是否正常工作。如果您在浏览器控制台中看到以下消息，说明 CSP nonce 配置正确：</p>
                        <ul>
                            <li>✅ "CSP Nonce 测试成功！"</li>
                            <li>✅ "内联样式应用成功！"</li>
                        </ul>
                        <p>如果看到 CSP 违规错误，说明配置需要调整。</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>当前 CSP Nonce</h5>
                            <div class="alert alert-secondary">
                                <code id="nonce-display">{{ csp_nonce }}</code>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>测试结果</h5>
                            <div id="test-results" class="alert alert-warning">
                                正在测试...
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>CSP 策略信息</h5>
                            <div class="alert alert-light">
                                <p><strong>当前策略特点：</strong></p>
                                <ul>
                                    <li>✅ 移除了 <code>'unsafe-eval'</code> 指令</li>
                                    <li>✅ 使用 nonce 替代部分 <code>'unsafe-inline'</code></li>
                                    <li>✅ 严格的资源来源控制</li>
                                    <li>✅ 防止代码注入攻击</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="test-element" id="styled-element">
                        这个元素应该有红色背景（通过内联样式）
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用 nonce 的内联样式 -->
<style nonce="{{ csp_nonce }}">
    .test-element {
        background-color: #ffebee;
        border: 2px solid #f44336;
        padding: 15px;
        border-radius: 5px;
        margin-top: 20px;
        text-align: center;
        font-weight: bold;
    }
    
    .success-message {
        color: #4caf50;
    }
    
    .error-message {
        color: #f44336;
    }
</style>

<!-- 使用 nonce 的内联脚本 -->
<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        console.log('✅ CSP Nonce 测试成功！当前 nonce:', '{{ csp_nonce }}');
        
        // 测试内联样式是否生效
        const styledElement = document.getElementById('styled-element');
        const computedStyle = window.getComputedStyle(styledElement);
        const backgroundColor = computedStyle.backgroundColor;
        
        const testResults = document.getElementById('test-results');
        
        if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
            console.log('✅ 内联样式应用成功！背景色:', backgroundColor);
            testResults.className = 'alert alert-success';
            testResults.innerHTML = '<strong>✅ 测试通过！</strong><br>CSP nonce 配置正确，内联脚本和样式都能正常工作。';
        } else {
            console.warn('⚠️ 内联样式可能未生效');
            testResults.className = 'alert alert-warning';
            testResults.innerHTML = '<strong>⚠️ 部分测试失败</strong><br>内联脚本工作正常，但样式可能有问题。';
        }
        
        // 测试 CSP 违规监听
        document.addEventListener('securitypolicyviolation', function(e) {
            console.error('❌ CSP 违规检测到:', {
                violatedDirective: e.violatedDirective,
                blockedURI: e.blockedURI,
                originalPolicy: e.originalPolicy
            });
            
            testResults.className = 'alert alert-danger';
            testResults.innerHTML = '<strong>❌ CSP 违规！</strong><br>检测到安全策略违规，请检查浏览器控制台。';
        });
        
        // 显示当前页面的 CSP 头信息
        fetch(window.location.href, { method: 'HEAD' })
            .then(response => {
                const cspHeader = response.headers.get('Content-Security-Policy');
                if (cspHeader) {
                    console.log('📋 当前页面的 CSP 策略:', cspHeader);
                } else {
                    console.warn('⚠️ 未检测到 CSP 头');
                }
            })
            .catch(error => {
                console.warn('无法获取 CSP 头信息:', error);
            });
    });
</script>
{% endblock %}
