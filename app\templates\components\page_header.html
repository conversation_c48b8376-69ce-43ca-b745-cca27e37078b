<!-- 标准化页面头部组件 -->
<!-- 使用方法: 在其他模板中 {% from 'components/page_header.html' import page_header, stats_page_header %} -->

{% macro page_header(title, subtitle=None, breadcrumbs=None, actions=None) %}
<div class="d-flex justify-content-between align-items-start mb-3">
  <div class="flex-grow-1">
    <h1 class="h4 mb-1 fw-semibold text-dark">{{ title }}</h1>
    {% if subtitle %}
      <div class="text-muted small">{{ subtitle }}</div>
    {% endif %}
    {% if breadcrumbs %}
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
          {% for crumb in breadcrumbs %}
            <li class="breadcrumb-item{% if loop.last %} active{% endif %}" {% if loop.last %}aria-current="page"{% endif %}>{{ crumb }}</li>
          {% endfor %}
        </ol>
      </nav>
    {% endif %}
  </div>
  {% if actions %}
    <div class="ms-3">
      {{ actions|safe }}
    </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 简化版页面头部 -->
{% macro simple_page_header(title, back_url=None, back_text="返回") %}
<div class="d-flex justify-content-between align-items-center mb-3">
  <h1 class="h4 mb-0 fw-semibold text-dark">{{ title }}</h1>
  {% if back_url %}
  <a href="{{ back_url }}" class="btn btn-outline-secondary btn-sm">
    <i class="fas fa-arrow-left me-1"></i>{{ back_text }}
  </a>
  {% endif %}
</div>
{% endmacro %}

<!-- 带统计信息的页面头部 -->
{% macro stats_page_header(title, stats=None, actions=None) %}
<div class="d-flex justify-content-between align-items-center mb-3">
  <div>
    <h1 class="h4 mb-1 fw-semibold text-dark">{{ title }}</h1>
    {% if stats %}
      <div class="d-flex flex-wrap gap-3 mt-2">
        {% for stat in stats %}
          <div class="bg-light rounded px-3 py-1 small text-muted">
            {{ stat }}
          </div>
        {% endfor %}
      </div>
    {% endif %}
  </div>
  {% if actions %}
    <div>
      {{ actions|safe }}
    </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 搜索和筛选头部 -->
{% macro filter_page_header(title, search_form=None, filter_options=None, actions=None) %}
<div class="mb-3">
  <!-- 标题和操作按钮 -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0 fw-semibold text-dark">{{ title }}</h1>
    {% if actions %}
    <div class="btn-toolbar gap-2">
      {% for action in actions %}
      <a href="{{ action.url }}" 
         class="btn {{ action.class|default('btn-primary') }} {{ action.size|default('btn-sm') }}"
         {% if action.title %}title="{{ action.title }}"{% endif %}>
        {% if action.icon %}<i class="{{ action.icon }}"></i>{% endif %}
        {% if action.text %}{{ action.text }}{% endif %}
      </a>
      {% endfor %}
    </div>
    {% endif %}
  </div>
  
  <!-- 搜索和筛选区域 -->
  {% if search_form or filter_options %}
  <div class="card border-0 bg-light">
    <div class="card-body py-3">
      <div class="row g-3 align-items-end">
        {% if search_form %}
        <div class="col-md-4">
          <div class="input-group">
            <input type="text" class="form-control" placeholder="搜索..." 
                   name="search" value="{{ request.args.get('search', '') }}">
            <button class="btn btn-outline-secondary" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        {% endif %}
        
        {% if filter_options %}
        {% for filter in filter_options %}
        <div class="col-md-2">
          <select class="form-select form-select-sm" name="{{ filter.name }}">
            <option value="">{{ filter.label }}</option>
            {% for option in filter.options %}
            <option value="{{ option.value }}" 
                    {% if request.args.get(filter.name) == option.value %}selected{% endif %}>
              {{ option.text }}
            </option>
            {% endfor %}
          </select>
        </div>
        {% endfor %}
        {% endif %}
        
        <div class="col-md-auto">
          <button type="submit" class="btn btn-primary btn-sm">
            <i class="fas fa-filter me-1"></i>筛选
          </button>
          <a href="{{ request.url_root }}{{ request.endpoint }}" class="btn btn-outline-secondary btn-sm ms-1">
            <i class="fas fa-times me-1"></i>清除
          </a>
        </div>
      </div>
    </div>
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 使用示例注释 -->
<!--
使用示例:

1. 基本页面头部:
{{ page_header(
  title="区域管理",
  subtitle="管理系统中的所有区域信息",
  breadcrumbs=[
    {'title': '首页', 'url': url_for('main.index')},
    {'title': '系统管理', 'url': url_for('admin.index')},
    {'title': '区域管理'}
  ],
  actions=[
    {'url': url_for('area.add'), 'text': '添加区域', 'icon': 'fas fa-plus', 'class': 'btn-primary'}
  ]
) }}

2. 简化页面头部:
{{ simple_page_header(
  title="编辑区域",
  back_url=url_for('area.index'),
  back_text="返回列表"
) }}

3. 带统计的页面头部:
{{ stats_page_header(
  title="库存管理",
  stats=[
    {'label': '总商品', 'value': '1,234', 'icon': 'fas fa-boxes', 'color': 'primary'},
    {'label': '低库存', 'value': '23', 'icon': 'fas fa-exclamation-triangle', 'color': 'warning'},
    {'label': '过期商品', 'value': '5', 'icon': 'fas fa-times-circle', 'color': 'danger'}
  ],
  actions=[
    {'url': url_for('inventory.add'), 'text': '添加商品', 'icon': 'fas fa-plus'}
  ]
) }}

4. 带筛选的页面头部:
{{ filter_page_header(
  title="供应商管理",
  search_form=True,
  filter_options=[
    {
      'name': 'status',
      'label': '状态',
      'options': [
        {'value': 'active', 'text': '活跃'},
        {'value': 'inactive', 'text': '停用'}
      ]
    }
  ],
  actions=[
    {'url': url_for('supplier.add'), 'text': '添加供应商', 'icon': 'fas fa-plus'}
  ]
) }}
-->
