{% extends 'base.html' %}

{% block title %}编辑特殊事件{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
    }
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        margin: -5px;
    }
    .photo-item {
        width: 200px;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }
    .photo-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }
    .photo-caption {
        padding: 8px;
        background-color: #f8f9fc;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">编辑特殊事件</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">事件信息</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" novalidate novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="event_type">事件类型 <span class="text-danger">*</span></label>
                            <select class="form-control" id="event_type" name="event_type" required>
                                <option value="">请选择事件类型</option>
                                <option value="突发事件" {% if event.event_type == '突发事件' %}selected{% endif %}>突发事件</option>
                                <option value="参观访问" {% if event.event_type == '参观访问' %}selected{% endif %}>参观访问</option>
                                <option value="检查督导" {% if event.event_type == '检查督导' %}selected{% endif %}>检查督导</option>
                                <option value="食品安全培训" {% if event.event_type == '食品安全培训' %}selected{% endif %}>食品安全培训</option>
                                <option value="其他" {% if event.event_type == '其他' %}selected{% endif %}>其他</option>
                                {% if event.event_type not in ['突发事件', '参观访问', '检查督导', '食品安全培训', '其他'] %}
                                <option value="{{ event.event_type }}" selected>{{ event.event_type }}</option>
                                {% endif %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="event_date">事件日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="event_date" name="event_date" value="{{ event.event_time.strftime('%Y-%m-%d') }}" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="event_time">事件时间 <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="event_time" name="event_time" value="{{ event.event_time.strftime('%H:%M') }}" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description">事件描述 <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="3" required>{{ event.description }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="participants">参与人员</label>
                    <textarea class="form-control" id="participants" name="participants" rows="2">{{ event.participants or '' }}</textarea>
                    <small class="form-text text-muted">多个人员请用逗号分隔</small>
                </div>

                <div class="mb-3">
                    <label for="handling_measures">处理措施</label>
                    <textarea class="form-control" id="handling_measures" name="handling_measures" rows="3">{{ event.handling_measures or '' }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="event_summary">事件总结</label>
                    <textarea class="form-control" id="event_summary" name="event_summary" rows="3">{{ event.event_summary or '' }}</textarea>
                </div>

                <!-- 现有照片 -->
                <div class="mb-3">
                    <label>现有照片</label>
                    <div class="photo-gallery">
                        {% set photos = event.photos %}
                        {% if photos %}
                            {% for photo in photos %}
                                <div class="photo-item">
                                    <a href="{{ photo.file_path }}" target="_blank">
                                        <img src="{{ photo.file_path }}" alt="事件照片">
                                    </a>
                                    <div class="photo-caption">
                                        {{ photo.description or '无描述' }}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">暂无照片</p>
                        {% endif %}
                    </div>
                </div>

                <div class="mb-3">
                    <label for="photos">上传新照片</label>
                    <input type="file" class="form-control-file" id="photos" name="photos" multiple accept="image/*">
                    <small class="form-text text-muted">可以选择多张照片上传，支持jpg、jpeg、png格式</small>
                    <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
                </div>

                <div class="mb-3 mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('daily_management.view_event', event_id=event.id) }}" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 照片预览
    document.getElementById('photos').addEventListener('change', function(e) {
        const previewsDiv = document.getElementById('photo-previews');
        previewsDiv.innerHTML = '';

        for (const file of this.files) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = document.createElement('img');
                img.src = event.target.result;
                img.className = 'photo-preview me-2 mb-2';
                previewsDiv.appendChild(img);
            }
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
