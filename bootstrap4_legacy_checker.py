#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 4 遗留代码检查工具
全面扫描项目中是否还有Bootstrap 4的遗留代码
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime

class Bootstrap4LegacyChecker:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.issues = {
            "bootstrap4_classes": [],
            "bootstrap4_attributes": [],
            "bootstrap4_structures": [],
            "bootstrap4_js": [],
            "deprecated_features": []
        }
        self.files_scanned = 0
        
        # Bootstrap 4 遗留类名模式
        self.bootstrap4_class_patterns = [
            # 表单控件
            r'\bcustom-select\b',
            r'\bcustom-file\b',
            r'\bcustom-file-input\b',
            r'\bcustom-file-label\b',
            r'\bcustom-checkbox\b',
            r'\bcustom-radio\b',
            r'\bcustom-switch\b',
            r'\bcustom-control\b',
            r'\bcustom-control-input\b',
            r'\bcustom-control-label\b',
            r'\bform-control-file\b',
            r'\bform-control-range\b',
            
            # 徽章
            r'\bbadge-primary\b',
            r'\bbadge-secondary\b',
            r'\bbadge-success\b',
            r'\bbadge-danger\b',
            r'\bbadge-warning\b',
            r'\bbadge-info\b',
            r'\bbadge-light\b',
            r'\bbadge-dark\b',
            r'\bbadge-pill\b',
            
            # 按钮
            r'\bbtn-default\b',
            
            # 文本对齐
            r'\btext-left\b',
            r'\btext-right\b',
            r'\bfloat-left\b',
            r'\bfloat-right\b',
            
            # 边框
            r'\bborder-left\b',
            r'\bborder-right\b',
            r'\brounded-left\b',
            r'\brounded-right\b',
            
            # 间距 (Bootstrap 4 格式)
            r'\bml-\d+\b',
            r'\bmr-\d+\b',
            r'\bpl-\d+\b',
            r'\bpr-\d+\b',
            
            # 网格
            r'\bno-gutters\b',
            
            # 卡片
            r'\bcard-deck\b',
            r'\bcard-columns\b',
            
            # 工具类
            r'\bsr-only\b',
            r'\bsr-only-focusable\b',
            
            # 字体
            r'\bfont-weight-bold\b',
            r'\bfont-weight-normal\b',
            r'\bfont-italic\b',
            
            # 输入组
            r'\binput-group-prepend\b',
            r'\binput-group-append\b',
            
            # 媒体对象
            r'\bmedia\b',
            r'\bmedia-object\b',
            r'\bmedia-body\b',
        ]
        
        # Bootstrap 4 属性模式
        self.bootstrap4_attr_patterns = [
            r'data-toggle="modal"',
            r'data-toggle="dropdown"',
            r'data-toggle="collapse"',
            r'data-toggle="tooltip"',
            r'data-toggle="popover"',
            r'data-toggle="tab"',
            r'data-toggle="pill"',
            r'data-target="[^"]*"',
            r'data-dismiss="modal"',
            r'data-dismiss="alert"',
            r'data-placement="[^"]*"',
        ]
        
        # Bootstrap 4 结构模式
        self.bootstrap4_structure_patterns = [
            r'<button[^>]*class="[^"]*close[^"]*"[^>]*>\s*<span[^>]*>&times;</span>\s*</button>',
            r'<div class="input-group-prepend">',
            r'<div class="input-group-append">',
            r'<div class="media">',
            r'<div class="media-object">',
            r'<div class="media-body">',
        ]
        
        # Bootstrap 4 JavaScript 模式
        self.bootstrap4_js_patterns = [
            r"\.modal\('toggle'\)",
            r"\.modal\('show'\)",
            r"\.modal\('hide'\)",
            r"\.dropdown\('toggle'\)",
            r"\.collapse\('toggle'\)",
            r"\.collapse\('show'\)",
            r"\.collapse\('hide'\)",
            r"'show\.bs\.modal'",
            r"'shown\.bs\.modal'",
            r"'hide\.bs\.modal'",
            r"'hidden\.bs\.modal'",
        ]

    def check_file(self, file_path):
        """检查单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            file_issues = []
            
            # 检查Bootstrap 4类名
            for pattern in self.bootstrap4_class_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    file_issues.append({
                        "type": "bootstrap4_class",
                        "pattern": pattern,
                        "match": match.group(),
                        "line": line_num,
                        "file": str(file_path.relative_to(self.project_root))
                    })
            
            # 检查Bootstrap 4属性
            for pattern in self.bootstrap4_attr_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    file_issues.append({
                        "type": "bootstrap4_attribute",
                        "pattern": pattern,
                        "match": match.group(),
                        "line": line_num,
                        "file": str(file_path.relative_to(self.project_root))
                    })
            
            # 检查Bootstrap 4结构
            for pattern in self.bootstrap4_structure_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    file_issues.append({
                        "type": "bootstrap4_structure",
                        "pattern": pattern,
                        "match": match.group()[:100] + "..." if len(match.group()) > 100 else match.group(),
                        "line": line_num,
                        "file": str(file_path.relative_to(self.project_root))
                    })
            
            # 检查JavaScript（如果是JS文件或包含script标签）
            if file_path.suffix.lower() in {'.js'} or '<script' in content:
                for pattern in self.bootstrap4_js_patterns:
                    matches = re.finditer(pattern, content)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        file_issues.append({
                            "type": "bootstrap4_js",
                            "pattern": pattern,
                            "match": match.group(),
                            "line": line_num,
                            "file": str(file_path.relative_to(self.project_root))
                        })
            
            # 按类型分类问题
            for issue in file_issues:
                issue_type = issue["type"]
                if issue_type == "bootstrap4_class":
                    self.issues["bootstrap4_classes"].append(issue)
                elif issue_type == "bootstrap4_attribute":
                    self.issues["bootstrap4_attributes"].append(issue)
                elif issue_type == "bootstrap4_structure":
                    self.issues["bootstrap4_structures"].append(issue)
                elif issue_type == "bootstrap4_js":
                    self.issues["bootstrap4_js"].append(issue)
            
            self.files_scanned += 1
            
            if file_issues:
                print(f"⚠️  {file_path.relative_to(self.project_root)} - 发现 {len(file_issues)} 个问题")
                
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")

    def scan_project(self):
        """扫描整个项目"""
        print("🔍 扫描项目中的Bootstrap 4遗留代码...")
        
        # 扫描目录
        scan_dirs = [
            self.project_root / "app" / "templates",
            self.project_root / "app" / "static" / "css",
            self.project_root / "app" / "static" / "js"
        ]
        
        for scan_dir in scan_dirs:
            if not scan_dir.exists():
                continue
                
            print(f"📁 扫描目录: {scan_dir.relative_to(self.project_root)}")
            
            # 扫描HTML, CSS, JS文件
            for pattern in ["*.html", "*.css", "*.js"]:
                for file_path in scan_dir.rglob(pattern):
                    self.check_file(file_path)

    def generate_report(self):
        """生成检查报告"""
        total_issues = sum(len(issues) for issues in self.issues.values())
        
        print(f"\n📊 检查完成:")
        print(f"   扫描文件: {self.files_scanned}")
        print(f"   发现问题: {total_issues}")
        
        if total_issues > 0:
            print(f"\n⚠️  问题分类:")
            for issue_type, issues in self.issues.items():
                if issues:
                    print(f"   {issue_type}: {len(issues)} 个")
            
            # 生成详细报告
            report_file = self.project_root / f"bootstrap4_legacy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "scan_time": datetime.now().isoformat(),
                    "files_scanned": self.files_scanned,
                    "total_issues": total_issues,
                    "issues": self.issues
                }, f, indent=2, ensure_ascii=False)
            
            # 生成文本摘要
            summary_file = report_file.with_suffix('.txt')
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("Bootstrap 4 遗留代码检查报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"扫描时间: {datetime.now().isoformat()}\n")
                f.write(f"扫描文件: {self.files_scanned}\n")
                f.write(f"发现问题: {total_issues}\n\n")
                
                for issue_type, issues in self.issues.items():
                    if issues:
                        f.write(f"{issue_type.upper()} ({len(issues)} 个):\n")
                        f.write("-" * 30 + "\n")
                        for issue in issues:
                            f.write(f"  文件: {issue['file']}\n")
                            f.write(f"  行号: {issue['line']}\n")
                            f.write(f"  模式: {issue['pattern']}\n")
                            f.write(f"  匹配: {issue['match']}\n")
                            f.write("\n")
                        f.write("\n")
            
            print(f"\n📄 详细报告已生成:")
            print(f"   {report_file}")
            print(f"   {summary_file}")
        else:
            print(f"\n✅ 恭喜！没有发现Bootstrap 4遗留代码")

    def run(self):
        """运行检查工具"""
        print("🚀 Bootstrap 4 遗留代码检查工具启动")
        print("=" * 50)
        
        self.scan_project()
        self.generate_report()

if __name__ == "__main__":
    checker = Bootstrap4LegacyChecker()
    checker.run()
