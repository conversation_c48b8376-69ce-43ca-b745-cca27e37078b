{% extends "base.html" %}

{% block title %}移动端优化演示 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">📱 移动端优化演示</h1>
            <p class="text-muted">展示系统在移动端的优化效果和交互体验</p>
        </div>
    </div>

    <!-- 移动端特性展示 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🎯 移动端优化特性</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-12 mobile-mb-3">
                            <div class="quick-actions">
                                <button class="btn btn-primary">
                                    <i class="fas fa-mobile-alt"></i>
                                    触控优化
                                </button>
                                <button class="btn btn-success">
                                    <i class="fas fa-hand-pointer"></i>
                                    手势支持
                                </button>
                                <button class="btn btn-info">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                    响应式布局
                                </button>
                                <button class="btn btn-warning">
                                    <i class="fas fa-eye"></i>
                                    视觉优化
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <div class="mobile-success-message mobile-only">
                                <div class="mobile-success-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="mobile-success-title">移动端优化已启用</div>
                                <div class="mobile-success-text">您正在体验专为移动设备优化的界面</div>
                            </div>
                            <div class="desktop-only">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    请在移动设备或调整浏览器窗口大小至768px以下查看移动端优化效果
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表单优化演示 -->
    <div class="row mobile-mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📝 表单优化演示</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="mb-3">
                            <label for="demo-text">文本输入框</label>
                            <input type="text" class="form-control" id="demo-text" placeholder="支持防缩放和焦点优化">
                        </div>
                        
                        <div class="mb-3">
                            <label for="demo-number">数字输入框（带步进器）</label>
                            <input type="number" class="form-control" id="demo-number" value="10" min="0" max="100" step="1">
                        </div>
                        
                        <div class="mb-3">
                            <label for="demo-select">选择框（长列表自动添加搜索）</label>
                            <select class="form-control" id="demo-select">
                                <option>选项 1</option>
                                <option>选项 2</option>
                                <option>选项 3</option>
                                <option>选项 4</option>
                                <option>选项 5</option>
                                <option>选项 6</option>
                                <option>选项 7</option>
                                <option>选项 8</option>
                                <option>选项 9</option>
                                <option>选项 10</option>
                                <option>选项 11</option>
                                <option>选项 12</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="demo-textarea">文本域</label>
                            <textarea class="form-control" id="demo-textarea" rows="3" placeholder="支持智能滚动和焦点管理"></textarea>
                        </div>
                        
                        <div class="form-check mobile-mb-3">
                            <input class="form-check-input" type="checkbox" id="demo-check">
                            <label class="form-check-label" for="demo-check">
                                复选框（优化触控目标大小）
                            </label>
                        </div>
                        
                        <div class="action-buttons">
                            <button type="submit" class="btn btn-primary">提交表单</button>
                            <button type="reset" class="btn btn-secondary">重置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格优化演示 -->
    <div class="row mobile-mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📊 表格优化演示</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>姓名</th>
                                    <th>部门</th>
                                    <th>职位</th>
                                    <th>状态</th>
                                    <th class="action-column">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>张三</td>
                                    <td>技术部</td>
                                    <td>前端工程师</td>
                                    <td><span class="badge badge-success">在职</span></td>
                                    <td class="action-column">
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>李四</td>
                                    <td>产品部</td>
                                    <td>产品经理</td>
                                    <td><span class="badge badge-warning">请假</span></td>
                                    <td class="action-column">
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>王五</td>
                                    <td>设计部</td>
                                    <td>UI设计师</td>
                                    <td><span class="badge badge-success">在职</span></td>
                                    <td class="action-column">
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="text-muted mobile-text-center mobile-mt-2">
                        <small>移动端会自动添加"切换视图"按钮，支持表格和卡片视图切换</small>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 交互演示 -->
    <div class="row mobile-mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🎮 交互演示</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-12 mobile-mb-3">
                            <h6>触控反馈</h6>
                            <p class="text-muted">点击按钮体验触控反馈效果</p>
                            <button class="btn btn-outline-primary mobile-full-width">点击我体验触控反馈</button>
                        </div>
                        <div class="col-md-6 col-12">
                            <h6>长按操作</h6>
                            <p class="text-muted">长按下方按钮体验长按功能</p>
                            <button class="btn btn-outline-success mobile-full-width" data-long-press>长按我试试</button>
                        </div>
                    </div>
                    
                    <hr class="mobile-mt-3 mobile-mb-3">
                    
                    <div class="row">
                        <div class="col-12">
                            <h6>滑动手势</h6>
                            <p class="text-muted">在下方区域左右滑动体验手势识别</p>
                            <div class="swipe-indicator" data-swipe style="background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; padding: 40px; text-align: center; margin: 16px 0;">
                                <i class="fas fa-hand-paper" style="font-size: 32px; color: #6c757d; margin-bottom: 12px;"></i>
                                <div>在此区域左右滑动</div>
                                <small class="text-muted">支持左滑和右滑手势识别</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框演示 -->
    <div class="row mobile-mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🪟 模态框优化演示</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">移动端模态框支持向下滑动关闭</p>
                    <button type="button" class="btn btn-primary mobile-full-width" data-bs-toggle="modal" data-bs-target="#demoModal">
                        打开移动端优化模态框
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 演示模态框 -->
<div class="modal fade" id="demoModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">移动端优化模态框</h5>
                <button type="button" class="close" data-bs-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>这是一个针对移动端优化的模态框：</p>
                <ul>
                    <li>支持向下滑动关闭</li>
                    <li>自适应屏幕高度</li>
                    <li>优化的按钮布局</li>
                    <li>更好的触控体验</li>
                </ul>
                <div class="mb-3">
                    <label for="modal-input">示例输入框</label>
                    <input type="text" class="form-control" id="modal-input" placeholder="在模态框中的输入框">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary">确认</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    // 长按事件演示
    document.addEventListener('longpress', function(e) {
        alert('检测到长按操作！');
    });
    
    // 滑动事件演示
    document.addEventListener('swipe', function(e) {
        const direction = e.detail.direction;
        const target = e.detail.target;
        
        if (target.hasAttribute('data-swipe')) {
            target.innerHTML = `
                <i class="fas fa-hand-paper" style="font-size: 32px; color: var(--theme-primary); margin-bottom: 12px;"></i>
                <div>检测到${direction === 'left' ? '左' : '右'}滑手势！</div>
                <small class="text-muted">继续滑动体验更多</small>
            `;
            
            setTimeout(() => {
                target.innerHTML = `
                    <i class="fas fa-hand-paper" style="font-size: 32px; color: #6c757d; margin-bottom: 12px;"></i>
                    <div>在此区域左右滑动</div>
                    <small class="text-muted">支持左滑和右滑手势识别</small>
                `;
            }, 2000);
        }
    });
});
</script>
{% endblock %}
