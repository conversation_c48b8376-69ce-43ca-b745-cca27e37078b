/**
 * CSP 辅助脚本
 * 用于处理动态创建的脚本和样式
 */

(function() {
    'use strict';
    
    // 获取当前页面的 nonce
    const getCurrentNonce = () => {
        const scripts = document.querySelectorAll('script[nonce]');
        return scripts.length > 0 ? scripts[0].getAttribute('nonce') : '';
    };
    
    // 创建带 nonce 的脚本元素
    window.createScriptWithNonce = function(src, content) {
        const script = document.createElement('script');
        const nonce = getCurrentNonce();
        
        if (nonce) {
            script.setAttribute('nonce', nonce);
        }
        
        if (src) {
            script.src = src;
        }
        
        if (content) {
            script.textContent = content;
        }
        
        return script;
    };
    
    // 创建带 nonce 的样式元素
    window.createStyleWithNonce = function(content) {
        const style = document.createElement('style');
        const nonce = getCurrentNonce();
        
        if (nonce) {
            style.setAttribute('nonce', nonce);
        }
        
        if (content) {
            style.textContent = content;
        }
        
        return style;
    };
    
    // 安全地执行代码
    window.safeEval = function(code) {
        try {
            const script = createScriptWithNonce(null, code);
            document.head.appendChild(script);
            document.head.removeChild(script);
        } catch (e) {
            console.error('Safe eval failed:', e);
        }
    };
    
    console.log('✅ CSP 辅助脚本已加载');
})();