{% extends 'base.html' %}

{% block title %}学校陪餐二维码{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .qrcode-container {
        text-align: center;
        margin: 20px 0;
    }
    .qrcode-image {
        max-width: 300px;
        margin: 0 auto;
    }
    .qrcode-info {
        margin-top: 20px;
        font-size: 16px;
    }
    .qrcode-url {
        word-break: break-all;
        margin-top: 10px;
        font-size: 14px;
        color: #666;
    }
    .download-btn {
        margin-top: 20px;
    }
    .school-info {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
    }
    .school-select {
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">
        学校陪餐二维码
    </h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">
                学校陪餐二维码生成器
            </h6>
        </div>
        <div class="card-body">
            <!-- 当前学校信息 -->
            <div class="school-info mb-4">
                <div class="alert alert-info">
                    <h5 class="mb-0">
                        <i class="fas fa-school"></i> 当前学校: <strong>{{ selected_school.name }}</strong>
                    </h5>
                </div>
            </div>

            {% if selected_school %}

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 fw-bold text-primary">陪餐记录入口二维码（推荐）</h6>
                        </div>
                        <div class="card-body">
                            <div class="qrcode-container">
                                {% if qrcode_base64 %}
                                <img src="data:image/png;base64,{{ qrcode_base64 }}" alt="陪餐记录入口二维码" class="qrcode-image img-thumbnail">
                                {% else %}
                                <div class="alert alert-warning">二维码生成失败</div>
                                {% endif %}
                                <!-- 调试信息 -->
                                {% if qrcode_base64 %}
                                <div class="d-none">二维码数据长度: {{ qrcode_base64|length }}</div>
                                {% else %}
                                <div class="alert alert-warning mt-2">二维码数据为空</div>
                                {% endif %}

                                <div class="qrcode-info">
                                    <p>扫描此二维码，进入陪餐记录主页面</p>
                                    <p class="qrcode-url">
                                        链接: {{ url_for('daily_management.companion_entry', school_id=selected_school.id, _external=True) }}
                                    </p>
                                </div>

                                <div class="download-btn">
                                    <a href="{{ url_for('static', filename=qrcode_path) }}" download="陪餐记录入口_{{ selected_school.name }}.png" class="btn btn-primary">
                                        <i class="fas fa-download"></i> 下载二维码
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 fw-bold text-primary">直接添加陪餐记录二维码</h6>
                        </div>
                        <div class="card-body">
                            <div class="qrcode-container">
                                {% if direct_qrcode_base64 %}
                                <img src="data:image/png;base64,{{ direct_qrcode_base64 }}" alt="直接添加陪餐记录二维码" class="qrcode-image img-thumbnail">
                                {% else %}
                                <div class="alert alert-warning">二维码生成失败</div>
                                {% endif %}
                                <!-- 调试信息 -->
                                {% if direct_qrcode_base64 %}
                                <div class="d-none">二维码数据长度: {{ direct_qrcode_base64|length }}</div>
                                {% else %}
                                <div class="alert alert-warning mt-2">二维码数据为空</div>
                                {% endif %}

                                <div class="qrcode-info">
                                    <p>扫描此二维码，直接填写陪餐记录</p>
                                    <p class="qrcode-url">
                                        链接: {{ url_for('daily_management.public_add_companion', school_id=selected_school.id, _external=True) }}
                                    </p>
                                </div>

                                <div class="download-btn">
                                    <a href="{{ url_for('static', filename=direct_qrcode_path) }}" download="直接添加陪餐记录_{{ selected_school.name }}.png" class="btn btn-primary">
                                        <i class="fas fa-download"></i> 下载二维码
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alert alert-info mt-4">
                <h5><i class="fas fa-info-circle"></i> 使用说明</h5>
                <ol>
                    <li>将此二维码打印出来，放置在食堂或学校公共区域</li>
                    <li>陪餐人员可通过扫描二维码直接填写陪餐记录</li>
                    <li>无需登录系统，即可完成陪餐记录的提交</li>
                    <li>提交的记录将自动关联到当前日期的工作日志</li>
                    <li><strong>每个学校的二维码是固定的</strong>，不会随时间变化</li>
                </ol>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 请选择一个学校来生成陪餐二维码
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
