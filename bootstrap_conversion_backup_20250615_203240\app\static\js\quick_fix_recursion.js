/**
 * 快速修复递归问题的脚本
 * 可以直接在浏览器控制台中运行
 * 或作为静态文件引入
 */

(function() {
    'use strict';
    
    console.log('🚀 开始修复递归问题...');
    
    // 1. 立即停止所有可能的递归调用
    function stopRecursion() {
        console.log('⏹️ 停止递归调用...');
        
        // 清除所有定时器
        const highestTimeoutId = setTimeout(function(){}, 0);
        for (let i = 0; i < highestTimeoutId; i++) {
            clearTimeout(i);
        }
        
        const highestIntervalId = setInterval(function(){}, 0);
        for (let i = 0; i < highestIntervalId; i++) {
            clearInterval(i);
        }
        
        console.log('✅ 已清除所有定时器');
    }
    
    // 2. 移除所有可能导致问题的事件监听器
    function removeProblematicEvents() {
        console.log('🧹 移除问题事件监听器...');
        
        const problematicSelectors = [
            '#dropZone',
            '#fileInput', 
            '#saveDocuments',
            '.upload-doc-btn',
            '.remove-file',
            '.custom-file',
            'input[type="file"]',
            '.upload-area'
        ];
        
        problematicSelectors.forEach(selector => {
            try {
                $(selector).off();
                console.log(`✅ 已清除 ${selector} 的所有事件`);
            } catch (e) {
                console.log(`⚠️ 清除 ${selector} 事件时出错:`, e.message);
            }
        });
        
        // 移除文档级别的委托事件
        try {
            $(document).off('click', '.remove-file');
            $(document).off('click', '.upload-doc-btn');
            console.log('✅ 已清除文档级别的委托事件');
        } catch (e) {
            console.log('⚠️ 清除文档事件时出错:', e.message);
        }
    }
    
    // 3. 重新绑定安全的事件监听器
    function rebindSafeEvents() {
        console.log('🔧 重新绑定安全事件...');
        
        // 文件上传区域点击事件
        $('#dropZone').on('click.safeFix', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('安全点击上传区域');
            
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.click();
            }
        });
        
        // 文件选择变化事件
        $('#fileInput').on('change.safeFix', function(e) {
            console.log('安全文件选择变化');
            if (this.files && this.files.length > 0) {
                console.log('选择了文件:', this.files.length);
                // 这里可以调用文件处理函数
                if (typeof handleFiles === 'function') {
                    handleFiles(this.files);
                }
            }
        });
        
        // 保存文档按钮事件
        $('#saveDocuments').on('click.safeFix', function(e) {
            e.preventDefault();
            console.log('安全保存文档');
            alert('文档保存功能（安全版本）');
        });
        
        // 删除文件按钮事件（使用事件委托）
        $(document).on('click.safeFix', '.remove-file', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('安全删除文件');
            $(this).closest('.alert').remove();
        });
        
        // 上传文档按钮事件（使用事件委托）
        $(document).on('click.safeFix', '.upload-doc-btn', function(e) {
            e.preventDefault();
            console.log('安全上传文档按钮');
            // 这里可以调用上传模态框
        });
        
        console.log('✅ 安全事件绑定完成');
    }
    
    // 4. 修复文件输入元素样式
    function fixFileInputStyles() {
        console.log('🎨 修复文件输入样式...');
        
        const style = document.createElement('style');
        style.id = 'recursion-fix-styles';
        style.textContent = `
            /* 修复文件上传样式 */
            .upload-area {
                position: relative !important;
                cursor: pointer !important;
            }
            
            .upload-area input[type="file"] {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                opacity: 0 !important;
                cursor: pointer !important;
                z-index: 2 !important;
            }
            
            .custom-file {
                position: relative !important;
                cursor: pointer !important;
            }
            
            .custom-file-input {
                position: absolute !important;
                opacity: 0 !important;
                width: 100% !important;
                height: 100% !important;
                cursor: pointer !important;
                z-index: 1 !important;
            }
            
            .custom-file-label {
                cursor: pointer !important;
            }
        `;
        
        // 移除旧样式
        const oldStyle = document.getElementById('recursion-fix-styles');
        if (oldStyle) {
            oldStyle.remove();
        }
        
        document.head.appendChild(style);
        console.log('✅ 样式修复完成');
    }
    
    // 5. 检查修复结果
    function checkFixResult() {
        console.log('🔍 检查修复结果...');
        
        const checks = [
            {
                name: '文件上传区域',
                selector: '#dropZone',
                test: () => $('#dropZone').length > 0
            },
            {
                name: '文件输入元素', 
                selector: '#fileInput',
                test: () => $('#fileInput').length > 0
            },
            {
                name: '保存按钮',
                selector: '#saveDocuments', 
                test: () => $('#saveDocuments').length > 0
            }
        ];
        
        checks.forEach(check => {
            if (check.test()) {
                const events = $._data($(check.selector)[0], 'events');
                const eventCount = events ? Object.keys(events).length : 0;
                console.log(`✅ ${check.name}: 存在, 事件数: ${eventCount}`);
            } else {
                console.log(`⚠️ ${check.name}: 不存在`);
            }
        });
    }
    
    // 6. 主修复函数
    function performFix() {
        try {
            stopRecursion();
            removeProblematicEvents();
            fixFileInputStyles();
            
            // 等待一下再重新绑定事件
            setTimeout(() => {
                rebindSafeEvents();
                checkFixResult();
                console.log('🎉 递归问题修复完成！');
                
                // 显示修复结果
                if (typeof alert !== 'undefined') {
                    alert('递归问题已修复！请重新测试文件上传功能。');
                }
            }, 100);
            
        } catch (error) {
            console.error('❌ 修复过程中出错:', error);
        }
    }
    
    // 立即执行修复
    performFix();
    
    // 导出修复函数到全局，以便手动调用
    window.fixRecursionProblem = performFix;
    
    console.log('💡 如需重新修复，请在控制台运行: fixRecursionProblem()');
    
})();
