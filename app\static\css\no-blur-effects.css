/* 彻底移除所有模糊效果的CSS */
/* 这个文件专门用于覆盖和移除任何可能的模糊效果 */

/* 移除所有backdrop-filter模糊效果 */
* {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保所有元素都有清晰的背景 */
.navbar,
.dropdown-menu,
.modal,
.modal-content,
.modal-header,
.modal-body,
.modal-footer,
.card,
.card-header,
.card-body,
.card-footer,
.alert,
.toast,
.popover,
.tooltip,
.offcanvas,
.offcanvas-header,
.offcanvas-body {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保所有背景都是实色，不透明 */
.navbar {
    background: #ffffff !important;
    background-color: #ffffff !important;
}

.dropdown-menu {
    background: #ffffff !important;
    background-color: #ffffff !important;
}

.modal-content {
    background: #ffffff !important;
    background-color: #ffffff !important;
}

.card {
    background: #ffffff !important;
    background-color: #ffffff !important;
}

.card-header {
    background: #f8f9fa !important;
    background-color: #f8f9fa !important;
}

/* 移除任何可能的玻璃效果类 */
.glass-effect,
.glass,
.blur-effect,
.backdrop-blur {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
}

/* 确保所有透明度都是完全不透明 */
.bg-transparent {
    background: #ffffff !important;
    background-color: #ffffff !important;
}

/* 移除任何rgba透明背景，改为实色 */
[style*="rgba"] {
    background: #ffffff !important;
}

/* 特殊处理一些可能有模糊效果的组件 */
.sidebar,
.sidebar-header,
.sidebar-nav,
.top-toolbar,
.content-area {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保所有阴影都是清晰的，不模糊 */
.shadow,
.shadow-sm,
.shadow-lg {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

/* 移除任何可能的CSS滤镜效果 */
img,
.img-thumbnail,
.figure-img {
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保按钮背景清晰 */
.btn {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 移除表格的任何模糊效果 */
.table,
.table-responsive {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保表单元素清晰 */
.form-control,
.form-select,
.form-check-input,
.form-range {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 移除导航相关的模糊效果 */
.nav,
.nav-tabs,
.nav-pills,
.navbar-nav,
.navbar-brand {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保分页组件清晰 */
.pagination,
.page-link {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 移除面包屑的模糊效果 */
.breadcrumb {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保徽章清晰 */
.badge {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 移除进度条的模糊效果 */
.progress,
.progress-bar {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保列表组清晰 */
.list-group,
.list-group-item {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 移除任何自定义的模糊类 */
.modern-card,
.modern-btn,
.modern-nav,
.modern-table,
.modern-form,
.modern-badge,
.modern-pagination,
.modern-modal,
.modern-alert,
.modern-progress {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 最后的保险措施：移除所有可能的模糊效果 */
*:before,
*:after {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保所有伪元素也没有模糊效果 */
::before,
::after {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}
