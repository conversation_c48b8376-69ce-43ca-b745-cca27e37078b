{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>{{ title }}</h2>
      <p class="text-muted">多个区域存在相同ID的菜单，请选择要操作的区域</p>
    </div>
    <div class="col-md-4 text-end">
      <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5>ID为 {{ menu_id }} 的菜单列表</h5>
        </div>
        <div class="card-body">
          <div class="list-group">
            {% for menu in menus %}
            {% if action == 'publish' %}
              <a href="{{ url_for('weekly_menu.publish', id=menu.id, area_id=menu.area_id) }}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">{{ menu.area.name }}</h5>
                  <small>{{ menu.week_start.strftime('%Y-%m-%d') }} 至 {{ menu.week_end.strftime('%Y-%m-%d') }}</small>
                </div>
                <p class="mb-1">状态: {{ menu.status }}</p>
                <small>创建时间: {{ menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
              </a>
            {% elif action == 'delete' %}
              <a href="{{ url_for('weekly_menu.delete_menu', id=menu.id, area_id=menu.area_id) }}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">{{ menu.area.name }}</h5>
                  <small>{{ menu.week_start.strftime('%Y-%m-%d') }} 至 {{ menu.week_end.strftime('%Y-%m-%d') }}</small>
                </div>
                <p class="mb-1">状态: {{ menu.status }}</p>
                <small>创建时间: {{ menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
              </a>
            {% elif action == 'print' %}
              <a href="{{ url_for('weekly_menu.print_menu', id=menu.id, area_id=menu.area_id) }}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">{{ menu.area.name }}</h5>
                  <small>{{ menu.week_start.strftime('%Y-%m-%d') }} 至 {{ menu.week_end.strftime('%Y-%m-%d') }}</small>
                </div>
                <p class="mb-1">状态: {{ menu.status }}</p>
                <small>创建时间: {{ menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
              </a>
            {% else %}
              <a href="{{ url_for('weekly_menu.view', id=menu.id, area_id=menu.area_id) }}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">{{ menu.area.name }}</h5>
                  <small>{{ menu.week_start.strftime('%Y-%m-%d') }} 至 {{ menu.week_end.strftime('%Y-%m-%d') }}</small>
                </div>
                <p class="mb-1">状态: {{ menu.status }}</p>
                <small>创建时间: {{ menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
              </a>
            {% endif %}
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
