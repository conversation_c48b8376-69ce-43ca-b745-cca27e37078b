<!-- 标准化卡片组件 -->
<!-- 使用方法: {% from 'components/cards.html' import info_card, data_card, action_card %} -->

<!-- 信息展示卡片 -->
{% macro info_card(title, content=None, icon=None, color="primary", size="normal") %}
<div class="card h-100 {% if size == 'small' %}card-sm{% endif %}">
  {% if title %}
  <div class="card-header bg-{{ color }} text-white py-2">
    <h6 class="card-title mb-0 d-flex align-items-center">
      {% if icon %}<i class="{{ icon }} me-2"></i>{% endif %}
      {{ title }}
    </h6>
  </div>
  {% endif %}
  
  {% if content %}
  <div class="card-body {% if size == 'small' %}py-2{% endif %}">
    {{ content|safe }}
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 数据统计卡片 -->
{% macro data_card(title, value, subtitle=None, icon=None, color="primary", trend=None) %}
<div class="card h-100 border-0 shadow-sm">
  <div class="card-body text-center">
    {% if icon %}
    <div class="mb-3">
      <div class="d-inline-flex align-items-center justify-content-center 
                  bg-{{ color }} bg-opacity-10 rounded-circle" 
           style="width: 60px; height: 60px;">
        <i class="{{ icon }} text-{{ color }} fs-4"></i>
      </div>
    </div>
    {% endif %}
    
    <h3 class="fw-bold text-{{ color }} mb-1">{{ value }}</h3>
    <h6 class="text-muted mb-0">{{ title }}</h6>
    
    {% if subtitle %}
    <small class="text-muted">{{ subtitle }}</small>
    {% endif %}
    
    {% if trend %}
    <div class="mt-2">
      <span class="badge bg-{{ trend.color }} bg-opacity-10 text-{{ trend.color }}">
        <i class="fas fa-{{ trend.icon }} me-1"></i>{{ trend.text }}
      </span>
    </div>
    {% endif %}
  </div>
</div>
{% endmacro %}

<!-- 操作卡片 -->
{% macro action_card(title, description=None, icon=None, actions=None, color="light") %}
<div class="card h-100 border-0 shadow-sm">
  <div class="card-body text-center">
    {% if icon %}
    <div class="mb-3">
      <div class="d-inline-flex align-items-center justify-content-center 
                  bg-{{ color }} bg-opacity-10 rounded-circle" 
           style="width: 60px; height: 60px;">
        <i class="{{ icon }} text-{{ color }} fs-4"></i>
      </div>
    </div>
    {% endif %}
    
    <h5 class="card-title">{{ title }}</h5>
    
    {% if description %}
    <p class="card-text text-muted small">{{ description }}</p>
    {% endif %}
    
    {% if actions %}
    <div class="mt-3">
      {% for action in actions %}
      <a href="{{ action.url }}" 
         class="btn {{ action.class|default('btn-primary') }} {{ action.size|default('btn-sm') }} me-1"
         {% if action.title %}title="{{ action.title }}"{% endif %}>
        {% if action.icon %}<i class="{{ action.icon }}"></i>{% endif %}
        {% if action.text %}{{ action.text }}{% endif %}
      </a>
      {% endfor %}
    </div>
    {% endif %}
  </div>
</div>
{% endmacro %}

<!-- 列表卡片 -->
{% macro list_card(title, items=None, actions=None, color="primary") %}
<div class="card h-100">
  {% if title %}
  <div class="card-header bg-{{ color }} text-white py-2">
    <div class="d-flex justify-content-between align-items-center">
      <h6 class="card-title mb-0">{{ title }}</h6>
      {% if actions %}
      <div class="btn-group btn-group-sm">
        {% for action in actions %}
        <a href="{{ action.url }}" 
           class="btn btn-outline-light btn-sm"
           {% if action.title %}title="{{ action.title }}"{% endif %}>
          {% if action.icon %}<i class="{{ action.icon }}"></i>{% endif %}
          {% if action.text %}{{ action.text }}{% endif %}
        </a>
        {% endfor %}
      </div>
      {% endif %}
    </div>
  </div>
  {% endif %}
  
  {% if items %}
  <div class="list-group list-group-flush">
    {% for item in items %}
    <div class="list-group-item d-flex justify-content-between align-items-center py-2">
      <div class="flex-grow-1">
        {% if item.icon %}
        <i class="{{ item.icon }} text-{{ item.color|default('muted') }} me-2"></i>
        {% endif %}
        <span class="{% if item.strong %}fw-semibold{% endif %}">{{ item.text }}</span>
        {% if item.subtitle %}
        <small class="text-muted d-block">{{ item.subtitle }}</small>
        {% endif %}
      </div>
      
      {% if item.value %}
      <span class="badge bg-{{ item.badge_color|default('secondary') }}">{{ item.value }}</span>
      {% endif %}
      
      {% if item.actions %}
      <div class="btn-group btn-group-sm ms-2">
        {% for action in item.actions %}
        <a href="{{ action.url }}" 
           class="btn btn-outline-secondary btn-sm"
           {% if action.title %}title="{{ action.title }}"{% endif %}>
          <i class="{{ action.icon }}"></i>
        </a>
        {% endfor %}
      </div>
      {% endif %}
    </div>
    {% endfor %}
  </div>
  {% else %}
  <div class="card-body text-center text-muted">
    <i class="fas fa-inbox fs-1 mb-3 opacity-50"></i>
    <p class="mb-0">暂无数据</p>
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 表格卡片 -->
{% macro table_card(title, headers=None, rows=None, actions=None, pagination=None) %}
<div class="card">
  {% if title %}
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h6 class="card-title mb-0">{{ title }}</h6>
      {% if actions %}
      <div class="btn-toolbar gap-2">
        {% for action in actions %}
        <a href="{{ action.url }}" 
           class="btn {{ action.class|default('btn-primary') }} btn-sm"
           {% if action.title %}title="{{ action.title }}"{% endif %}>
          {% if action.icon %}<i class="{{ action.icon }}"></i>{% endif %}
          {% if action.text %}{{ action.text }}{% endif %}
        </a>
        {% endfor %}
      </div>
      {% endif %}
    </div>
  </div>
  {% endif %}
  
  {% if headers and rows %}
  <div class="table-responsive">
    <table class="table table-hover table-sm mb-0">
      <thead class="table-light">
        <tr>
          {% for header in headers %}
          <th class="{% if header.class %}{{ header.class }}{% endif %}">
            {{ header.text }}
          </th>
          {% endfor %}
        </tr>
      </thead>
      <tbody>
        {% for row in rows %}
        <tr>
          {% for cell in row %}
          <td class="{% if cell.class %}{{ cell.class }}{% endif %}">
            {% if cell.type == 'link' %}
            <a href="{{ cell.url }}" class="text-decoration-none">{{ cell.text }}</a>
            {% elif cell.type == 'badge' %}
            <span class="badge bg-{{ cell.color|default('secondary') }}">{{ cell.text }}</span>
            {% elif cell.type == 'actions' %}
            <div class="btn-group btn-group-sm">
              {% for action in cell.actions %}
              <a href="{{ action.url }}" 
                 class="btn btn-outline-secondary btn-sm"
                 {% if action.title %}title="{{ action.title }}"{% endif %}>
                <i class="{{ action.icon }}"></i>
              </a>
              {% endfor %}
            </div>
            {% else %}
            {{ cell.text }}
            {% endif %}
          </td>
          {% endfor %}
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  
  {% if pagination %}
  <div class="card-footer">
    {{ pagination }}
  </div>
  {% endif %}
  
  {% else %}
  <div class="card-body text-center text-muted">
    <i class="fas fa-table fs-1 mb-3 opacity-50"></i>
    <p class="mb-0">暂无数据</p>
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 紧凑型卡片网格 -->
{% macro compact_card_grid(cards, cols="lg-4 md-6") %}
<div class="row g-3">
  {% for card in cards %}
  <div class="col-{{ cols }}">
    {% if card.type == 'data' %}
    {{ data_card(
      title=card.title,
      value=card.value,
      subtitle=card.subtitle,
      icon=card.icon,
      color=card.color,
      trend=card.trend
    ) }}
    {% elif card.type == 'action' %}
    {{ action_card(
      title=card.title,
      description=card.description,
      icon=card.icon,
      actions=card.actions,
      color=card.color
    ) }}
    {% else %}
    {{ info_card(
      title=card.title,
      content=card.content,
      icon=card.icon,
      color=card.color,
      size=card.size
    ) }}
    {% endif %}
  </div>
  {% endfor %}
</div>
{% endmacro %}
