/* 增强型图片上传器样式 */
.enhanced-uploader-dropzone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 15px;
    background-color: #f8f9fa;
}

.enhanced-uploader-dropzone:hover,
.enhanced-uploader-dropzone.dragover {
    border-color: #4e73df;
    background-color: #eef2ff;
}

.enhanced-uploader-icon {
    font-size: 2rem;
    color: #4e73df;
    margin-bottom: 10px;
}

.enhanced-uploader-text {
    color: #666;
    margin-bottom: 10px;
}

.enhanced-uploader-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.enhanced-uploader-item {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s;
}

.enhanced-uploader-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.enhanced-uploader-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.enhanced-uploader-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.enhanced-uploader-item:hover .enhanced-uploader-item-overlay {
    opacity: 1;
}

.enhanced-uploader-item-actions {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

.enhanced-uploader-item-action {
    color: white;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
}

.enhanced-uploader-item-action:hover {
    background: rgba(0,0,0,0.7);
}

.enhanced-uploader-rating {
    margin-top: 10px;
}

.enhanced-uploader-rating i {
    color: #ffc107;
    cursor: pointer;
    margin: 0 2px;
}

.enhanced-uploader-status {
    padding: 10px;
    margin-top: 10px;
    border-radius: 4px;
    text-align: center;
}

.enhanced-uploader-status.success {
    background-color: #d4edda;
    color: #155724;
}

.enhanced-uploader-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 进度条样式 */
.enhanced-uploader-progress {
    width: 100%;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    margin-top: 5px;
}

.enhanced-uploader-progress-bar {
    height: 100%;
    background-color: #4e73df;
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .enhanced-uploader-item {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 576px) {
    .enhanced-uploader-item {
        width: 100px;
        height: 100px;
    }
} 