#!/usr/bin/env python3
"""
Bootstrap 5.3.6 模板重构自动化工具
用于批量重构StudentsCMSSP项目的模板文件
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple
import argparse

class TemplateRefactorTool:
    def __init__(self, templates_dir: str = "app/templates"):
        self.templates_dir = Path(templates_dir)
        self.refactor_rules = self._load_refactor_rules()
        self.component_mappings = self._load_component_mappings()
        
    def _load_refactor_rules(self) -> Dict:
        """加载重构规则"""
        return {
            # Bootstrap 4 到 5 的类名映射
            'class_mappings': {
                'ml-': 'ms-',
                'mr-': 'me-',
                'pl-': 'ps-',
                'pr-': 'pe-',
                'text-left': 'text-start',
                'text-right': 'text-end',
                'float-left': 'float-start',
                'float-right': 'float-end',
                'border-left': 'border-start',
                'border-right': 'border-end',
                'rounded-left': 'rounded-start',
                'rounded-right': 'rounded-end',
                'badge-pill': 'rounded-pill',
                'close': 'btn-close',
                'sr-only': 'visually-hidden',
                'font-weight-bold': 'fw-bold',
                'font-weight-normal': 'fw-normal',
                'font-italic': 'fst-italic',
                'text-monospace': 'font-monospace',
                'custom-control': 'form-check',
                'custom-control-input': 'form-check-input',
                'custom-control-label': 'form-check-label',
                'custom-select': 'form-select',
                'form-group': 'mb-3',
                'input-group-prepend': '',
                'input-group-append': '',
                'card-deck': 'row row-cols-1 row-cols-md-2 row-cols-lg-3',
                'media': 'd-flex',
                'media-object': 'flex-shrink-0',
                'media-body': 'flex-grow-1 ms-3',
                'jumbotron': 'bg-light p-5 rounded-3',
                'list-inline-item': '',
                'btn-block': 'btn w-100',
                'form-inline': 'd-flex',
                'navbar-expand': 'navbar-expand-lg',
                'dropdown-menu-right': 'dropdown-menu-end',
                'dropdown-menu-left': 'dropdown-menu-start',
                'dropleft': 'dropstart',
                'dropright': 'dropend',
                'btn-outline-dark': 'btn-outline-secondary',
                'text-hide': 'd-none',
                'embed-responsive': 'ratio',
                'embed-responsive-item': '',
                'no-gutters': 'g-0'
            },
            
            # jQuery 到原生 JavaScript 的转换
            'js_mappings': {
                r'\$\(document\)\.ready\(function\(\)\s*\{': 'document.addEventListener(\'DOMContentLoaded\', function() {',
                r'\$\(\'([^\']+)\'\)\.click\(': 'document.querySelectorAll(\'$1\').forEach(el => el.addEventListener(\'click\', ',
                r'\$\(\'([^\']+)\'\)\.on\(\'([^\']+)\',': 'document.querySelectorAll(\'$1\').forEach(el => el.addEventListener(\'$2\', ',
                r'\$\(\'([^\']+)\'\)\.addClass\(\'([^\']+)\'\)': 'document.querySelectorAll(\'$1\').forEach(el => el.classList.add(\'$2\'))',
                r'\$\(\'([^\']+)\'\)\.removeClass\(\'([^\']+)\'\)': 'document.querySelectorAll(\'$1\').forEach(el => el.classList.remove(\'$2\'))',
                r'\$\(\'([^\']+)\'\)\.toggleClass\(\'([^\']+)\'\)': 'document.querySelectorAll(\'$1\').forEach(el => el.classList.toggle(\'$2\'))',
                r'\$\(\'([^\']+)\'\)\.hide\(\)': 'document.querySelectorAll(\'$1\').forEach(el => el.style.display = \'none\')',
                r'\$\(\'([^\']+)\'\)\.show\(\)': 'document.querySelectorAll(\'$1\').forEach(el => el.style.display = \'\')',
                r'\$\(\'([^\']+)\'\)\.text\(': 'document.querySelector(\'$1\').textContent = ',
                r'\$\(\'([^\']+)\'\)\.html\(': 'document.querySelector(\'$1\').innerHTML = ',
                r'\$\(\'([^\']+)\'\)\.val\(\)': 'document.querySelector(\'$1\').value',
                r'\$\(\'([^\']+)\'\)\.attr\(\'([^\']+)\'\)': 'document.querySelector(\'$1\').getAttribute(\'$2\')',
                r'\$\.ajax\(': 'fetch(',
                r'data-toggle': 'data-bs-toggle',
                r'data-target': 'data-bs-target',
                r'data-dismiss': 'data-bs-dismiss',
                r'data-placement': 'data-bs-placement',
                r'data-container': 'data-bs-container'
            },
            
            # 模板结构优化
            'template_patterns': {
                # 旧的卡片结构
                r'<div class="card">\s*<div class="card-header">\s*<h[1-6][^>]*>([^<]+)</h[1-6]>\s*</div>': 
                '<div class="card border-0 shadow-sm"><div class="card-header bg-primary text-white py-2"><h6 class="card-title mb-0">$1</h6></div>',
                
                # 旧的按钮组
                r'<div class="btn-group">\s*<a[^>]+class="btn btn-([^"]+)"[^>]*>([^<]+)</a>':
                '<div class="btn-group btn-group-sm"><a class="btn btn-outline-$1" href="#">$2</a>',
                
                # 旧的表格结构
                r'<table class="table">\s*<thead>':
                '<div class="table-responsive"><table class="table table-hover table-sm"><thead class="table-light">',
                
                # 旧的表单结构
                r'<div class="form-group">\s*<label[^>]*>([^<]+)</label>\s*<input([^>]+)>':
                '<div class="mb-3"><label class="form-label">$1</label><input$2 class="form-control">',
                
                # 旧的导航结构
                r'<nav class="navbar navbar-expand-lg">\s*<div class="container">':
                '<nav class="navbar navbar-expand-lg bg-light"><div class="container-fluid">',
            }
        }
    
    def _load_component_mappings(self) -> Dict:
        """加载组件映射"""
        return {
            'imports': {
                'page_header': "{% from 'components/page_header.html' import page_header, simple_page_header %}",
                'cards': "{% from 'components/cards.html' import info_card, data_card, table_card %}",
                'forms': "{% from 'components/forms.html' import modern_form_field, form_card %}",
                'layout': "{% from 'components/layout.html' import content_section, responsive_grid %}",
                'data_display': "{% from 'components/data_display.html' import data_table, stat_card %}"
            },
            
            'replacements': {
                # 页面头部替换
                'page_title_pattern': r'<h[1-6][^>]*>([^<]+)</h[1-6]>\s*<p[^>]*>([^<]+)</p>',
                'page_title_replacement': "{{ page_header(title='$1', subtitle='$2') }}",
                
                # 统计卡片替换
                'stat_card_pattern': r'<div class="card[^"]*bg-([^"]+)[^"]*">[^<]*<div class="card-body">[^<]*<h[1-6][^>]*>([^<]+)</h[1-6]>[^<]*<h[1-6][^>]*>([^<]+)</h[1-6]>',
                'stat_card_replacement': "{{ stat_card(title='$2', value='$3', color='$1') }}",
                
                # 表格替换
                'table_pattern': r'<div class="table-responsive">\s*<table class="table[^"]*">',
                'table_replacement': "{{ data_table(headers, rows, {'responsive': True, 'hover': True}) }}",
            }
        }
    
    def analyze_template(self, file_path: Path) -> Dict:
        """分析模板文件，识别需要重构的内容"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        analysis = {
            'file': str(file_path),
            'issues': [],
            'suggestions': [],
            'complexity_score': 0
        }
        
        # 检查Bootstrap 4特有的类名
        for old_class, new_class in self.refactor_rules['class_mappings'].items():
            if old_class in content:
                analysis['issues'].append(f"发现Bootstrap 4类名: {old_class}")
                analysis['suggestions'].append(f"替换为: {new_class}")
                analysis['complexity_score'] += 1
        
        # 检查jQuery使用
        jquery_patterns = [r'\$\(', r'jQuery\(', r'\.ajax\(', r'\.click\(', r'\.ready\(']
        for pattern in jquery_patterns:
            if re.search(pattern, content):
                analysis['issues'].append(f"发现jQuery使用: {pattern}")
                analysis['suggestions'].append("建议替换为原生JavaScript")
                analysis['complexity_score'] += 2
        
        # 检查旧的HTML结构
        old_structures = [
            r'<div class="form-group">',
            r'<div class="input-group-prepend">',
            r'<div class="input-group-append">',
            r'class="[^"]*ml-[^"]*"',
            r'class="[^"]*mr-[^"]*"',
            r'data-toggle=',
            r'data-target='
        ]
        
        for pattern in old_structures:
            if re.search(pattern, content):
                analysis['issues'].append(f"发现旧HTML结构: {pattern}")
                analysis['complexity_score'] += 1
        
        # 计算文件复杂度
        lines = content.split('\n')
        analysis['line_count'] = len(lines)
        analysis['complexity_score'] += len(lines) // 50  # 每50行增加1分复杂度
        
        return analysis
    
    def refactor_template(self, file_path: Path, backup: bool = True) -> bool:
        """重构单个模板文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 备份原文件
            if backup:
                backup_path = file_path.with_suffix(file_path.suffix + '.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            # 应用类名映射
            for old_class, new_class in self.refactor_rules['class_mappings'].items():
                content = content.replace(old_class, new_class)
            
            # 应用JavaScript映射
            for pattern, replacement in self.refactor_rules['js_mappings'].items():
                content = re.sub(pattern, replacement, content)
            
            # 应用模板结构优化
            for pattern, replacement in self.refactor_rules['template_patterns'].items():
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
            
            # 添加组件导入（如果需要）
            if self._needs_component_imports(content):
                imports = self._generate_imports(content)
                content = self._add_imports_to_template(content, imports)
            
            # 优化HTML结构
            content = self._optimize_html_structure(content)
            
            # 写入重构后的内容
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"重构文件 {file_path} 时出错: {e}")
            return False
    
    def _needs_component_imports(self, content: str) -> bool:
        """检查是否需要添加组件导入"""
        component_indicators = [
            'card', 'table', 'form', 'btn-group', 'navbar'
        ]
        return any(indicator in content for indicator in component_indicators)
    
    def _generate_imports(self, content: str) -> List[str]:
        """根据内容生成需要的导入语句"""
        imports = []
        
        if 'card' in content:
            imports.append(self.component_mappings['imports']['cards'])
        if 'table' in content:
            imports.append(self.component_mappings['imports']['data_display'])
        if 'form' in content:
            imports.append(self.component_mappings['imports']['forms'])
        if any(x in content for x in ['<h1', '<h2', '<h3']):
            imports.append(self.component_mappings['imports']['page_header'])
        
        return list(set(imports))  # 去重
    
    def _add_imports_to_template(self, content: str, imports: List[str]) -> str:
        """在模板中添加导入语句"""
        if not imports:
            return content
        
        # 查找extends语句后的位置
        extends_match = re.search(r'{% extends [^%]+%}', content)
        if extends_match:
            insert_pos = extends_match.end()
            import_block = '\n' + '\n'.join(imports) + '\n'
            content = content[:insert_pos] + import_block + content[insert_pos:]
        
        return content
    
    def _optimize_html_structure(self, content: str) -> str:
        """优化HTML结构"""
        # 移除多余的空白行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 优化缩进
        lines = content.split('\n')
        optimized_lines = []
        indent_level = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                optimized_lines.append('')
                continue
            
            # 计算缩进级别
            if stripped.startswith('</') or stripped.startswith('{% end'):
                indent_level = max(0, indent_level - 1)
            
            optimized_lines.append('  ' * indent_level + stripped)
            
            if (stripped.startswith('<') and not stripped.startswith('</') and 
                not stripped.endswith('/>') and not stripped.endswith('>')):
                indent_level += 1
            elif stripped.startswith('{% ') and not stripped.startswith('{% end'):
                indent_level += 1
        
        return '\n'.join(optimized_lines)
    
    def batch_refactor(self, pattern: str = "**/*.html", dry_run: bool = False) -> Dict:
        """批量重构模板文件"""
        results = {
            'processed': 0,
            'modified': 0,
            'errors': 0,
            'files': []
        }
        
        for file_path in self.templates_dir.glob(pattern):
            if file_path.is_file():
                results['processed'] += 1
                
                if dry_run:
                    analysis = self.analyze_template(file_path)
                    results['files'].append(analysis)
                else:
                    success = self.refactor_template(file_path)
                    if success:
                        results['modified'] += 1
                    else:
                        results['errors'] += 1
                    
                    results['files'].append({
                        'file': str(file_path),
                        'status': 'modified' if success else 'error'
                    })
        
        return results
    
    def generate_report(self, results: Dict) -> str:
        """生成重构报告"""
        report = f"""
# Bootstrap 5.3.6 重构报告

## 总体统计
- 处理文件数: {results['processed']}
- 修改文件数: {results['modified']}
- 错误文件数: {results['errors']}

## 详细结果
"""
        
        for file_info in results['files']:
            if 'issues' in file_info:  # 分析模式
                report += f"\n### {file_info['file']}\n"
                report += f"- 复杂度评分: {file_info['complexity_score']}\n"
                report += f"- 发现问题: {len(file_info['issues'])}\n"
                for issue in file_info['issues'][:5]:  # 只显示前5个问题
                    report += f"  - {issue}\n"
            else:  # 重构模式
                report += f"- {file_info['file']}: {file_info['status']}\n"
        
        return report

def main():
    parser = argparse.ArgumentParser(description='Bootstrap 5.3.6 模板重构工具')
    parser.add_argument('--templates-dir', default='app/templates', help='模板目录路径')
    parser.add_argument('--pattern', default='**/*.html', help='文件匹配模式')
    parser.add_argument('--dry-run', action='store_true', help='只分析不修改')
    parser.add_argument('--no-backup', action='store_true', help='不创建备份文件')
    parser.add_argument('--output', help='报告输出文件')
    
    args = parser.parse_args()
    
    tool = TemplateRefactorTool(args.templates_dir)
    
    print(f"开始处理模板文件: {args.pattern}")
    print(f"模式: {'分析模式' if args.dry_run else '重构模式'}")
    
    results = tool.batch_refactor(args.pattern, args.dry_run)
    
    print(f"\n处理完成!")
    print(f"处理文件: {results['processed']}")
    if not args.dry_run:
        print(f"修改文件: {results['modified']}")
        print(f"错误文件: {results['errors']}")
    
    # 生成报告
    report = tool.generate_report(results)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {args.output}")
    else:
        print(report)

if __name__ == '__main__':
    main()
