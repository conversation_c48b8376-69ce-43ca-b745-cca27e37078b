/**
 * 移动端下拉菜单修复脚本
 * 解决移动端Bootstrap dropdown不展开的问题
 */

(function() {
    'use strict';

    // 检测是否为移动设备
    function isMobileDevice() {
        return window.innerWidth <= 768 || 
               /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 移动端下拉菜单修复
    function fixMobileDropdown() {
        console.log('初始化移动端下拉菜单修复...');

        // 获取所有下拉菜单触发器
        const dropdownToggles = document.querySelectorAll('.navbar-nav .dropdown-toggle');
        
        dropdownToggles.forEach(function(toggle) {
            // 移除原有事件监听器
            toggle.removeEventListener('click', handleDropdownClick);
            
            // 添加新的事件监听器
            toggle.addEventListener('click', handleDropdownClick);
            toggle.addEventListener('touchstart', handleDropdownTouch, { passive: true });
        });

        console.log(`已修复 ${dropdownToggles.length} 个下拉菜单`);
    }

    // 处理下拉菜单点击事件
    function handleDropdownClick(e) {
        e.preventDefault();
        e.stopPropagation();

        const toggle = e.currentTarget;
        const dropdownMenu = toggle.nextElementSibling;
        const parentLi = toggle.closest('.dropdown');

        if (!dropdownMenu || !parentLi) {
            console.warn('下拉菜单结构不正确');
            return;
        }

        // 关闭其他打开的下拉菜单
        closeAllDropdowns(parentLi);

        // 切换当前下拉菜单
        toggleDropdown(parentLi, dropdownMenu, toggle);
    }

    // 处理触摸事件
    function handleDropdownTouch(e) {
        // 为触摸设备添加特殊处理
        const toggle = e.currentTarget;
        toggle.classList.add('touch-active');
        
        setTimeout(() => {
            toggle.classList.remove('touch-active');
        }, 200);
    }

    // 切换下拉菜单状态
    function toggleDropdown(parentLi, dropdownMenu, toggle) {
        const isOpen = parentLi.classList.contains('show');

        if (isOpen) {
            // 关闭菜单
            closeDropdown(parentLi, dropdownMenu, toggle);
        } else {
            // 打开菜单
            openDropdown(parentLi, dropdownMenu, toggle);
        }
    }

    // 打开下拉菜单
    function openDropdown(parentLi, dropdownMenu, toggle) {
        parentLi.classList.add('show');
        dropdownMenu.classList.add('show');
        toggle.setAttribute('aria-expanded', 'true');

        // 添加动画效果
        dropdownMenu.style.opacity = '0';
        dropdownMenu.style.transform = 'translateY(-10px)';
        
        requestAnimationFrame(() => {
            dropdownMenu.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            dropdownMenu.style.opacity = '1';
            dropdownMenu.style.transform = 'translateY(0)';
        });

        // 添加点击外部关闭功能
        setTimeout(() => {
            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('touchstart', handleOutsideClick);
        }, 100);

        console.log('下拉菜单已打开:', toggle.textContent.trim());
    }

    // 关闭下拉菜单
    function closeDropdown(parentLi, dropdownMenu, toggle) {
        parentLi.classList.remove('show');
        dropdownMenu.classList.remove('show');
        toggle.setAttribute('aria-expanded', 'false');

        // 移除动画样式
        dropdownMenu.style.transition = '';
        dropdownMenu.style.opacity = '';
        dropdownMenu.style.transform = '';

        console.log('下拉菜单已关闭:', toggle.textContent.trim());
    }

    // 关闭所有下拉菜单
    function closeAllDropdowns(except = null) {
        const openDropdowns = document.querySelectorAll('.navbar-nav .dropdown.show');
        
        openDropdowns.forEach(function(dropdown) {
            if (dropdown !== except) {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                
                if (toggle && menu) {
                    closeDropdown(dropdown, menu, toggle);
                }
            }
        });

        // 移除外部点击监听器
        document.removeEventListener('click', handleOutsideClick);
        document.removeEventListener('touchstart', handleOutsideClick);
    }

    // 处理点击外部关闭
    function handleOutsideClick(e) {
        const clickedElement = e.target;
        const isInsideDropdown = clickedElement.closest('.navbar-nav .dropdown');
        
        if (!isInsideDropdown) {
            closeAllDropdowns();
        }
    }

    // 处理窗口大小变化
    function handleResize() {
        // 如果从移动端切换到桌面端，重新初始化
        if (!isMobileDevice()) {
            closeAllDropdowns();
        }
    }

    // Bootstrap dropdown 事件监听器
    function setupBootstrapEvents() {
        // 监听 Bootstrap dropdown 事件
        $(document).on('show.bs.dropdown', '.navbar-nav .dropdown', function(e) {
            console.log('Bootstrap dropdown 显示事件');
        });

        $(document).on('hide.bs.dropdown', '.navbar-nav .dropdown', function(e) {
            console.log('Bootstrap dropdown 隐藏事件');
        });

        // 强制启用 Bootstrap dropdown
        $('.navbar-nav .dropdown-toggle').dropdown();
    }

    // 添加移动端特定样式
    function addMobileStyles() {
        if (isMobileDevice()) {
            const style = document.createElement('style');
            style.textContent = `
                .navbar-nav .dropdown-menu {
                    position: static !important;
                    float: none !important;
                    width: 100% !important;
                    margin-top: 0 !important;
                    background-color: rgba(0, 0, 0, 0.05) !important;
                    border: none !important;
                    border-radius: 0 !important;
                    box-shadow: none !important;
                    transform: none !important;
                }
                
                .navbar-nav .dropdown-toggle.touch-active {
                    background-color: rgba(255, 255, 255, 0.1);
                }
                
                .navbar-nav .dropdown-item {
                    padding: 12px 24px;
                    color: rgba(255, 255, 255, 0.9);
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                }
                
                .navbar-nav .dropdown-item:hover,
                .navbar-nav .dropdown-item:focus {
                    background-color: rgba(255, 255, 255, 0.1) !important;
                    color: white !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // 初始化函数
    function init() {
        console.log('移动端下拉菜单修复脚本开始初始化...');
        console.log('当前设备类型:', isMobileDevice() ? '移动设备' : '桌面设备');

        // 等待 DOM 和 Bootstrap 加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(init, 100);
            });
            return;
        }

        // 等待 jQuery 和 Bootstrap 加载
        if (typeof $ === 'undefined' || typeof $.fn.dropdown === 'undefined') {
            setTimeout(init, 100);
            return;
        }

        // 添加移动端样式
        addMobileStyles();

        // 设置 Bootstrap 事件
        setupBootstrapEvents();

        // 修复移动端下拉菜单
        fixMobileDropdown();

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);

        console.log('移动端下拉菜单修复完成!');
    }

    // 启动初始化
    init();

    // 导出函数供调试使用
    window.mobileDropdownFix = {
        init: init,
        fixMobileDropdown: fixMobileDropdown,
        closeAllDropdowns: closeAllDropdowns,
        isMobileDevice: isMobileDevice
    };

})();
