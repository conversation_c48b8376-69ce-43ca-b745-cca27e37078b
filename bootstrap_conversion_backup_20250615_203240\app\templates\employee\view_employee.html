{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ employee.name }} - 员工详情</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> 编辑信息
        </a>
        <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">基本信息</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if employee.photo %}
                    <img src="{{ url_for('static', filename=employee.photo) }}" alt="{{ employee.name }}" class="img-thumbnail" style="max-height: 200px;">
                    {% else %}
                    <div class="bg-light p-5 rounded-circle mb-3">
                        <i class="fas fa-user fa-5x text-muted"></i>
                    </div>
                    {% endif %}
                    <h4>{{ employee.name }}</h4>
                    <p class="text-muted">{{ employee.position }} - {{ employee.department }}</p>
                </div>

                <table class="table table-sm">
                    <tr>
                        <th>性别</th>
                        <td>{{ employee.gender }}</td>
                    </tr>
                    <tr>
                        <th>出生日期</th>
                        <td>{{  employee.birth_date|format_datetime('%Y-%m-%d') if employee.birth_date else '未设置'   }}</td>
                    </tr>
                    <tr>
                        <th>联系电话</th>
                        <td>{{ employee.phone }}</td>
                    </tr>
                    <tr>
                        <th>住址</th>
                        <td>{{ employee.address or '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>入职日期</th>
                        <td>{{  employee.entry_date|format_datetime('%Y-%m-%d')   }}</td>
                    </tr>
                    <tr>
                        <th>状态</th>
                        <td>
                            {% if employee.status == 1 %}
                            <span class="badge badge-success">在职</span>
                            {% elif employee.status == 0 %}
                            <span class="badge badge-secondary">离职</span>
                            {% elif employee.status == 2 %}
                            <span class="badge badge-warning">休假</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>所属区域</th>
                        <td>
                            {% if employee.area %}
                            <span class="badge badge-info">{{ employee.area.get_level_name() }}</span>
                            {{ employee.area.name }}
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% if employee.leave_date %}
                    <tr>
                        <th>离职日期</th>
                        <td>{{  employee.leave_date|format_datetime('%Y-%m-%d')   }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- 食品安全责任 -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">食品安全责任</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <th>负责区域</th>
                        <td>
                            {% if employee.responsible_areas %}
                                {% set areas = employee.responsible_areas.split(',') %}
                                {% for area in areas %}
                                    <span class="badge badge-info">{{ area }}</span>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>食品安全证书</th>
                        <td>
                            {% if employee.food_safety_certifications %}
                                {% set certs = employee.food_safety_certifications.split(',') %}
                                {% for cert in certs %}
                                    <span class="badge badge-success">{{ cert }}</span>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>安全违规次数</th>
                        <td>
                            {% if employee.safety_violation_count and employee.safety_violation_count > 0 %}
                                <span class="badge badge-danger">{{ employee.safety_violation_count }}</span>
                            {% else %}
                                <span class="badge badge-success">0</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>最近健康检查</th>
                        <td>
                            {% if employee.last_health_check_date %}
                                {{ employee.last_health_check_date|format_datetime('%Y-%m-%d') }}
                                {% if employee.health_status == '正常' %}
                                    <span class="badge badge-success">{{ employee.health_status }}</span>
                                {% else %}
                                    <span class="badge badge-danger">{{ employee.health_status }}</span>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">未检查</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 系统账号信息 -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">系统账号信息</h5>
            </div>
            <div class="card-body">
                {% if employee.user %}
                    <table class="table table-sm">
                        <tr>
                            <th>用户名</th>
                            <td>{{ employee.user.username }}</td>
                        </tr>
                        <tr>
                            <th>状态</th>
                            <td>
                                {% if employee.user.status == 1 %}
                                    <span class="badge badge-success">启用</span>
                                {% else %}
                                    <span class="badge badge-danger">禁用</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>角色</th>
                            <td>
                                {% for role in employee.user.roles %}
                                    <span class="badge badge-primary">{{ role.name }}</span>
                                {% endfor %}
                            </td>
                        </tr>
                        <tr>
                            <th>最后登录</th>
                            <td>
                                {% if employee.user.last_login %}
                                    {{ employee.user.last_login|format_datetime('%Y-%m-%d %H:%M:%S') }}
                                {% else %}
                                    <span class="text-muted">从未登录</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                    <div class="text-center">
                        <a href="{{ url_for('admin.edit_user', id=employee.user.id) }}" class="btn btn-sm btn-info">
                            <i class="fas fa-edit"></i> 编辑账号
                        </a>
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 该员工尚未关联系统账号
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('employee.edit_employee', id=employee.id) }}#system-account" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> 创建系统账号
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">健康证信息</h5>
                <a href="{{ url_for('employee.add_health_certificate', employee_id=employee.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> 添加健康证
                </a>
            </div>
            <div class="card-body">
                {% if health_certificates %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>证件编号</th>
                                <th>发证机构</th>
                                <th>发证日期</th>
                                <th>到期日期</th>
                                <th>状态</th>
                                <th>证件图片</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cert in health_certificates %}
                            <tr>
                                <td>{{ cert.certificate_no }}</td>
                                <td>{{ cert.issue_authority }}</td>
                                <td>{{  cert.issue_date|format_datetime('%Y-%m-%d')   }}</td>
                                <td>{{  cert.expire_date|format_datetime('%Y-%m-%d')   }}</td>
                                <td>
                                    {% if cert.expire_date < now.date() %}
                                    <span class="badge badge-danger">已过期</span>
                                    {% elif (cert.expire_date - now.date()).days <= 30 %}
                                    <span class="badge badge-warning">即将过期({{ (cert.expire_date - now.date()).days }}天)</span>
                                    {% else %}
                                    <span class="badge badge-success">有效</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if cert.certificate_img %}
                                    <a href="{{ url_for('static', filename=cert.certificate_img) }}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-image"></i> 查看
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无图片</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 该员工尚未办理健康证
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">体检记录</h5>
                <a href="{{ url_for('employee.add_medical_examination', employee_id=employee.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> 添加体检记录
                </a>
            </div>
            <div class="card-body">
                {% if medical_examinations %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>体检日期</th>
                                <th>体检医院</th>
                                <th>体检结果</th>
                                <th>体检报告</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for exam in medical_examinations %}
                            <tr>
                                <td>{{  exam.exam_date|format_datetime('%Y-%m-%d')   }}</td>
                                <td>{{ exam.exam_hospital }}</td>
                                <td>
                                    {% if exam.result == '合格' %}
                                    <span class="badge badge-success">{{ exam.result }}</span>
                                    {% else %}
                                    <span class="badge badge-danger">{{ exam.result }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if exam.report_img %}
                                    <a href="{{ url_for('static', filename=exam.report_img) }}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-file-medical"></i> 查看
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无报告</span>
                                    {% endif %}
                                </td>
                                <td>{{ exam.notes or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 该员工尚无体检记录
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="mb-0">健康检查记录</h5>
                <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> 添加健康检查
                </a>
            </div>
            <div class="card-body">
                {% if health_checks %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>检查日期</th>
                                <th>体温</th>
                                <th>健康状态</th>
                                <th>症状</th>
                                <th>检查人</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in health_checks %}
                            <tr>
                                <td>{{  check.check_date|format_datetime('%Y-%m-%d')   }}</td>
                                <td>{{ check.temperature }}°C</td>
                                <td>
                                    {% if check.health_status == '正常' %}
                                    <span class="badge badge-success">{{ check.health_status }}</span>
                                    {% else %}
                                    <span class="badge badge-danger">{{ check.health_status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ check.symptoms or '-' }}</td>
                                <td>{{ check.checker.real_name or check.checker.username }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 该员工尚无健康检查记录
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">培训记录</h5>
                <a href="{{ url_for('employee.add_training_record', employee_id=employee.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> 添加培训记录
                </a>
            </div>
            <div class="card-body">
                {% if training_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>培训名称</th>
                                <th>培训日期</th>
                                <th>有效期</th>
                                <th>培训成绩</th>
                                <th>培训师</th>
                                <th>证书</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in training_records %}
                            <tr>
                                <td>{{ record.training_name }}</td>
                                <td>{{  record.training_date|format_datetime('%Y-%m-%d')   }}</td>
                                <td>
                                    {% if record.expire_date %}
                                    {{  record.expire_date|format_datetime('%Y-%m-%d')   }}
                                    {% if record.expire_date < now.date() %}
                                    <span class="badge badge-danger">已过期</span>
                                    {% elif (record.expire_date - now.date()).days <= 30 %}
                                    <span class="badge badge-warning">即将过期</span>
                                    {% endif %}
                                    {% else %}
                                    <span class="text-muted">长期有效</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.score if record.score is not none else '-' }}</td>
                                <td>{{ record.trainer or '-' }}</td>
                                <td>
                                    {% if record.certificate_img %}
                                    <a href="{{ url_for('static', filename=record.certificate_img) }}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-certificate"></i> 查看
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无证书</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 该员工尚无培训记录
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
