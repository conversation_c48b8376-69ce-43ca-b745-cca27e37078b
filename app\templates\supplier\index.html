{% extends 'base.html' %}

{% block title %}供应商管理 - {{ super() }}{% endblock %}

{% block content %}
<!-- 现代化页面头部 -->
<div class="d-flex justify-content-between align-items-start mb-4">
  <div class="flex-grow-1">
    <h1 class="h3 mb-2 fw-bold text-dark">供应商管理</h1>
    <p class="text-muted mb-0 fs-6">管理系统中的所有供应商信息，包括基本信息、分类和合作状态</p>
  </div>

  <div class="flex-shrink-0 ms-3">
    <div class="btn-toolbar gap-2">
      <a href="{{ url_for('supplier.create') }}" class="btn btn-primary modern-btn btn-sm">
        <i class="fas fa-plus me-1"></i>添加供应商
      </a>
      <a href="{{ url_for('supplier_category.index') }}" class="btn btn-success modern-btn btn-sm">
        <i class="fas fa-tags me-1"></i>分类管理
      </a>
      <a href="{{ url_for('supplier_school.index') }}" class="btn btn-info modern-btn btn-sm">
        <i class="fas fa-school me-1"></i>学校绑定
      </a>
    </div>
  </div>
</div>

<!-- 搜索和筛选区域 -->
<div class="card modern-card mb-4">
  <div class="card-body p-3">
    <form method="GET" action="{{ url_for('supplier.index') }}" class="row g-3 align-items-end">
      <div class="col-md-4">
        <div class="form-floating">
          <input type="text" class="form-control" id="search" name="search"
                 value="{{ request.args.get('search', '') }}" placeholder="搜索供应商...">
          <label for="search">搜索供应商</label>
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-floating">
          <select class="form-select" id="category_id" name="category_id">
            <option value="">全部分类</option>
            {% for category in categories %}
            <option value="{{ category.id }}" {% if request.args.get('category_id') == category.id|string %}selected{% endif %}>
              {{ category.name }}
            </option>
            {% endfor %}
          </select>
          <label for="category_id">供应商分类</label>
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-floating">
          <select class="form-select" id="status" name="status">
            <option value="">全部状态</option>
            <option value="1" {% if request.args.get('status') == '1' %}selected{% endif %}>合作中</option>
            <option value="0" {% if request.args.get('status') == '0' %}selected{% endif %}>已停用</option>
          </select>
          <label for="status">合作状态</label>
        </div>
      </div>
      <div class="col-md-2">
        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary modern-btn">
            <i class="fas fa-search me-1"></i>搜索
          </button>
          <a href="{{ url_for('supplier.index') }}" class="btn btn-outline-secondary modern-btn">
            <i class="fas fa-times me-1"></i>清除
          </a>
        </div>
      </div>
    </form>
  </div>
</div>
<!-- 简洁统计卡片 -->
{% if supplier_stats %}
<div class="row g-3 mb-4">
  <div class="col-lg-3 col-md-6">
    <div class="card modern-card h-100">
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h3 class="mb-1 text-primary">{{ supplier_stats.total }}</h3>
            <div class="text-muted">总供应商</div>
          </div>
          <div class="text-primary">
            <i class="fas fa-building fs-4"></i>
          </div>
        </div>
        <div class="mt-2">
          <small class="text-muted">系统中的所有供应商</small>
        </div>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="card modern-card h-100">
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h3 class="mb-1 text-success">{{ supplier_stats.active }}</h3>
            <div class="text-muted">合作中</div>
          </div>
          <div class="text-success">
            <i class="fas fa-handshake fs-4"></i>
          </div>
        </div>
        <div class="mt-2">
          <small class="text-muted">正在合作的供应商</small>
        </div>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="card modern-card h-100">
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h3 class="mb-1 text-secondary">{{ supplier_stats.inactive }}</h3>
            <div class="text-muted">已停用</div>
          </div>
          <div class="text-secondary">
            <i class="fas fa-pause-circle fs-4"></i>
          </div>
        </div>
        <div class="mt-2">
          <small class="text-muted">暂停合作的供应商</small>
        </div>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="card modern-card h-100">
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h3 class="mb-1 text-warning">{{ supplier_stats.avg_rating|round(1) if supplier_stats.avg_rating else '暂无' }}</h3>
            <div class="text-muted">平均评级</div>
          </div>
          <div class="text-warning">
            <i class="fas fa-star fs-4"></i>
          </div>
        </div>
        <div class="mt-2">
          <small class="text-muted">供应商服务质量</small>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %}

<!-- 简洁供应商列表 -->
<div class="card modern-card">
  <div class="card-header bg-light border-bottom">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h6 class="mb-1 fw-semibold">供应商列表</h6>
        <small class="text-muted">共 {{ suppliers|length }} 个供应商</small>
      </div>
      <div class="d-flex gap-2">
        <button class="btn btn-sm btn-outline-secondary" onclick="exportSuppliers()">
          <i class="fas fa-download me-1"></i>导出
        </button>
        <button class="btn btn-sm btn-outline-secondary" onclick="refreshList()">
          <i class="fas fa-sync-alt me-1"></i>刷新
        </button>
      </div>
    </div>
  </div>

  <!-- 桌面端表格 -->
  <div class="d-none d-lg-block">
    <div class="table-responsive">
      <table class="table table-hover mb-0">
        <thead class="table-light">
          <tr>
            <th class="border-0 fw-semibold text-center" style="width: 8%;">ID</th>
            <th class="border-0 fw-semibold" style="width: 20%;">供应商名称</th>
            <th class="border-0 fw-semibold" style="width: 12%;">分类</th>
            <th class="border-0 fw-semibold" style="width: 12%;">联系人</th>
            <th class="border-0 fw-semibold" style="width: 12%;">联系电话</th>
            <th class="border-0 fw-semibold" style="width: 15%;">合作学校</th>
            <th class="border-0 fw-semibold text-center" style="width: 8%;">评级</th>
            <th class="border-0 fw-semibold text-center" style="width: 8%;">状态</th>
            <th class="border-0 fw-semibold text-center" style="width: 15%;">操作</th>
          </tr>
        </thead>
        <tbody>
          {% for supplier in suppliers %}
          <tr class="border-0">
            <td class="py-3 border-0 text-center">
              <span class="fw-medium text-primary">#{{ supplier.id }}</span>
            </td>
            <td class="py-3 border-0">
              <div class="d-flex align-items-center">
                <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-2">
                  <i class="fas fa-building text-primary"></i>
                </div>
                <div>
                  <div class="fw-medium">{{ supplier.name }}</div>
                  <small class="text-muted">供应商</small>
                </div>
              </div>
            </td>
            <td class="py-3 border-0">
              <span class="badge bg-info bg-opacity-10 text-info border border-info">
                {{ supplier.category.name if supplier.category else '未分类' }}
              </span>
            </td>
            <td class="py-3 border-0">{{ supplier.contact_person or '未设置' }}</td>
            <td class="py-3 border-0">{{ supplier.phone or '未设置' }}</td>
            <td class="py-3 border-0">
              {% if supplier.accessible_relations %}
                {% for relation in supplier.accessible_relations %}
                  {% if relation.status == 1 %}
                  <span class="badge bg-success bg-opacity-10 text-success border border-success me-1 mb-1">
                    {{ relation.area.name }}
                  </span>
                  {% endif %}
                {% endfor %}
              {% else %}
              <span class="text-muted">暂无合作学校</span>
              {% endif %}
            </td>
            <td class="py-3 border-0 text-center">
              {% if supplier.rating %}
                <div class="text-warning">
                  {% for i in range(supplier.rating|int) %}
                  <i class="fas fa-star"></i>
                  {% endfor %}
                  {% if supplier.rating % 1 > 0 %}
                  <i class="fas fa-star-half-alt"></i>
                  {% endif %}
                </div>
              {% else %}
              <span class="text-muted">暂无评级</span>
              {% endif %}
            </td>
            <td class="py-3 border-0 text-center">
              {% if supplier.status == 1 %}
              <span class="badge bg-success bg-opacity-10 text-success border border-success">合作中</span>
              {% else %}
              <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary">已停用</span>
              {% endif %}
            </td>
            <td class="py-3 border-0 text-center">
              <div class="btn-group btn-group-sm">
                <a href="{{ url_for('supplier.view', id=supplier.id) }}"
                   class="btn btn-outline-info" title="查看详情">
                  <i class="fas fa-eye"></i>
                </a>
                <a href="{{ url_for('supplier.edit', id=supplier.id) }}"
                   class="btn btn-outline-primary" title="编辑供应商">
                  <i class="fas fa-edit"></i>
                </a>
                <button type="button" class="btn btn-outline-danger delete-btn"
                        data-id="{{ supplier.id }}" title="删除供应商">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
          {% else %}
          <tr>
            <td colspan="9" class="text-center py-5 border-0">
              <div class="text-muted">
                <i class="fas fa-building fs-1 opacity-25 mb-3"></i>
                <div>暂无供应商数据</div>
                <small>您可以添加新的供应商或调整筛选条件</small>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- 移动端卡片 -->
  <div class="d-lg-none">
    <div class="card-body p-3">
      {% for supplier in suppliers %}
      <div class="card mb-3 border-0 shadow-sm modern-card-sm">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="flex-grow-1">
              <h6 class="mb-1 fw-semibold text-primary">{{ supplier.name }}</h6>
              <div class="text-muted small">
                ID: {{ supplier.id }} | {{ supplier.category.name if supplier.category else '未分类' }}
              </div>
            </div>
            <div class="text-end">
              {% if supplier.status == 1 %}
              <span class="badge bg-success bg-opacity-10 text-success border border-success">合作中</span>
              {% else %}
              <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary">已停用</span>
              {% endif %}
            </div>
          </div>

          <div class="row g-2 mb-2">
            <div class="col-6">
              <div class="bg-light rounded-3 p-2">
                <small class="text-muted d-block">联系人</small>
                <div class="fw-medium">{{ supplier.contact_person or '未设置' }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light rounded-3 p-2">
                <small class="text-muted d-block">联系电话</small>
                <div class="fw-medium">{{ supplier.phone or '未设置' }}</div>
              </div>
            </div>
          </div>

          {% if supplier.rating %}
          <div class="mb-2">
            <small class="text-muted d-block">评级</small>
            <div class="text-warning">
              {% for i in range(supplier.rating|int) %}
              <i class="fas fa-star"></i>
              {% endfor %}
              {% if supplier.rating % 1 > 0 %}
              <i class="fas fa-star-half-alt"></i>
              {% endif %}
            </div>
          </div>
          {% endif %}

          {% if supplier.accessible_relations %}
          <div class="mb-3">
            <small class="text-muted d-block mb-1">合作学校</small>
            <div>
              {% for relation in supplier.accessible_relations %}
                {% if relation.status == 1 %}
                <span class="badge bg-success bg-opacity-10 text-success border border-success me-1 mb-1">
                  {{ relation.area.name }}
                </span>
                {% endif %}
              {% endfor %}
            </div>
          </div>
          {% endif %}

          <div class="d-grid gap-2 d-md-flex">
            <a href="{{ url_for('supplier.view', id=supplier.id) }}"
               class="btn btn-outline-info btn-sm flex-fill">
              <i class="fas fa-eye me-1"></i>查看
            </a>
            <a href="{{ url_for('supplier.edit', id=supplier.id) }}"
               class="btn btn-outline-primary btn-sm flex-fill">
              <i class="fas fa-edit me-1"></i>编辑
            </a>
            <button type="button" class="btn btn-outline-danger btn-sm flex-fill delete-btn"
                    data-id="{{ supplier.id }}">
              <i class="fas fa-trash me-1"></i>删除
            </button>
          </div>
        </div>
      </div>
      {% else %}
      <div class="text-center py-5">
        <i class="fas fa-building fs-1 text-muted opacity-25 mb-3"></i>
        <div class="text-muted">暂无供应商数据</div>
        <small class="text-muted">您可以添加新的供应商或调整筛选条件</small>
      </div>
      {% endfor %}
    </div>
  </div>
</div>

<!-- 现代化分页 -->
{% if pagination and pagination.pages > 1 %}
<div class="d-flex justify-content-center mt-4">
  <nav aria-label="Page navigation">
    <ul class="pagination modern-pagination">
      {% if pagination.has_prev %}
      <li class="page-item">
        <a class="page-link" href="{{ url_for('supplier.index', page=pagination.prev_num, search=request.args.get('search'), category_id=request.args.get('category_id'), status=request.args.get('status')) }}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <span class="page-link"><i class="fas fa-chevron-left"></i></span>
      </li>
      {% endif %}

      {% for page in pagination.iter_pages() %}
        {% if page %}
          {% if page != pagination.page %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('supplier.index', page=page, search=request.args.get('search'), category_id=request.args.get('category_id'), status=request.args.get('status')) }}">{{ page }}</a>
          </li>
          {% else %}
          <li class="page-item active">
            <span class="page-link">{{ page }}</span>
          </li>
          {% endif %}
        {% else %}
        <li class="page-item disabled">
          <span class="page-link">...</span>
        </li>
        {% endif %}
      {% endfor %}

      {% if pagination.has_next %}
      <li class="page-item">
        <a class="page-link" href="{{ url_for('supplier.index', page=pagination.next_num, search=request.args.get('search'), category_id=request.args.get('category_id'), status=request.args.get('status')) }}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <span class="page-link"><i class="fas fa-chevron-right"></i></span>
      </li>
      {% endif %}
    </ul>
  </nav>
</div>
{% endif %}

<!-- 现代化删除确认模态框 -->
<div class="modal fade modern-modal" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <div class="d-flex align-items-center">
          <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
            <i class="fas fa-exclamation-triangle text-danger"></i>
          </div>
          <div>
            <h5 class="modal-title mb-0" id="deleteModalLabel">确认删除供应商</h5>
            <small class="text-muted">此操作不可恢复</small>
          </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="alert modern-alert alert-warning">
          <i class="fas fa-info-circle me-2"></i>
          删除供应商将会影响以下数据：
          <ul class="mb-0 mt-2">
            <li>相关的采购订单记录</li>
            <li>库存入库记录</li>
            <li>供应商评价信息</li>
          </ul>
        </div>
        <p class="mb-0">确定要删除这个供应商吗？</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary modern-btn" data-bs-dismiss="modal">
          <i class="fas fa-times me-1"></i>取消
        </button>
        <button type="button" class="btn btn-danger modern-btn" id="confirmDelete">
          <i class="fas fa-trash me-1"></i>确认删除
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    let deleteId = null;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

    // 删除按钮事件处理
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            deleteId = this.dataset.id;
            deleteModal.show();
        });
    });

    // 确认删除
    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (deleteId) {
            const deleteBtn = this;
            deleteBtn.disabled = true;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';

            fetch(`{{ url_for("supplier.delete", id=0) }}`.replace('0', deleteId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    toastr.success(data.message || '删除成功');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    toastr.error(data.message || '删除失败');
                }
                deleteModal.hide();
            })
            .catch(error => {
                console.error('删除失败:', error);
                toastr.error('删除失败，请稍后重试！');
                deleteModal.hide();
            })
            .finally(() => {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '<i class="fas fa-trash me-1"></i>确认删除';
            });
        }
    });

    // 导出功能
    window.exportSuppliers = function() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'excel');
        window.open(`{{ url_for('supplier.index') }}?${params.toString()}`, '_blank');
    };

    // 刷新列表
    window.refreshList = function() {
        window.location.reload();
    };

    // 搜索表单增强
    const searchForm = document.querySelector('form');
    if (searchForm) {
        const searchInput = searchForm.querySelector('input[name="search"]');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        searchForm.submit();
                    }
                }, 500);
            });
        }
    }

    // 添加页面加载动画
    document.querySelectorAll('.modern-card').forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
});
</script>
{% endblock %}
