{% extends 'base.html' %}
{% from 'components/page_header.html' import filter_page_header %}
{% from 'components/cards.html' import table_card, data_card, compact_card_grid %}

{% block title %}供应商管理 - {{ super() }}{% endblock %}

{% block content %}
<!-- 页面头部和筛选 -->
{% set breadcrumbs = [{'title': '首页', 'url': url_for('main.index')}, {'title': '供应商管理'}] %}
{% if current_area %}
  {% for area in area_path %}
    {% set breadcrumbs = breadcrumbs + [{'title': area.get_level_name() + ' - ' + area.name}] %}
  {% endfor %}
{% endif %}

{% set filter_options = [
  {
    'name': 'category_id',
    'label': '选择分类',
    'options': [{'value': category.id, 'text': category.name} for category in categories]
  }
] %}

{% set actions = [
  {'url': url_for('supplier.create'), 'text': '添加供应商', 'icon': 'fas fa-plus', 'class': 'btn-primary'},
  {'url': url_for('supplier_category.index'), 'text': '分类管理', 'icon': 'fas fa-tags', 'class': 'btn-info'},
  {'url': url_for('supplier_school.index'), 'text': '学校绑定', 'icon': 'fas fa-school', 'class': 'btn-success'}
] %}

<form method="GET" action="{{ url_for('supplier.index') }}">
{{ filter_page_header(
  title="供应商管理",
  search_form=True,
  filter_options=filter_options,
  actions=actions
) }}
</form>
<!-- 统计卡片 -->
{% if supplier_stats %}
{% set stats_cards = [
  {
    'type': 'data',
    'title': '总供应商',
    'value': supplier_stats.total,
    'icon': 'fas fa-building',
    'color': 'primary'
  },
  {
    'type': 'data',
    'title': '合作中',
    'value': supplier_stats.active,
    'icon': 'fas fa-handshake',
    'color': 'success'
  },
  {
    'type': 'data',
    'title': '已停用',
    'value': supplier_stats.inactive,
    'icon': 'fas fa-pause-circle',
    'color': 'secondary'
  },
  {
    'type': 'data',
    'title': '平均评级',
    'value': supplier_stats.avg_rating|round(1) if supplier_stats.avg_rating else '暂无',
    'icon': 'fas fa-star',
    'color': 'warning'
  }
] %}

<div class="mb-4">
  {{ compact_card_grid(stats_cards, "lg-3 md-6") }}
</div>
{% endif %}

<!-- 供应商列表表格 -->
{% set headers = [
  {'text': 'ID', 'class': 'text-center'},
  {'text': '供应商名称', 'class': ''},
  {'text': '分类', 'class': ''},
  {'text': '联系人', 'class': ''},
  {'text': '联系电话', 'class': ''},
  {'text': '合作学校', 'class': ''},
  {'text': '评级', 'class': 'text-center'},
  {'text': '状态', 'class': 'text-center'},
  {'text': '操作', 'class': 'text-center'}
] %}

{% set rows = [] %}
{% for supplier in suppliers %}
  {% set status_color = 'success' if supplier.status == 1 else 'secondary' %}
  {% set status_text = '合作中' if supplier.status == 1 else '已停用' %}

  <!-- 合作学校处理 -->
  {% set cooperation_schools = [] %}
  {% if supplier.accessible_relations %}
    {% for relation in supplier.accessible_relations %}
      {% if relation.status == 1 %}
        {% set cooperation_schools = cooperation_schools + [relation.area.name] %}
      {% endif %}
    {% endfor %}
  {% endif %}
  {% set schools_text = cooperation_schools|join(', ') if cooperation_schools else '暂无合作学校' %}

  <!-- 评级处理 -->
  {% set rating_html = '' %}
  {% if supplier.rating %}
    {% for i in range(supplier.rating|int) %}
      {% set rating_html = rating_html + '<i class="fas fa-star text-warning"></i>' %}
    {% endfor %}
    {% if supplier.rating % 1 > 0 %}
      {% set rating_html = rating_html + '<i class="fas fa-star-half-alt text-warning"></i>' %}
    {% endif %}
  {% else %}
    {% set rating_html = '<span class="text-muted">暂无评级</span>' %}
  {% endif %}

  {% set row = [
    {'text': supplier.id, 'type': 'text', 'class': 'text-center'},
    {'text': supplier.name, 'type': 'text'},
    {'text': supplier.category.name if supplier.category else '未分类', 'type': 'text'},
    {'text': supplier.contact_person or '未设置', 'type': 'text'},
    {'text': supplier.phone or '未设置', 'type': 'text'},
    {'text': schools_text, 'type': 'text'},
    {'text': rating_html, 'type': 'html', 'class': 'text-center'},
    {'text': status_text, 'type': 'badge', 'color': status_color, 'class': 'text-center'},
    {
      'type': 'actions',
      'actions': [
        {'url': url_for('supplier.view', id=supplier.id), 'icon': 'fas fa-eye', 'title': '查看详情'},
        {'url': url_for('supplier.edit', id=supplier.id), 'icon': 'fas fa-edit', 'title': '编辑供应商'},
        {'onclick': 'deleteSupplier(' + supplier.id|string + ')', 'icon': 'fas fa-trash', 'title': '删除供应商', 'class': 'btn-outline-danger'}
      ],
      'class': 'text-center'
    }
  ] %}
  {% set rows = rows + [row] %}
{% endfor %}

<div class="d-none d-lg-block">
{{ table_card(
  title="供应商列表 (" + suppliers|length|string + "个)",
  headers=headers,
  rows=rows,
  pagination=pagination_html() if pagination and pagination.pages > 1 else None
) }}
</div>

                    <!-- 移动端供应商卡片 -->
                    <div class="mobile-only">
                        {% for supplier in suppliers %}
                        <div class="card mb-3 border-start-{% if supplier.status == 1 %}success{% else %}secondary{% endif %}">
                            <div class="card-body py-2">
                                <div class="row">
                                    <div class="col-8">
                                        <h6 class="mb-1">{{ supplier.name }}</h6>
                                        <small class="text-muted">ID: {{ supplier.id }} | {{ supplier.category.name if supplier.category else '未分类' }}</small>
                                    </div>
                                    <div class="col-4 text-end">
                                        {% if supplier.status == 1 %}
                                        <span class="badge bg-success">合作中</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已停用</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">联系人</small>
                                        <div class="small">{{ supplier.contact_person or '-' }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">联系电话</small>
                                        <div class="small">{{ supplier.phone or '-' }}</div>
                                    </div>
                                </div>

                                {% if supplier.rating %}
                                <div class="row mt-1">
                                    <div class="col-12">
                                        <small class="text-muted">评级</small>
                                        <div class="text-warning">
                                            {% for i in range(supplier.rating|int) %}
                                            <i class="fas fa-star"></i>
                                            {% endfor %}
                                            {% if supplier.rating % 1 > 0 %}
                                            <i class="fas fa-star-half-alt"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                {% if supplier.accessible_relations %}
                                <div class="row mt-1">
                                    <div class="col-12">
                                        <small class="text-muted">合作学校</small>
                                        <div>
                                            {% for relation in supplier.accessible_relations %}
                                                {% if relation.status == 1 %}
                                                <span class="badge bg-success mb-1">{{ relation.area.name }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="btn-group btn-group-sm w-100" role="group">
                                            <a href="{{ url_for('supplier.view', id=supplier.id) }}" class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('supplier.edit', id=supplier.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-btn" data-id="{{ supplier.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5>暂无供应商数据</h5>
                            <p class="text-muted">您可以添加新的供应商或调整筛选条件</p>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier.index', page=pagination.prev_num, category_id=category_id, keyword=keyword) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('supplier.index', page=page, category_id=category_id, keyword=keyword) }}">{{ page }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier.index', page=pagination.next_num, category_id=category_id, keyword=keyword) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个供应商吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
