{% extends 'base.html' %}

{% block title %}待生成凭证的入库单 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
<style nonce="{{ csp_nonce }}">
/* 用友风格待入库单页面样式 */
.pending-stock-container, .pending-stock-container * {
    font-size: 13px !important;
    font-family: var(--yonyou-font-family);
}

.pending-stock-container {
    background: var(--yonyou-color-background);
    min-height: 100vh;
    padding: 10px;
}

.pending-stock-window {
    background: white;
    border: 1px solid var(--yonyou-color-border);
    border-radius: var(--yonyou-border-radius);
    box-shadow: var(--yonyou-box-shadow);
    margin: 0 auto;
    max-width: 1400px;
}

.pending-stock-header {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border-bottom: 1px solid #90caf9;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: bold;
    color: #1565c0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px 4px 0 0;
}

.pending-stock-toolbar {
    background: #f8f8f8;
    border-bottom: 1px solid var(--yonyou-color-border);
    padding: 8px 15px;
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
}

.toolbar-left {
    display: flex;
    gap: 8px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 8px;
    align-items: center;
}

.uf-btn {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid var(--yonyou-color-border);
    border-radius: 3px;
    padding: 4px 12px;
    font-size: 13px;
    color: var(--yonyou-color-text);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
    text-decoration: none;
}

.uf-btn:hover {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    color: #1565c0;
    text-decoration: none;
}

.uf-btn-primary {
    background: linear-gradient(to bottom, #1890ff, #1565c0);
    color: white;
    border-color: #1565c0;
}

.uf-btn-primary:hover {
    background: linear-gradient(to bottom, #40a9ff, #1890ff);
    color: white;
}

.uf-btn-success {
    background: linear-gradient(to bottom, #52c41a, #389e0d);
    color: white;
    border-color: #389e0d;
}

.uf-btn-success:hover {
    background: linear-gradient(to bottom, #73d13d, #52c41a);
    color: white;
}

.uf-btn-info {
    background: linear-gradient(to bottom, #1890ff, #1565c0);
    color: white;
    border-color: #1565c0;
}

.uf-btn-info:hover {
    background: linear-gradient(to bottom, #40a9ff, #1890ff);
    color: white;
}

.pending-stock-content {
    padding: 15px;
    background: white;
}

.uf-alert {
    padding: 12px 15px;
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: var(--yonyou-border-radius);
    font-size: 13px;
}

.uf-alert-info {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.uf-alert-warning {
    background: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
}

.uf-table-container {
    border: 1px solid var(--yonyou-color-border);
    border-radius: var(--yonyou-border-radius);
    overflow: hidden;
    margin-bottom: 15px;
}

.uf-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    margin: 0;
    background: white;
}

.uf-table th {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border: 1px solid #90caf9;
    padding: 8px 6px;
    text-align: center;
    font-weight: normal;
    color: #1565c0;
    font-size: 13px;
    white-space: nowrap;
}

.uf-table td {
    border: 1px solid var(--yonyou-color-border);
    padding: 6px;
    vertical-align: middle;
    background: white;
    font-size: 13px;
}

.uf-table tbody tr:hover {
    background: #f5f5f5;
}

.uf-table tbody tr.selected {
    background: #e3f2fd;
}

/* 用友风格图标增强 */
.uf-icon-enhanced {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    vertical-align: middle;
}

.uf-icon-document::before {
    content: "📋";
    font-size: 14px;
}

.uf-icon-refresh::before {
    content: "🔄";
    font-size: 12px;
}

.uf-icon-view::before {
    content: "👁";
    font-size: 12px;
}

.uf-icon-generate::before {
    content: "➕";
    font-size: 12px;
    color: #52c41a;
}

.uf-icon-batch::before {
    content: "📦";
    font-size: 12px;
}

.uf-icon-empty::before {
    content: "📂";
    font-size: 32px;
}

/* 用友风格字体 */
.uf-font {
    font-family: "Microsoft YaHei", "SimSun", "宋体", sans-serif;
    font-size: 12px;
}
</style>
{% endblock %}

{% block content %}
<div class="pending-stock-container">
    <div class="pending-stock-window">
        <!-- 窗口标题栏 -->
        <div class="pending-stock-header">
            <div style="display: flex; align-items: center;">
                待生成凭证的入库单
            </div>
            <div style="display: flex; gap: 4px;">
                <button class="uf-btn" onclick="window.location.reload();">
                    刷新
                </button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="pending-stock-toolbar">
            <div class="toolbar-left">
                <span style="color: #666; font-size: 13px;">
                    <i class="fas fa-info-circle"></i>
                    以下入库单已完成入库（已审核、已入库、已完成状态），可以生成财务凭证
                </span>
            </div>
            <div class="toolbar-right">
                <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn">
                    返回凭证管理
                </a>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="pending-stock-content">
            {% if stock_ins %}
            <div class="uf-alert uf-alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>操作说明：</strong>点击"生成凭证"为单个入库单生成凭证，或使用批量生成功能。生成的凭证将自动审核通过。
            </div>

            <div class="uf-table-container">
                <table class="uf-table">
                    <thead>
                        <tr>
                            <th width="4%">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" style="cursor: pointer;">
                            </th>
                            <th width="18%">入库单号</th>
                            <th width="10%">入库日期</th>
                            <th width="15%">供应商</th>
                            <th width="18%">采购订单</th>
                            <th width="12%">总金额</th>
                            <th width="10%">状态</th>
                            <th width="13%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stock_in in stock_ins %}
                        <tr>
                            <td style="text-align: center;">
                                <input type="checkbox" class="stock-in-checkbox" value="{{ stock_in.id }}" style="cursor: pointer;">
                            </td>
                            <td style="word-break: break-all; white-space: normal;">
                                <span style="font-weight: 500; color: #1890ff;">{{ stock_in.stock_in_number }}</span>
                            </td>
                            <td style="text-align: center; white-space: nowrap;">
                                {{ stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in.stock_in_date else '-' }}
                            </td>
                            <td style="max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                                title="{{ stock_in.supplier.name if stock_in.supplier else '-' }}">
                                {{ stock_in.supplier.name if stock_in.supplier else '-' }}
                            </td>
                            <td style="word-break: break-all; white-space: normal;">
                                {% if stock_in.purchase_order %}
                                    <a href="{{ url_for('purchase_order.view', id=stock_in.purchase_order.id) }}"
                                       target="_blank" style="color: #1890ff; text-decoration: none;">
                                        {{ stock_in.purchase_order.order_number }}
                                    </a>
                                {% else %}
                                    <span style="color: #999;">-</span>
                                {% endif %}
                            </td>
                            <td style="text-align: right; font-weight: 500;">
                                <span style="color: #52c41a;">¥{{ "%.2f"|format(stock_in.total_cost) }}</span>
                            </td>
                            <td style="text-align: center;">
                                <span style="background: #f6ffed; color: #52c41a; padding: 2px 8px; border-radius: 3px; font-size: 12px; border: 1px solid #b7eb8f;">
                                    已财务确认
                                </span>
                            </td>
                            <td style="white-space: nowrap; text-align: center;">
                                <a href="{{ url_for('stock_in.view', id=stock_in.id) }}"
                                   class="uf-btn uf-btn-info" title="查看入库单" target="_blank" style="margin-right: 4px;">
                                    查看
                                </a>
                                <button type="button" class="uf-btn uf-btn-success"
                                        data-action="generate-voucher"
                                        data-stock-in-id="{{ stock_in.id }}"
                                        data-stock-in-number="{{ stock_in.stock_in_number }}"
                                        title="生成凭证">
                                    生成凭证
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 批量操作面板 -->
            <div style="background: #fafafa; border: 1px solid var(--yonyou-color-border); border-radius: var(--yonyou-border-radius); padding: 15px; margin-top: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h6 style="margin: 0; color: #666; font-size: 13px; font-weight: bold;">批量操作</h6>
                    <span style="font-size: 12px; color: #999;">
                        <i class="fas fa-info-circle"></i>
                        批量生成的凭证将自动审核通过，请确保会计科目设置正确
                    </span>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button type="button" class="uf-btn uf-btn-primary" onclick="batchGenerateSelected()">
                        为选中项生成凭证
                    </button>
                    <button type="button" class="uf-btn uf-btn-success" onclick="batchGenerateAll()">
                        为全部生成凭证
                    </button>
                </div>
            </div>

            {% else %}
            <!-- 无数据状态 -->
            <div class="uf-alert uf-alert-warning" style="text-align: center; padding: 40px;">
                <div style="font-size: 48px; color: #faad14; margin-bottom: 15px;">
                    <i class="fas fa-folder-open"></i>
                </div>
                <h5 style="color: #fa8c16; margin-bottom: 10px; font-size: 16px;">暂无待生成凭证的入库单</h5>
                <p style="color: #999; margin-bottom: 20px; font-size: 13px;">
                    所有已完成入库的入库单都已生成财务凭证。
                </p>
                <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn uf-btn-primary">
                    返回凭证管理
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 绑定生成凭证按钮事件
    document.querySelectorAll('[data-action="generate-voucher"]').forEach(btn => {
        btn.addEventListener('click', function() {
            const stockInId = this.getAttribute('data-stock-in-id');
            const stockInNumber = this.getAttribute('data-stock-in-number');
            generateSingleVoucher(stockInId, stockInNumber, this);
        });
    });
});

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.stock-in-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 生成单个凭证
function generateSingleVoucher(stockInId, stockInNumber, btn) {
    // 用友风格确认对话框
    if (!confirm(`确定要为入库单 ${stockInNumber} 生成财务凭证吗？\n\n生成的凭证将自动审核通过。`)) {
        return;
    }

    btn = btn || event.target.closest('button');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    btn.style.opacity = '0.6';

    fetch('{{ url_for("financial.generate_voucher_from_stock_in") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            stock_in_id: stockInId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 用友风格成功提示
            alert(`✓ 凭证生成成功！\n\n凭证号：${data.voucher_number}\n状态：已审核`);
            // 移除该行
            btn.closest('tr').remove();

            // 检查是否还有数据
            const tbody = document.querySelector('tbody');
            if (tbody.children.length === 0) {
                location.reload();
            }
        } else {
            alert('✗ 生成失败：' + data.message);
            btn.innerHTML = originalText;
            btn.disabled = false;
            btn.style.opacity = '1';
        }
    })
    .catch(error => {
        alert('✗ 网络错误：' + error.message);
        btn.innerHTML = originalText;
        btn.disabled = false;
        btn.style.opacity = '1';
    });
}

// 批量生成选中项
function batchGenerateSelected() {
    const selectedCheckboxes = document.querySelectorAll('.stock-in-checkbox:checked');

    if (selectedCheckboxes.length === 0) {
        alert('⚠ 请先选择要生成凭证的入库单');
        return;
    }

    const stockInIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

    if (!confirm(`确定要为选中的 ${stockInIds.length} 个入库单生成财务凭证吗？\n\n批量生成的凭证将自动审核通过。`)) {
        return;
    }

    batchGenerateVouchers(stockInIds);
}

// 批量生成全部
function batchGenerateAll() {
    const allCheckboxes = document.querySelectorAll('.stock-in-checkbox');

    if (allCheckboxes.length === 0) {
        alert('⚠ 没有可生成凭证的入库单');
        return;
    }

    if (!confirm(`确定要为全部 ${allCheckboxes.length} 个入库单生成财务凭证吗？\n\n批量生成的凭证将自动审核通过。`)) {
        return;
    }

    const stockInIds = Array.from(allCheckboxes).map(cb => parseInt(cb.value));
    batchGenerateVouchers(stockInIds);
}

// 批量生成凭证
function batchGenerateVouchers(stockInIds) {
    // 创建用友风格进度提示
    const progressDiv = document.createElement('div');
    progressDiv.className = 'uf-alert uf-alert-info';
    progressDiv.style.marginBottom = '15px';
    progressDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-spinner fa-spin" style="color: #1890ff;"></i>
            <span>正在批量生成凭证，请稍候...（${stockInIds.length} 个入库单）</span>
        </div>
    `;
    document.querySelector('.pending-stock-content').insertBefore(progressDiv, document.querySelector('.uf-table-container'));

    // 禁用所有按钮
    document.querySelectorAll('button, .uf-btn').forEach(btn => {
        btn.disabled = true;
        btn.style.opacity = '0.6';
    });

    // 逐个生成凭证
    let successCount = 0;
    let failedCount = 0;
    let processedCount = 0;

    const processNext = () => {
        if (processedCount >= stockInIds.length) {
            // 全部处理完成
            progressDiv.className = successCount > 0 ? 'uf-alert uf-alert-info' : 'uf-alert uf-alert-warning';
            progressDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-check-circle" style="color: #52c41a;"></i>
                    <span>✓ 批量生成完成！成功：${successCount} 个，失败：${failedCount} 个</span>
                </div>
            `;

            setTimeout(() => {
                location.reload();
            }, 2000);
            return;
        }

        const stockInId = stockInIds[processedCount];

        fetch('{{ url_for("financial.generate_voucher_from_stock_in") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                stock_in_id: stockInId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                successCount++;
            } else {
                failedCount++;
            }
            processedCount++;

            // 更新进度
            const percentage = Math.round((processedCount / stockInIds.length) * 100);
            progressDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-spinner fa-spin" style="color: #1890ff;"></i>
                    <span>正在批量生成凭证...（${processedCount}/${stockInIds.length}）- ${percentage}%</span>
                </div>
            `;

            // 处理下一个
            setTimeout(processNext, 300);
        })
        .catch(error => {
            failedCount++;
            processedCount++;

            // 处理下一个
            setTimeout(processNext, 300);
        });
    };

    // 开始处理
    processNext();
}
</script>
{% endblock %}
