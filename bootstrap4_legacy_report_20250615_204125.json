{"scan_time": "2025-06-15T20:41:25.550903", "files_scanned": 425, "total_issues": 249, "issues": {"bootstrap4_classes": [{"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 130, "file": "app\\static\\css\\color-contrast-fix.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 148, "file": "app\\static\\css\\color-contrast-fix.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 77, "file": "app\\static\\css\\custom-csp-fixes.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 216, "file": "app\\static\\css\\dashboard-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 250, "file": "app\\static\\css\\dashboard-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 533, "file": "app\\static\\css\\dashboard-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 224, "file": "app\\static\\css\\elegant-navigation.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 144, "file": "app\\static\\css\\enhanced-image-uploader.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 151, "file": "app\\static\\css\\enhanced-image-uploader.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 90, "file": "app\\static\\css\\home.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 295, "file": "app\\static\\css\\homepage.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 337, "file": "app\\static\\css\\homepage.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 530, "file": "app\\static\\css\\homepage.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 553, "file": "app\\static\\css\\homepage.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 593, "file": "app\\static\\css\\homepage.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 853, "file": "app\\static\\css\\homepage.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1090, "file": "app\\static\\css\\homepage.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 200, "file": "app\\static\\css\\inventory-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 224, "file": "app\\static\\css\\inventory-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 4, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 11, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 18, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 54, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 104, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 256, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 301, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 400, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 517, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 638, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 698, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 763, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 798, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 872, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 957, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1008, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1048, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1070, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1102, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1139, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1167, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1209, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1287, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1419, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1461, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1481, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1499, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1535, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1576, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1614, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1643, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1672, "file": "app\\static\\css\\mobile-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 2, "file": "app\\static\\css\\print-styles.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 217, "file": "app\\static\\css\\process_navigation.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 259, "file": "app\\static\\css\\process_navigation.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 301, "file": "app\\static\\css\\process_navigation.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 343, "file": "app\\static\\css\\process_navigation.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 51, "file": "app\\static\\css\\progress-steps.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 78, "file": "app\\static\\css\\progress-steps.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 220, "file": "app\\static\\css\\progress-steps.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 238, "file": "app\\static\\css\\progress-steps.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 237, "file": "app\\static\\css\\sidebar-layout.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 347, "file": "app\\static\\css\\sidebar-layout.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 361, "file": "app\\static\\css\\sidebar-layout.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 212, "file": "app\\static\\css\\style.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 56, "file": "app\\static\\css\\table-headers-global.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 226, "file": "app\\static\\css\\table-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 261, "file": "app\\static\\css\\table-optimization.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 813, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 819, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 826, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 848, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 994, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1051, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1301, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1504, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 1548, "file": "app\\static\\css\\theme-colors.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 90, "file": "app\\static\\css\\weekly_menu_modal.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 109, "file": "app\\static\\css\\weekly_menu_modal.css"}, {"type": "bootstrap4_class", "pattern": "\\bmedia\\b", "match": "media", "line": 116, "file": "app\\static\\css\\weekly_menu_modal.css"}], "bootstrap4_attributes": [], "bootstrap4_structures": [], "bootstrap4_js": [{"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 248, "file": "app\\templates\\admin\\carousel_list.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 456, "file": "app\\templates\\admin\\data_management.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 460, "file": "app\\templates\\admin\\data_management.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 436, "file": "app\\templates\\admin\\users.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 441, "file": "app\\templates\\admin\\users.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 498, "file": "app\\templates\\admin\\users.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 263, "file": "app\\templates\\admin\\guide_management\\demo_data.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 241, "file": "app\\templates\\admin\\guide_management\\scenarios.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 258, "file": "app\\templates\\admin\\guide_management\\scenarios.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 288, "file": "app\\templates\\admin\\guide_management\\scenarios.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 294, "file": "app\\templates\\admin\\guide_management\\scenarios.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 209, "file": "app\\templates\\daily_management\\fixed_inspection_qrcode.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 615, "file": "app\\templates\\daily_management\\inspection_templates.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 502, "file": "app\\templates\\daily_management\\inspection_templates.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 676, "file": "app\\templates\\daily_management\\inspection_templates.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 761, "file": "app\\templates\\daily_management\\optimized_dashboard.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 816, "file": "app\\templates\\daily_management\\optimized_dashboard.html"}, {"type": "bootstrap4_js", "pattern": "'show\\.bs\\.modal'", "match": "'show.bs.modal'", "line": 401, "file": "app\\templates\\daily_management\\public_rate_inspection_photos.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 558, "file": "app\\templates\\daily_management\\simplified_inspection.html"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 561, "file": "app\\templates\\daily_management\\simplified_inspection.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 116, "file": "app\\templates\\daily_management\\trainings.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 166, "file": "app\\templates\\daily_management\\view_training.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 215, "file": "app\\templates\\daily_management\\components\\data_visualization.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 229, "file": "app\\templates\\daily_management\\components\\data_visualization.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 263, "file": "app\\templates\\daily_management\\components\\data_visualization.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 482, "file": "app\\templates\\daily_management\\widgets\\image_widget.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 778, "file": "app\\templates\\financial\\accounting_subjects\\form.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 826, "file": "app\\templates\\financial\\accounting_subjects\\form.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1079, "file": "app\\templates\\financial\\vouchers\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1137, "file": "app\\templates\\financial\\vouchers\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1291, "file": "app\\templates\\financial\\vouchers\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 202, "file": "app\\templates\\guide\\scenario_selection.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 226, "file": "app\\templates\\ingredient\\categories.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 243, "file": "app\\templates\\ingredient\\categories.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 247, "file": "app\\templates\\ingredient\\categories.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 236, "file": "app\\templates\\ingredient\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 253, "file": "app\\templates\\ingredient\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 257, "file": "app\\templates\\ingredient\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 245, "file": "app\\templates\\ingredient\\index_category.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 262, "file": "app\\templates\\ingredient\\index_category.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 266, "file": "app\\templates\\ingredient\\index_category.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 335, "file": "app\\templates\\inspection\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 282, "file": "app\\templates\\inspection\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 625, "file": "app\\templates\\inventory\\statistics.html"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 628, "file": "app\\templates\\inventory\\statistics.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 866, "file": "app\\templates\\main\\canteen_dashboard_new.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1804, "file": "app\\templates\\main\\index.html"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 1807, "file": "app\\templates\\main\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 764, "file": "app\\templates\\purchase_order\\create_form.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 872, "file": "app\\templates\\purchase_order\\create_form.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1057, "file": "app\\templates\\purchase_order\\create_from_menu.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 860, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 898, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 946, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 989, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1322, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1328, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1334, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1340, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 890, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 938, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 981, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1017, "file": "app\\templates\\purchase_order\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 802, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 832, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 870, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 903, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 816, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 854, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 887, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 926, "file": "app\\templates\\purchase_order\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 82, "file": "app\\templates\\recipe\\categories.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 99, "file": "app\\templates\\recipe\\categories.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 103, "file": "app\\templates\\recipe\\categories.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 368, "file": "app\\templates\\recipe\\form_simplified.html"}, {"type": "bootstrap4_js", "pattern": "'show\\.bs\\.modal'", "match": "'show.bs.modal'", "line": 269, "file": "app\\templates\\recipe\\form_simplified.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 535, "file": "app\\templates\\recipe\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 568, "file": "app\\templates\\recipe\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 583, "file": "app\\templates\\recipe\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 395, "file": "app\\templates\\recipe\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 439, "file": "app\\templates\\recipe\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 534, "file": "app\\templates\\stock_in\\batch_editor_simplified_scripts.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1008, "file": "app\\templates\\stock_in\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1361, "file": "app\\templates\\stock_in\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1384, "file": "app\\templates\\stock_in\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1453, "file": "app\\templates\\stock_in\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1105, "file": "app\\templates\\stock_in\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1380, "file": "app\\templates\\stock_in\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1417, "file": "app\\templates\\stock_in\\edit.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 755, "file": "app\\templates\\stock_in\\form.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1051, "file": "app\\templates\\stock_in\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1050, "file": "app\\templates\\stock_in\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1076, "file": "app\\templates\\stock_in\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1105, "file": "app\\templates\\stock_in\\view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 741, "file": "app\\templates\\stock_in\\wizard.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 958, "file": "app\\templates\\stock_in\\wizard.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 761, "file": "app\\templates\\stock_in\\wizard.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1163, "file": "app\\templates\\stock_in\\wizard.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 107, "file": "app\\templates\\stock_in\\wizard_simple.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 184, "file": "app\\templates\\stock_in\\wizard_simple.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 103, "file": "app\\templates\\supplier\\category_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 120, "file": "app\\templates\\supplier\\category_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 124, "file": "app\\templates\\supplier\\category_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 216, "file": "app\\templates\\supplier\\certificate_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 233, "file": "app\\templates\\supplier\\certificate_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 237, "file": "app\\templates\\supplier\\certificate_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 163, "file": "app\\templates\\supplier\\certificate_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 195, "file": "app\\templates\\supplier\\certificate_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 180, "file": "app\\templates\\supplier\\certificate_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 184, "file": "app\\templates\\supplier\\certificate_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 347, "file": "app\\templates\\supplier\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 364, "file": "app\\templates\\supplier\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 368, "file": "app\\templates\\supplier\\index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 416, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 527, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 547, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 567, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 587, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 604, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 612, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 433, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 437, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 638, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 642, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 690, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 694, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 700, "file": "app\\templates\\supplier\\product_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 325, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 357, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 455, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 342, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 346, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 374, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 378, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 481, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 485, "file": "app\\templates\\supplier\\product_view.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 284, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 320, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 356, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 301, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 309, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 337, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 345, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 373, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 381, "file": "app\\templates\\supplier\\school_index.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 1678, "file": "app\\templates\\weekly_menu\\plan.html"}, {"type": "bootstrap4_js", "pattern": "'shown\\.bs\\.modal'", "match": "'shown.bs.modal'", "line": 792, "file": "app\\templates\\weekly_menu\\plan_v2.html"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 669, "file": "app\\static\\js\\image_uploader.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 687, "file": "app\\static\\js\\mobile-enhancements.js"}, {"type": "bootstrap4_js", "pattern": "'show\\.bs\\.modal'", "match": "'show.bs.modal'", "line": 159, "file": "app\\static\\js\\stock-in-detail-enhancement.js"}, {"type": "bootstrap4_js", "pattern": "'hide\\.bs\\.modal'", "match": "'hide.bs.modal'", "line": 163, "file": "app\\static\\js\\stock-in-detail-enhancement.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 111, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 134, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 386, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 391, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 402, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 406, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 42, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 114, "file": "app\\static\\js\\user_guide.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 10, "file": "app\\static\\js\\video-management.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 27, "file": "app\\static\\js\\video-management.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 77, "file": "app\\static\\js\\video-management.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 955, "file": "app\\static\\js\\weekly_menu_modal.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1218, "file": "app\\static\\js\\weekly_menu_modal.js"}, {"type": "bootstrap4_js", "pattern": "'show\\.bs\\.modal'", "match": "'show.bs.modal'", "line": 364, "file": "app\\static\\js\\weekly_menu_modal.js"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 369, "file": "app\\static\\js\\weekly_menu_modal.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('show'\\)", "match": ".modal('show')", "line": 958, "file": "app\\static\\js\\weekly_menu_v2.js"}, {"type": "bootstrap4_js", "pattern": "\\.modal\\('hide'\\)", "match": ".modal('hide')", "line": 1227, "file": "app\\static\\js\\weekly_menu_v2.js"}, {"type": "bootstrap4_js", "pattern": "'show\\.bs\\.modal'", "match": "'show.bs.modal'", "line": 364, "file": "app\\static\\js\\weekly_menu_v2.js"}, {"type": "bootstrap4_js", "pattern": "'hidden\\.bs\\.modal'", "match": "'hidden.bs.modal'", "line": 369, "file": "app\\static\\js\\weekly_menu_v2.js"}], "deprecated_features": []}}