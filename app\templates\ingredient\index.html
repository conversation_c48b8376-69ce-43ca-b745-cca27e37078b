{% extends 'base.html' %}

{% block title %}食材管理{% endblock %}

{% block content %}
<!-- 现代化页面头部 -->
<div class="d-flex justify-content-between align-items-start mb-4">
  <div class="flex-grow-1">
    <h1 class="h3 mb-2 fw-bold text-dark">食材管理</h1>
    <p class="text-muted mb-0 fs-6">管理系统中的所有食材信息，包括基本信息、分类、规格和存储条件</p>
  </div>

  <div class="flex-shrink-0 ms-3">
    <div class="btn-toolbar gap-2">
      <a href="{{ url_for('ingredient.create') }}" class="btn btn-gradient-1 modern-btn btn-sm">
        <i class="fas fa-plus me-1"></i>添加食材
      </a>
      <a href="{{ url_for('ingredient_category.index') }}" class="btn btn-gradient-2 modern-btn btn-sm">
        <i class="fas fa-tags me-1"></i>分类管理
      </a>
    </div>
  </div>
</div>

<!-- 视图模式切换和搜索 -->
<div class="card modern-card mb-4">
  <div class="card-body p-3">
    <div class="row g-3 align-items-end">
      <div class="col-md-3">
        <div class="btn-group w-100" role="group">
          <a href="{{ url_for('ingredient.index', view_mode='list', category_id=request.args.get('category_id'), keyword=request.args.get('keyword')) }}"
             class="btn {{ 'btn-gradient-1' if view_mode == 'list' else 'btn-outline-primary' }} modern-btn">
            <i class="fas fa-list me-1"></i>列表视图
          </a>
          <a href="{{ url_for('ingredient.index', view_mode='category', category_id=request.args.get('category_id'), keyword=request.args.get('keyword')) }}"
             class="btn {{ 'btn-gradient-1' if view_mode == 'category' else 'btn-outline-primary' }} modern-btn">
            <i class="fas fa-th-large me-1"></i>分类视图
          </a>
        </div>
      </div>

      <div class="col-md-4">
        <form method="GET" action="{{ url_for('ingredient.index') }}" class="d-flex gap-2">
          <input type="hidden" name="view_mode" value="{{ view_mode }}">
          <div class="form-floating flex-grow-1">
            <input type="text" class="form-control" id="keyword" name="keyword"
                   value="{{ request.args.get('keyword', '') }}" placeholder="搜索食材...">
            <label for="keyword">搜索食材</label>
          </div>
          <button type="submit" class="btn btn-gradient-1 modern-btn">
            <i class="fas fa-search"></i>
          </button>
        </form>
      </div>

      <div class="col-md-3">
        <form method="GET" action="{{ url_for('ingredient.index') }}">
          <input type="hidden" name="view_mode" value="{{ view_mode }}">
          <input type="hidden" name="keyword" value="{{ request.args.get('keyword', '') }}">
          <div class="form-floating">
            <select class="form-select" id="category_id" name="category_id" onchange="this.form.submit()">
              <option value="">全部分类</option>
              {% for category in categories %}
              <option value="{{ category.id }}" {% if request.args.get('category_id') == category.id|string %}selected{% endif %}>
                {{ category.name }}
              </option>
              {% endfor %}
            </select>
            <label for="category_id">食材分类</label>
          </div>
        </form>
      </div>

      <div class="col-md-2">
        <a href="{{ url_for('ingredient.index') }}" class="btn btn-outline-secondary modern-btn w-100">
          <i class="fas fa-times me-1"></i>清除
        </a>
      </div>
    </div>
  </div>
</div>

<!-- 现代化食材列表 -->
<div class="card modern-card">
  <div class="card-header border-0" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="d-flex justify-content-between align-items-center text-white">
      <div>
        <h6 class="mb-1 fw-semibold">食材列表</h6>
        <small class="opacity-75">共 {{ ingredients|length }} 种食材</small>
      </div>
      <div class="d-flex gap-2">
        <button class="btn btn-sm btn-light" onclick="exportIngredients()">
          <i class="fas fa-download me-1"></i>导出
        </button>
        <button class="btn btn-sm btn-light" onclick="refreshList()">
          <i class="fas fa-sync-alt me-1"></i>刷新
        </button>
      </div>
    </div>
  </div>

  <!-- 桌面端表格 -->
  <div class="d-none d-lg-block">
    <div class="table-responsive">
      <table class="table table-hover mb-0">
        <thead class="table-light">
          <tr>
            <th class="border-0 fw-semibold text-center" style="width: 8%;">ID</th>
            <th class="border-0 fw-semibold text-center" style="width: 10%;">图片</th>
            <th class="border-0 fw-semibold" style="width: 15%;">名称</th>
            <th class="border-0 fw-semibold" style="width: 12%;">分类</th>
            <th class="border-0 fw-semibold" style="width: 12%;">规格</th>
            <th class="border-0 fw-semibold" style="width: 8%;">单位</th>
            <th class="border-0 fw-semibold" style="width: 12%;">存储条件</th>
            <th class="border-0 fw-semibold text-center" style="width: 8%;">保质期</th>
            <th class="border-0 fw-semibold text-center" style="width: 8%;">状态</th>
            <th class="border-0 fw-semibold text-center" style="width: 15%;">操作</th>
          </tr>
        </thead>
        <tbody>
          {% for ingredient in ingredients %}
          <tr class="border-0">
            <td class="py-3 border-0 text-center">
              <span class="fw-medium text-primary">#{{ ingredient.id }}</span>
            </td>
            <td class="py-3 border-0 text-center">
              <div class="d-flex justify-content-center">
                {% if ingredient.base_image %}
                <img src="{{ url_for('static', filename=ingredient.base_image) }}"
                     alt="{{ ingredient.name }}"
                     class="rounded-3 shadow-sm"
                     style="width: 50px; height: 50px; object-fit: cover;"
                     onerror="this.src='{{ url_for('static', filename='img/qr-code-placeholder.png') }}'; this.onerror=null;">
                {% else %}
                <div class="bg-light rounded-3 d-flex align-items-center justify-content-center"
                     style="width: 50px; height: 50px;">
                  <i class="fas fa-image text-muted"></i>
                </div>
                {% endif %}
              </div>
            </td>
            <td class="py-3 border-0">
              <div class="d-flex align-items-center">
                <div class="bg-success bg-opacity-10 rounded-circle p-2 me-2">
                  <i class="fas fa-carrot text-success"></i>
                </div>
                <div>
                  <div class="fw-medium">{{ ingredient.name }}</div>
                  <small class="text-muted">食材</small>
                </div>
              </div>
            </td>
            <td class="py-3 border-0">
              <span class="badge bg-info bg-opacity-10 text-info border border-info">
                {% if ingredient.category_rel %}
                  {{ ingredient.category_rel.name }}
                {% else %}
                  {{ ingredient.category or '未分类' }}
                {% endif %}
              </span>
            </td>
            <td class="py-3 border-0">{{ ingredient.specification or '-' }}</td>
            <td class="py-3 border-0">
              <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary">
                {{ ingredient.unit }}
              </span>
            </td>
            <td class="py-3 border-0">{{ ingredient.storage_condition or ingredient.storage_temp or '-' }}</td>
            <td class="py-3 border-0 text-center">
              {% if ingredient.shelf_life %}
              <span class="badge bg-warning bg-opacity-10 text-warning border border-warning">
                {{ ingredient.shelf_life }}天
              </span>
              {% else %}
              <span class="text-muted">-</span>
              {% endif %}
            </td>
            <td class="py-3 border-0 text-center">
              {% if ingredient.status == 1 %}
              <span class="badge bg-success bg-opacity-10 text-success border border-success">启用</span>
              {% else %}
              <span class="badge bg-danger bg-opacity-10 text-danger border border-danger">停用</span>
              {% endif %}
            </td>
            <td class="py-3 border-0 text-center">
              <div class="btn-group btn-group-sm">
                <a href="{{ url_for('ingredient.view', id=ingredient.id) }}"
                   class="btn btn-outline-info" title="查看详情">
                  <i class="fas fa-eye"></i>
                </a>
                {% if current_user.is_admin() or (ingredient.area_id and ingredient.area_id == current_user.get_current_area().id) %}
                <a href="{{ url_for('ingredient.edit', id=ingredient.id) }}"
                   class="btn btn-outline-primary" title="编辑食材">
                  <i class="fas fa-edit"></i>
                </a>
                <button type="button" class="btn btn-outline-danger delete-btn"
                        data-id="{{ ingredient.id }}" title="删除食材">
                  <i class="fas fa-trash"></i>
                </button>
                {% elif ingredient.is_global %}
                <span class="badge bg-info bg-opacity-10 text-info border border-info">系统食材</span>
                {% else %}
                <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary">其他学校</span>
                {% endif %}
              </div>
            </td>
          </tr>
          {% else %}
          <tr>
            <td colspan="10" class="text-center py-5 border-0">
              <div class="text-muted">
                <i class="fas fa-carrot fs-1 opacity-25 mb-3"></i>
                <div>暂无食材数据</div>
                <small>您可以添加新的食材或调整筛选条件</small>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- 移动端卡片 -->
  <div class="d-lg-none">
    <div class="card-body p-3">
      {% for ingredient in ingredients %}
      <div class="card mb-3 border-0 shadow-sm modern-card-sm">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="flex-grow-1 d-flex align-items-center">
              <div class="me-3">
                {% if ingredient.base_image %}
                <img src="{{ url_for('static', filename=ingredient.base_image) }}"
                     alt="{{ ingredient.name }}"
                     class="rounded-3 shadow-sm"
                     style="width: 60px; height: 60px; object-fit: cover;"
                     onerror="this.src='{{ url_for('static', filename='img/qr-code-placeholder.png') }}'; this.onerror=null;">
                {% else %}
                <div class="bg-light rounded-3 d-flex align-items-center justify-content-center"
                     style="width: 60px; height: 60px;">
                  <i class="fas fa-image text-muted"></i>
                </div>
                {% endif %}
              </div>
              <div>
                <h6 class="mb-1 fw-semibold text-primary">{{ ingredient.name }}</h6>
                <div class="text-muted small">
                  ID: {{ ingredient.id }} |
                  {% if ingredient.category_rel %}
                    {{ ingredient.category_rel.name }}
                  {% else %}
                    {{ ingredient.category or '未分类' }}
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="text-end">
              {% if ingredient.status == 1 %}
              <span class="badge bg-success bg-opacity-10 text-success border border-success">启用</span>
              {% else %}
              <span class="badge bg-danger bg-opacity-10 text-danger border border-danger">停用</span>
              {% endif %}
            </div>
          </div>

          <div class="row g-2 mb-2">
            <div class="col-6">
              <div class="bg-light rounded-3 p-2">
                <small class="text-muted d-block">规格</small>
                <div class="fw-medium">{{ ingredient.specification or '-' }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light rounded-3 p-2">
                <small class="text-muted d-block">单位</small>
                <div class="fw-medium">{{ ingredient.unit }}</div>
              </div>
            </div>
          </div>

          <div class="row g-2 mb-3">
            <div class="col-6">
              <div class="bg-light rounded-3 p-2">
                <small class="text-muted d-block">存储条件</small>
                <div class="fw-medium">{{ ingredient.storage_condition or ingredient.storage_temp or '-' }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light rounded-3 p-2">
                <small class="text-muted d-block">保质期</small>
                <div class="fw-medium">{{ ingredient.shelf_life + '天' if ingredient.shelf_life else '-' }}</div>
              </div>
            </div>
          </div>

          <div class="d-grid gap-2 d-md-flex">
            <a href="{{ url_for('ingredient.view', id=ingredient.id) }}"
               class="btn btn-outline-info btn-sm flex-fill">
              <i class="fas fa-eye me-1"></i>查看
            </a>
            {% if current_user.is_admin() or (ingredient.area_id and ingredient.area_id == current_user.get_current_area().id) %}
            <a href="{{ url_for('ingredient.edit', id=ingredient.id) }}"
               class="btn btn-outline-primary btn-sm flex-fill">
              <i class="fas fa-edit me-1"></i>编辑
            </a>
            <button type="button" class="btn btn-outline-danger btn-sm flex-fill delete-btn"
                    data-id="{{ ingredient.id }}">
              <i class="fas fa-trash me-1"></i>删除
            </button>
            {% elif ingredient.is_global %}
            <span class="badge bg-info bg-opacity-10 text-info border border-info">系统食材</span>
            {% else %}
            <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary">其他学校</span>
            {% endif %}
          </div>
        </div>
      </div>
      {% else %}
      <div class="text-center py-5">
        <i class="fas fa-carrot fs-1 text-muted opacity-25 mb-3"></i>
        <div class="text-muted">暂无食材数据</div>
        <small class="text-muted">您可以添加新的食材或调整筛选条件</small>
      </div>
      {% endfor %}
    </div>
  </div>
</div>

<!-- 现代化分页 -->
{% if pagination and pagination.pages > 1 %}
<div class="d-flex justify-content-center mt-4">
  <nav aria-label="Page navigation">
    <ul class="pagination modern-pagination">
      {% if pagination.has_prev %}
      <li class="page-item">
        <a class="page-link" href="{{ url_for('ingredient.index', page=pagination.prev_num, per_page=pagination.per_page, category_id=request.args.get('category_id'), keyword=request.args.get('keyword'), view_mode=view_mode) }}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <span class="page-link"><i class="fas fa-chevron-left"></i></span>
      </li>
      {% endif %}

      {% for page_num in pagination.iter_pages() %}
        {% if page_num %}
          {% if page_num == pagination.page %}
          <li class="page-item active">
            <span class="page-link">{{ page_num }}</span>
          </li>
          {% else %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('ingredient.index', page=page_num, per_page=pagination.per_page, category_id=request.args.get('category_id'), keyword=request.args.get('keyword'), view_mode=view_mode) }}">{{ page_num }}</a>
          </li>
          {% endif %}
        {% else %}
        <li class="page-item disabled">
          <span class="page-link">...</span>
        </li>
        {% endif %}
      {% endfor %}

      {% if pagination.has_next %}
      <li class="page-item">
        <a class="page-link" href="{{ url_for('ingredient.index', page=pagination.next_num, per_page=pagination.per_page, category_id=request.args.get('category_id'), keyword=request.args.get('keyword'), view_mode=view_mode) }}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <span class="page-link"><i class="fas fa-chevron-right"></i></span>
      </li>
      {% endif %}
    </ul>
  </nav>
</div>

<div class="text-center mt-2">
  <small class="text-muted">
    显示 {{ pagination.total }} 条记录中的第
    {{ (pagination.page - 1) * pagination.per_page + 1 }}
    到
    {{ (pagination.page * pagination.per_page) if (pagination.page * pagination.per_page < pagination.total) else pagination.total }}
    条
  </small>
</div>
{% endif %}

<!-- 现代化删除确认模态框 -->
<div class="modal fade modern-modal" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <div class="d-flex align-items-center">
          <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
            <i class="fas fa-exclamation-triangle text-danger"></i>
          </div>
          <div>
            <h5 class="modal-title mb-0" id="deleteModalLabel">确认删除食材</h5>
            <small class="text-muted">此操作不可恢复</small>
          </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="alert modern-alert alert-warning">
          <i class="fas fa-info-circle me-2"></i>
          删除食材将会影响以下数据：
          <ul class="mb-0 mt-2">
            <li>相关的库存记录</li>
            <li>菜谱中的食材配方</li>
            <li>采购订单中的食材项</li>
          </ul>
        </div>
        <p class="mb-0">确定要删除这个食材吗？</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary modern-btn" data-bs-dismiss="modal">
          <i class="fas fa-times me-1"></i>取消
        </button>
        <button type="button" class="btn btn-danger modern-btn" id="confirmDelete">
          <i class="fas fa-trash me-1"></i>确认删除
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    let deleteId = null;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

    // 删除按钮事件处理
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            deleteId = this.dataset.id;
            deleteModal.show();
        });
    });

    // 确认删除
    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (deleteId) {
            const deleteBtn = this;
            deleteBtn.disabled = true;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';

            fetch(`{{ url_for("ingredient.delete", id=0) }}`.replace('0', deleteId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    toastr.success(data.message || '删除成功');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    toastr.error(data.message || '删除失败');
                }
                deleteModal.hide();
            })
            .catch(error => {
                console.error('删除失败:', error);
                toastr.error('删除失败，请稍后重试！');
                deleteModal.hide();
            })
            .finally(() => {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '<i class="fas fa-trash me-1"></i>确认删除';
            });
        }
    });

    // 导出功能
    window.exportIngredients = function() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'excel');
        window.open(`{{ url_for('ingredient.index') }}?${params.toString()}`, '_blank');
    };

    // 刷新列表
    window.refreshList = function() {
        window.location.reload();
    };

    // 搜索表单增强
    const searchInput = document.querySelector('input[name="keyword"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
    }

    // 添加页面加载动画
    document.querySelectorAll('.modern-card').forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
});
</script>
{% endblock %}
