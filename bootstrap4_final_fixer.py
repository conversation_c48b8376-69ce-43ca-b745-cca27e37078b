#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 4 最终修复工具
只修复真正的Bootstrap 4遗留问题，排除误报
"""

import os
import re
from pathlib import Path

class Bootstrap4FinalFixer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.fixes_applied = 0
        self.files_checked = 0
        
        # 真正需要修复的Bootstrap 4问题
        self.real_bootstrap4_issues = [
            # HTML模板中的真实Bootstrap 4类名
            (r'\bcustom-select\b', 'form-select'),
            (r'\bcustom-file\b', 'form-control'),
            (r'\bcustom-file-input\b', 'form-control'),
            (r'\bcustom-file-label\b', 'form-label'),
            (r'\bcustom-checkbox\b', 'form-check'),
            (r'\bcustom-radio\b', 'form-check'),
            (r'\bcustom-switch\b', 'form-switch'),
            (r'\bcustom-control\b', 'form-check'),
            (r'\bcustom-control-input\b', 'form-check-input'),
            (r'\bcustom-control-label\b', 'form-check-label'),
            (r'\bform-control-file\b', 'form-control'),
            (r'\bform-control-range\b', 'form-range'),
            
            # 徽章类名（在HTML中）
            (r'\bbadge-primary\b', 'bg-primary'),
            (r'\bbadge-secondary\b', 'bg-secondary'),
            (r'\bbadge-success\b', 'bg-success'),
            (r'\bbadge-danger\b', 'bg-danger'),
            (r'\bbadge-warning\b', 'bg-warning'),
            (r'\bbadge-info\b', 'bg-info'),
            (r'\bbadge-light\b', 'bg-light'),
            (r'\bbadge-dark\b', 'bg-dark'),
            (r'\bbadge-pill\b', 'rounded-pill'),
            
            # 按钮
            (r'\bbtn-default\b', 'btn-secondary'),
            
            # 文本对齐
            (r'\btext-left\b', 'text-start'),
            (r'\btext-right\b', 'text-end'),
            (r'\bfloat-left\b', 'float-start'),
            (r'\bfloat-right\b', 'float-end'),
            
            # 边框
            (r'\bborder-left\b', 'border-start'),
            (r'\bborder-right\b', 'border-end'),
            (r'\brounded-left\b', 'rounded-start'),
            (r'\brounded-right\b', 'rounded-end'),
            
            # 间距
            (r'\bml-(\d+)\b', r'ms-\1'),
            (r'\bmr-(\d+)\b', r'me-\1'),
            (r'\bpl-(\d+)\b', r'ps-\1'),
            (r'\bpr-(\d+)\b', r'pe-\1'),
            
            # 网格
            (r'\bno-gutters\b', 'g-0'),
            
            # 工具类
            (r'\bsr-only\b', 'visually-hidden'),
            (r'\bsr-only-focusable\b', 'visually-hidden-focusable'),
            
            # 字体
            (r'\bfont-weight-bold\b', 'fw-bold'),
            (r'\bfont-weight-normal\b', 'fw-normal'),
            (r'\bfont-italic\b', 'fst-italic'),
        ]
        
        # HTML属性修复
        self.attribute_fixes = [
            (r'data-toggle="modal"', 'data-bs-toggle="modal"'),
            (r'data-toggle="dropdown"', 'data-bs-toggle="dropdown"'),
            (r'data-toggle="collapse"', 'data-bs-toggle="collapse"'),
            (r'data-toggle="tooltip"', 'data-bs-toggle="tooltip"'),
            (r'data-toggle="popover"', 'data-bs-toggle="popover"'),
            (r'data-toggle="tab"', 'data-bs-toggle="tab"'),
            (r'data-toggle="pill"', 'data-bs-toggle="pill"'),
            (r'data-target="([^"]*)"', r'data-bs-target="\1"'),
            (r'data-dismiss="modal"', 'data-bs-dismiss="modal"'),
            (r'data-dismiss="alert"', 'data-bs-dismiss="alert"'),
            (r'data-placement="([^"]*)"', r'data-bs-placement="\1"'),
        ]

    def is_css_media_query(self, content, match_start):
        """检查匹配是否在CSS媒体查询中"""
        # 向前查找@media
        before_content = content[:match_start]
        lines_before = before_content.split('\n')
        
        # 检查最近的几行是否包含@media
        for i in range(min(10, len(lines_before))):
            line = lines_before[-(i+1)].strip()
            if '@media' in line:
                return True
            if line and not line.startswith('/*') and '{' in line and '@media' not in line:
                break
        return False

    def fix_file(self, file_path):
        """修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_content = content
            changes_made = 0
            
            # 只处理HTML文件的类名和属性
            if file_path.suffix.lower() == '.html':
                # 修复类名
                for pattern, replacement in self.real_bootstrap4_issues:
                    old_content = content
                    content = re.sub(pattern, replacement, content)
                    if content != old_content:
                        changes_made += 1
                
                # 修复属性
                for pattern, replacement in self.attribute_fixes:
                    old_content = content
                    content = re.sub(pattern, replacement, content)
                    if content != old_content:
                        changes_made += 1
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.fixes_applied += 1
                print(f"✅ 修复了 {file_path.relative_to(self.project_root)} - {changes_made} 处修改")
                
            self.files_checked += 1
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")

    def scan_templates(self):
        """只扫描模板文件"""
        template_dir = self.project_root / "app" / "templates"
        
        if not template_dir.exists():
            print("❌ 模板目录不存在")
            return
            
        print("🔍 扫描HTML模板文件...")
        
        for file_path in template_dir.rglob("*.html"):
            self.fix_file(file_path)

    def run(self):
        """运行修复工具"""
        print("🚀 Bootstrap 4 最终修复工具启动")
        print("=" * 40)
        print("只修复HTML模板中的真实Bootstrap 4遗留问题")
        print("排除CSS媒体查询和JavaScript方法调用")
        print()
        
        self.scan_templates()
        
        print(f"\n📊 修复完成:")
        print(f"   检查文件: {self.files_checked}")
        print(f"   修复文件: {self.fixes_applied}")
        
        if self.fixes_applied == 0:
            print(f"\n✅ 恭喜！HTML模板中没有发现真正的Bootstrap 4遗留问题")
        else:
            print(f"\n🎉 已修复所有真正的Bootstrap 4遗留问题")

if __name__ == "__main__":
    fixer = Bootstrap4FinalFixer()
    fixer.run()
