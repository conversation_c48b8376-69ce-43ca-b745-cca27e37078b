<!-- 标准化表单组件 -->
<!-- 使用方法: {% from 'components/forms.html' import form_field, form_card, floating_field %} -->

<!-- 标准表单字段 -->
{% macro form_field(field, label=None, help_text=None, required=False, size="normal") %}
<div class="mb-3 {% if size == 'small' %}mb-2{% endif %}">
  {% if label %}
  <label for="{{ field.id }}" class="form-label {% if required %}required{% endif %}">
    {{ label }}
    {% if required %}<span class="text-danger">*</span>{% endif %}
  </label>
  {% endif %}
  
  {% if field.type == 'SelectField' %}
  {{ field(class="form-select" + (" form-select-sm" if size == "small" else "")) }}
  {% elif field.type == 'TextAreaField' %}
  {{ field(class="form-control" + (" form-control-sm" if size == "small" else ""), rows=3) }}
  {% elif field.type == 'BooleanField' %}
  <div class="form-check">
    {{ field(class="form-check-input") }}
    <label class="form-check-label" for="{{ field.id }}">
      {{ field.label.text }}
    </label>
  </div>
  {% else %}
  {{ field(class="form-control" + (" form-control-sm" if size == "small" else "")) }}
  {% endif %}
  
  {% if help_text %}
  <div class="form-text">{{ help_text }}</div>
  {% endif %}
  
  {% if field.errors %}
  {% for error in field.errors %}
  <div class="invalid-feedback d-block">{{ error }}</div>
  {% endfor %}
  {% endif %}
</div>
{% endmacro %}

<!-- 浮动标签字段 -->
{% macro floating_field(field, placeholder=None, help_text=None, required=False) %}
<div class="form-floating mb-3">
  {% if field.type == 'SelectField' %}
  {{ field(class="form-select") }}
  {% elif field.type == 'TextAreaField' %}
  {{ field(class="form-control", style="height: 100px") }}
  {% else %}
  {{ field(class="form-control", placeholder=placeholder or field.label.text) }}
  {% endif %}
  
  <label for="{{ field.id }}">
    {{ field.label.text }}
    {% if required %}<span class="text-danger">*</span>{% endif %}
  </label>
  
  {% if help_text %}
  <div class="form-text">{{ help_text }}</div>
  {% endif %}
  
  {% if field.errors %}
  {% for error in field.errors %}
  <div class="invalid-feedback d-block">{{ error }}</div>
  {% endfor %}
  {% endif %}
</div>
{% endmacro %}

<!-- 输入组字段 -->
{% macro input_group_field(field, prepend=None, append=None, help_text=None, required=False) %}
<div class="mb-3">
  {% if field.label %}
  <label for="{{ field.id }}" class="form-label {% if required %}required{% endif %}">
    {{ field.label.text }}
    {% if required %}<span class="text-danger">*</span>{% endif %}
  </label>
  {% endif %}
  
  <div class="input-group">
    {% if prepend %}
    <span class="input-group-text">{{ prepend }}</span>
    {% endif %}
    
    {{ field(class="form-control") }}
    
    {% if append %}
    <span class="input-group-text">{{ append }}</span>
    {% endif %}
  </div>
  
  {% if help_text %}
  <div class="form-text">{{ help_text }}</div>
  {% endif %}
  
  {% if field.errors %}
  {% for error in field.errors %}
  <div class="invalid-feedback d-block">{{ error }}</div>
  {% endfor %}
  {% endif %}
</div>
{% endmacro %}

<!-- 表单卡片容器 -->
{% macro form_card(title, form_content, actions=None, size="normal") %}
<div class="card {% if size == 'small' %}card-sm{% endif %}">
  {% if title %}
  <div class="card-header">
    <h5 class="card-title mb-0">{{ title }}</h5>
  </div>
  {% endif %}
  
  <div class="card-body {% if size == 'small' %}py-3{% endif %}">
    {{ form_content }}
  </div>
  
  {% if actions %}
  <div class="card-footer bg-light">
    <div class="d-flex justify-content-end gap-2">
      {% for action in actions %}
      {% if action.type == 'submit' %}
      <button type="submit" class="btn {{ action.class|default('btn-primary') }}">
        {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </button>
      {% elif action.type == 'button' %}
      <button type="button" class="btn {{ action.class|default('btn-secondary') }}"
              {% if action.onclick %}onclick="{{ action.onclick }}"{% endif %}>
        {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </button>
      {% else %}
      <a href="{{ action.url }}" class="btn {{ action.class|default('btn-secondary') }}">
        {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </a>
      {% endif %}
      {% endfor %}
    </div>
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 多列表单布局 -->
{% macro multi_column_form(fields, columns=2) %}
<div class="row g-3">
  {% for field_group in fields %}
  <div class="col-md-{{ 12 // columns }}">
    {% if field_group.type == 'floating' %}
    {{ floating_field(
      field=field_group.field,
      placeholder=field_group.placeholder,
      help_text=field_group.help_text,
      required=field_group.required
    ) }}
    {% elif field_group.type == 'input_group' %}
    {{ input_group_field(
      field=field_group.field,
      prepend=field_group.prepend,
      append=field_group.append,
      help_text=field_group.help_text,
      required=field_group.required
    ) }}
    {% else %}
    {{ form_field(
      field=field_group.field,
      label=field_group.label,
      help_text=field_group.help_text,
      required=field_group.required,
      size=field_group.size
    ) }}
    {% endif %}
  </div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 搜索表单 -->
{% macro search_form(action, fields=None, method="GET") %}
<form method="{{ method }}" action="{{ action }}" class="mb-3">
  <div class="card border-0 bg-light">
    <div class="card-body py-3">
      <div class="row g-3 align-items-end">
        <!-- 搜索框 -->
        <div class="col-md-4">
          <div class="input-group">
            <input type="text" class="form-control" name="search" 
                   placeholder="搜索..." value="{{ request.args.get('search', '') }}">
            <button class="btn btn-outline-secondary" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        
        <!-- 自定义字段 -->
        {% if fields %}
        {% for field in fields %}
        <div class="col-md-{{ field.width|default('2') }}">
          {% if field.type == 'select' %}
          <select class="form-select form-select-sm" name="{{ field.name }}">
            <option value="">{{ field.label }}</option>
            {% for option in field.options %}
            <option value="{{ option.value }}" 
                    {% if request.args.get(field.name) == option.value %}selected{% endif %}>
              {{ option.text }}
            </option>
            {% endfor %}
          </select>
          {% elif field.type == 'date' %}
          <input type="date" class="form-control form-control-sm" 
                 name="{{ field.name }}" value="{{ request.args.get(field.name, '') }}"
                 placeholder="{{ field.label }}">
          {% else %}
          <input type="text" class="form-control form-control-sm" 
                 name="{{ field.name }}" value="{{ request.args.get(field.name, '') }}"
                 placeholder="{{ field.label }}">
          {% endif %}
        </div>
        {% endfor %}
        {% endif %}
        
        <!-- 操作按钮 -->
        <div class="col-md-auto">
          <div class="btn-group">
            <button type="submit" class="btn btn-primary btn-sm">
              <i class="fas fa-filter me-1"></i>筛选
            </button>
            <a href="{{ request.url_root }}{{ request.endpoint }}" 
               class="btn btn-outline-secondary btn-sm">
              <i class="fas fa-times me-1"></i>清除
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
{% endmacro %}

<!-- 快速操作表单 -->
{% macro quick_action_form(actions) %}
<div class="btn-toolbar gap-2 mb-3">
  {% for action in actions %}
  {% if action.type == 'form' %}
  <form method="{{ action.method|default('POST') }}" action="{{ action.url }}" class="d-inline">
    {{ action.csrf_token() if action.csrf_token }}
    <button type="submit" class="btn {{ action.class|default('btn-primary') }} btn-sm"
            {% if action.confirm %}onclick="return confirm('{{ action.confirm }}')"{% endif %}>
      {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
      {{ action.text }}
    </button>
  </form>
  {% else %}
  <a href="{{ action.url }}" class="btn {{ action.class|default('btn-primary') }} btn-sm"
     {% if action.confirm %}onclick="return confirm('{{ action.confirm }}')"{% endif %}>
    {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
    {{ action.text }}
  </a>
  {% endif %}
  {% endfor %}
</div>
{% endmacro %}

<!-- 表单验证样式 -->
<style>
.form-label.required::after {
  content: " *";
  color: #dc3545;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: .65;
  transform: scale(.85) translateY(-.5rem) translateX(.15rem);
}

.card-sm .card-body {
  padding: 1rem;
}

.card-sm .card-header {
  padding: 0.5rem 1rem;
}

.card-sm .card-footer {
  padding: 0.5rem 1rem;
}
</style>
