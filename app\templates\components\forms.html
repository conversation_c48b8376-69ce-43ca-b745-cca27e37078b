<!-- Bootstrap 5.3.6 现代化表单组件库 -->
<!-- 使用方法: {% from 'components/forms.html' import modern_form, floating_field, input_group_field %} -->

<!-- 现代化表单字段 -->
{% macro modern_form_field(field, options={}) %}
{% set field_type = options.get('type', 'standard') %}
{% set size = options.get('size', 'normal') %}
{% set required = options.get('required', False) %}
{% set help_text = options.get('help_text') %}
{% set icon = options.get('icon') %}
{% set placeholder = options.get('placeholder') %}

<div class="mb-3 {% if size == 'small' %}mb-2{% endif %}">
  {% if field_type == 'floating' %}
  <!-- 浮动标签 -->
  <div class="form-floating">
    {% if field.type == 'SelectField' %}
    {{ field(class="form-select" + (" form-select-sm" if size == "small" else "")) }}
    {% elif field.type == 'TextAreaField' %}
    {{ field(class="form-control", style="height: 100px", placeholder=placeholder or field.label.text) }}
    {% else %}
    {{ field(class="form-control" + (" form-control-sm" if size == "small" else ""), placeholder=placeholder or field.label.text) }}
    {% endif %}
    <label for="{{ field.id }}">
      {{ field.label.text }}
      {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
  </div>

  {% elif field_type == 'input_group' %}
  <!-- 输入组 -->
  {% if field.label %}
  <label for="{{ field.id }}" class="form-label {% if required %}required{% endif %}">
    {{ field.label.text }}
    {% if required %}<span class="text-danger">*</span>{% endif %}
  </label>
  {% endif %}
  <div class="input-group">
    {% if icon %}
    <span class="input-group-text">
      <i class="{{ icon }}"></i>
    </span>
    {% endif %}
    {{ field(class="form-control" + (" form-control-sm" if size == "small" else "")) }}
    {% if options.get('append') %}
    <span class="input-group-text">{{ options.append }}</span>
    {% endif %}
  </div>

  {% elif field.type == 'BooleanField' %}
  <!-- 复选框/开关 -->
  <div class="form-check {% if options.get('switch') %}form-switch{% endif %}">
    {{ field(class="form-check-input") }}
    <label class="form-check-label" for="{{ field.id }}">
      {{ field.label.text }}
      {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
  </div>

  {% else %}
  <!-- 标准字段 -->
  {% if field.label %}
  <label for="{{ field.id }}" class="form-label {% if required %}required{% endif %}">
    {{ field.label.text }}
    {% if required %}<span class="text-danger">*</span>{% endif %}
  </label>
  {% endif %}

  {% if field.type == 'SelectField' %}
  {{ field(class="form-select" + (" form-select-sm" if size == "small" else "")) }}
  {% elif field.type == 'TextAreaField' %}
  {{ field(class="form-control" + (" form-control-sm" if size == "small" else ""), rows=options.get('rows', 3)) }}
  {% else %}
  {{ field(class="form-control" + (" form-control-sm" if size == "small" else "")) }}
  {% endif %}
  {% endif %}

  {% if help_text %}
  <div class="form-text">
    <i class="fas fa-info-circle me-1"></i>{{ help_text }}
  </div>
  {% endif %}

  {% if field.errors %}
  {% for error in field.errors %}
  <div class="invalid-feedback d-block">
    <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
  </div>
  {% endfor %}
  {% endif %}
</div>
{% endmacro %}

<!-- 智能表单布局 -->
{% macro smart_form_layout(fields, columns=2, gap="3") %}
<div class="row g-{{ gap }}">
  {% for field_config in fields %}
  {% set col_class = field_config.get('col_class', 'col-md-' ~ (12 // columns)) %}
  <div class="{{ col_class }}">
    {{ modern_form_field(field_config.field, field_config.options) }}
  </div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 表单卡片容器 -->
{% macro form_card(title, form_content, actions=None, options={}) %}
{% set size = options.get('size', 'normal') %}
{% set color = options.get('color', 'primary') %}
{% set collapsible = options.get('collapsible', False) %}
{% set card_id = options.get('id', 'form-card-' ~ range(1000, 9999) | random) %}

<div class="card border-0 shadow-sm {% if size == 'small' %}card-sm{% endif %}">
  {% if title %}
  <div class="card-header bg-{{ color }} text-white py-2">
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="card-title mb-0 fs-6">{{ title }}</h5>
      {% if collapsible %}
      <button class="btn btn-sm btn-link text-white p-0" type="button"
              data-bs-toggle="collapse" data-bs-target="#{{ card_id }}-body">
        <i class="fas fa-chevron-down"></i>
      </button>
      {% endif %}
    </div>
  </div>
  {% endif %}

  <div class="{% if collapsible %}collapse show{% endif %} card-body {% if size == 'small' %}py-3{% endif %}"
       {% if collapsible %}id="{{ card_id }}-body"{% endif %}>
    {{ form_content }}
  </div>

  {% if actions %}
  <div class="card-footer bg-light border-0">
    <div class="d-flex justify-content-end gap-2">
      {% for action in actions %}
      {% if action.type == 'submit' %}
      <button type="submit" class="btn {{ action.get('class', 'btn-primary') }}">
        {% if action.get('icon') %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </button>
      {% elif action.type == 'button' %}
      <button type="button" class="btn {{ action.get('class', 'btn-secondary') }}"
              {% if action.get('onclick') %}onclick="{{ action.onclick }}"{% endif %}>
        {% if action.get('icon') %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </button>
      {% else %}
      <a href="{{ action.url }}" class="btn {{ action.get('class', 'btn-secondary') }}">
        {% if action.get('icon') %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </a>
      {% endif %}
      {% endfor %}
    </div>
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 步骤表单 -->
{% macro step_form(steps, current_step=1) %}
<div class="step-form">
  <!-- 步骤指示器 -->
  <div class="step-indicator mb-4">
    <div class="d-flex justify-content-between">
      {% for step in steps %}
      <div class="step-item {% if loop.index <= current_step %}active{% endif %} {% if loop.index < current_step %}completed{% endif %}">
        <div class="step-circle">
          {% if loop.index < current_step %}
          <i class="fas fa-check"></i>
          {% else %}
          {{ loop.index }}
          {% endif %}
        </div>
        <div class="step-label">{{ step.title }}</div>
      </div>
      {% if not loop.last %}
      <div class="step-connector {% if loop.index < current_step %}completed{% endif %}"></div>
      {% endif %}
      {% endfor %}
    </div>
  </div>

  <!-- 步骤内容 -->
  <div class="step-content">
    {% for step in steps %}
    <div class="step-pane {% if loop.index == current_step %}active{% endif %}"
         id="step-{{ loop.index }}">
      {{ step.content }}
    </div>
    {% endfor %}
  </div>
</div>

<style nonce="{{ csp_nonce }}">
.step-indicator {
  position: relative;
}

.step-item {
  text-align: center;
  flex: 1;
  position: relative;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.step-item.active .step-circle {
  background: var(--bs-primary);
  color: white;
}

.step-item.completed .step-circle {
  background: var(--bs-success);
  color: white;
}

.step-connector {
  position: absolute;
  top: 20px;
  left: 50%;
  right: -50%;
  height: 2px;
  background: #e9ecef;
  z-index: -1;
}

.step-connector.completed {
  background: var(--bs-success);
}

.step-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.step-item.active .step-label {
  color: var(--bs-primary);
  font-weight: 600;
}

.step-pane {
  display: none;
}

.step-pane.active {
  display: block;
}
</style>
{% endmacro %}

<!-- 输入组字段 -->
{% macro input_group_field(field, prepend=None, append=None, help_text=None, required=False) %}
<div class="mb-3">
  {% if field.label %}
  <label for="{{ field.id }}" class="form-label {% if required %}required{% endif %}">
    {{ field.label.text }}
    {% if required %}<span class="text-danger">*</span>{% endif %}
  </label>
  {% endif %}
  
  <div class="input-group">
    {% if prepend %}
    <span class="input-group-text">{{ prepend }}</span>
    {% endif %}
    
    {{ field(class="form-control") }}
    
    {% if append %}
    <span class="input-group-text">{{ append }}</span>
    {% endif %}
  </div>
  
  {% if help_text %}
  <div class="form-text">{{ help_text }}</div>
  {% endif %}
  
  {% if field.errors %}
  {% for error in field.errors %}
  <div class="invalid-feedback d-block">{{ error }}</div>
  {% endfor %}
  {% endif %}
</div>
{% endmacro %}

<!-- 表单卡片容器 -->
{% macro form_card(title, form_content, actions=None, size="normal") %}
<div class="card {% if size == 'small' %}card-sm{% endif %}">
  {% if title %}
  <div class="card-header">
    <h5 class="card-title mb-0">{{ title }}</h5>
  </div>
  {% endif %}
  
  <div class="card-body {% if size == 'small' %}py-3{% endif %}">
    {{ form_content }}
  </div>
  
  {% if actions %}
  <div class="card-footer bg-light">
    <div class="d-flex justify-content-end gap-2">
      {% for action in actions %}
      {% if action.type == 'submit' %}
      <button type="submit" class="btn {{ action.class|default('btn-primary') }}">
        {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </button>
      {% elif action.type == 'button' %}
      <button type="button" class="btn {{ action.class|default('btn-secondary') }}"
              {% if action.onclick %}onclick="{{ action.onclick }}"{% endif %}>
        {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </button>
      {% else %}
      <a href="{{ action.url }}" class="btn {{ action.class|default('btn-secondary') }}">
        {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
        {{ action.text }}
      </a>
      {% endif %}
      {% endfor %}
    </div>
  </div>
  {% endif %}
</div>
{% endmacro %}

<!-- 多列表单布局 -->
{% macro multi_column_form(fields, columns=2) %}
<div class="row g-3">
  {% for field_group in fields %}
  <div class="col-md-{{ 12 // columns }}">
    {% if field_group.type == 'floating' %}
    {{ floating_field(
      field=field_group.field,
      placeholder=field_group.placeholder,
      help_text=field_group.help_text,
      required=field_group.required
    ) }}
    {% elif field_group.type == 'input_group' %}
    {{ input_group_field(
      field=field_group.field,
      prepend=field_group.prepend,
      append=field_group.append,
      help_text=field_group.help_text,
      required=field_group.required
    ) }}
    {% else %}
    {{ form_field(
      field=field_group.field,
      label=field_group.label,
      help_text=field_group.help_text,
      required=field_group.required,
      size=field_group.size
    ) }}
    {% endif %}
  </div>
  {% endfor %}
</div>
{% endmacro %}

<!-- 搜索表单 -->
{% macro search_form(action, fields=None, method="GET") %}
<form method="{{ method }}" action="{{ action }}" class="mb-3">
  <div class="card border-0 bg-light">
    <div class="card-body py-3">
      <div class="row g-3 align-items-end">
        <!-- 搜索框 -->
        <div class="col-md-4">
          <div class="input-group">
            <input type="text" class="form-control" name="search" 
                   placeholder="搜索..." value="{{ request.args.get('search', '') }}">
            <button class="btn btn-outline-secondary" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        
        <!-- 自定义字段 -->
        {% if fields %}
        {% for field in fields %}
        <div class="col-md-{{ field.width|default('2') }}">
          {% if field.type == 'select' %}
          <select class="form-select form-select-sm" name="{{ field.name }}">
            <option value="">{{ field.label }}</option>
            {% for option in field.options %}
            <option value="{{ option.value }}" 
                    {% if request.args.get(field.name) == option.value %}selected{% endif %}>
              {{ option.text }}
            </option>
            {% endfor %}
          </select>
          {% elif field.type == 'date' %}
          <input type="date" class="form-control form-control-sm" 
                 name="{{ field.name }}" value="{{ request.args.get(field.name, '') }}"
                 placeholder="{{ field.label }}">
          {% else %}
          <input type="text" class="form-control form-control-sm" 
                 name="{{ field.name }}" value="{{ request.args.get(field.name, '') }}"
                 placeholder="{{ field.label }}">
          {% endif %}
        </div>
        {% endfor %}
        {% endif %}
        
        <!-- 操作按钮 -->
        <div class="col-md-auto">
          <div class="btn-group">
            <button type="submit" class="btn btn-primary btn-sm">
              <i class="fas fa-filter me-1"></i>筛选
            </button>
            <a href="{{ request.url_root }}{{ request.endpoint }}" 
               class="btn btn-outline-secondary btn-sm">
              <i class="fas fa-times me-1"></i>清除
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
{% endmacro %}

<!-- 快速操作表单 -->
{% macro quick_action_form(actions) %}
<div class="btn-toolbar gap-2 mb-3">
  {% for action in actions %}
  {% if action.type == 'form' %}
  <form method="{{ action.method|default('POST') }}" action="{{ action.url }}" class="d-inline">
    {{ action.csrf_token() if action.csrf_token }}
    <button type="submit" class="btn {{ action.class|default('btn-primary') }} btn-sm"
            {% if action.confirm %}onclick="return confirm('{{ action.confirm }}')"{% endif %}>
      {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
      {{ action.text }}
    </button>
  </form>
  {% else %}
  <a href="{{ action.url }}" class="btn {{ action.class|default('btn-primary') }} btn-sm"
     {% if action.confirm %}onclick="return confirm('{{ action.confirm }}')"{% endif %}>
    {% if action.icon %}<i class="{{ action.icon }} me-1"></i>{% endif %}
    {{ action.text }}
  </a>
  {% endif %}
  {% endfor %}
</div>
{% endmacro %}

<!-- 表单验证样式 -->
<style>
.form-label.required::after {
  content: " *";
  color: #dc3545;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: .65;
  transform: scale(.85) translateY(-.5rem) translateX(.15rem);
}

.card-sm .card-body {
  padding: 1rem;
}

.card-sm .card-header {
  padding: 0.5rem 1rem;
}

.card-sm .card-footer {
  padding: 0.5rem 1rem;
}
</style>
