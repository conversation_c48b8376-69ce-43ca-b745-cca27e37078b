{% extends 'base.html' %}

{% block title %}用户管理 - {{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h2>用户管理</h2>
    </div>
    <div class="col-12 mt-2">
        <div class="action-buttons d-md-none">
            <a href="{{ url_for('system.add_user') }}" class="btn btn-primary w-100">
                <i class="fas fa-plus"></i> 添加用户
            </a>
            <a href="{{ url_for('system.roles') }}" class="btn btn-info w-100">
                <i class="fas fa-users-cog"></i> 角色管理
            </a>
        </div>
        <div class="d-none d-md-block text-end">
            <a href="{{ url_for('system.add_user') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加用户
            </a>
            <a href="{{ url_for('system.roles') }}" class="btn btn-info">
                <i class="fas fa-users-cog"></i> 角色管理
            </a>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">批量操作</h5>
                    <div>
                        <button id="selectAllBtn" class="btn btn-sm btn-outline-secondary">全选</button>
                        <button id="deselectAllBtn" class="btn btn-sm btn-outline-secondary">取消全选</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 col-md-8">
                        <div class="mb-3">
                            <label for="batchAction">选择操作：</label>
                            <select id="batchAction" class="form-control">
                                <option value="">-- 请选择操作 --</option>
                                <option value="assign_roles">批量分配角色</option>
                                {% if current_user.is_admin() or current_user.has_permission('user', 'delete') %}
                                <option value="delete">批量删除用户</option>
                                {% endif %}
                            </select>
                        </div>
                    </div>
                    <div class="col-12 col-md-4 d-flex align-items-end">
                        <button id="applyBatchAction" class="btn btn-primary w-100 btn-md-auto" disabled>
                            <i class="fas fa-check"></i> 应用操作
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <!-- 桌面端表格视图 -->
        <div class="table-responsive d-none d-lg-block">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="40px">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="selectAll">
                                <label class="form-check-label" for="selectAll"></label>
                            </div>
                        </th>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>真实姓名</th>
                        <th>电子邮箱</th>
                        <th>所属区域</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <div class="form-check">
                                <input type="checkbox" class="custom-control-input user-checkbox" id="user-{{ user.id }}" value="{{ user.id }}">
                                <label class="form-check-label" for="user-{{ user.id }}"></label>
                            </div>
                        </td>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.real_name or '-' }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.area %}
                            <span class="badge badge-info">{{ user.area.get_level_name() }}</span>
                            {{ user.area.name }}
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                        <td>
                            {% for role in user.roles %}
                            <span class="badge badge-primary">{{ role.name }}</span>
                            {% else %}
                            <span class="text-muted">无角色</span>
                            {% endfor %}
                        </td>
                        <td>
                            {% if user.status == 1 %}
                            <span class="badge badge-success">启用</span>
                            {% else %}
                            <span class="badge badge-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('system.view_user', id=user.id) }}" class="btn btn-info" title="查看">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('system.edit_user', id=user.id) }}" class="btn btn-primary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if current_user.is_admin() or current_user.has_permission('user', 'delete') %}
                                <button type="button" class="btn btn-danger" title="删除"
                                        data-action="delete-confirm" data-function="confirmDeleteUser({{ user.id }}, '{{ user.username }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center">暂无用户数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 移动端卡片视图 -->
        <div class="d-lg-none">
            {% for user in users.items %}
            <div class="card mb-3 border-start-primary">
                <div class="card-body">
                    <div class="row">
                        <div class="col-2">
                            <div class="form-check">
                                <input type="checkbox" class="custom-control-input user-checkbox" id="user-mobile-{{ user.id }}" value="{{ user.id }}">
                                <label class="form-check-label" for="user-mobile-{{ user.id }}"></label>
                            </div>
                        </div>
                        <div class="col-10">
                            <h6 class="card-title mb-1">{{ user.username }}</h6>
                            <p class="card-text text-muted small mb-2">ID: {{ user.id }}</p>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">真实姓名</small>
                            <div>{{ user.real_name or '-' }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">状态</small>
                            <div>
                                {% if user.status == 1 %}
                                <span class="badge badge-success">启用</span>
                                {% else %}
                                <span class="badge badge-danger">禁用</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted">电子邮箱</small>
                            <div class="text-truncate">{{ user.email }}</div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted">所属区域</small>
                            <div>
                                {% if user.area %}
                                <span class="badge badge-info">{{ user.area.get_level_name() }}</span>
                                {{ user.area.name }}
                                {% else %}
                                <span class="text-muted">未设置</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted">角色</small>
                            <div>
                                {% for role in user.roles %}
                                <span class="badge badge-primary">{{ role.name }}</span>
                                {% else %}
                                <span class="text-muted">无角色</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="btn-group btn-group-sm w-100">
                                <a href="{{ url_for('system.view_user', id=user.id) }}" class="btn btn-info">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                                <a href="{{ url_for('system.edit_user', id=user.id) }}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                {% if current_user.is_admin() or current_user.has_permission('user', 'delete') %}
                                <button type="button" class="btn btn-danger" data-onclick="confirmDeleteUser({{ user.id }}, '{{ user.username }}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted">暂无用户数据</p>
            </div>
            {% endfor %}
        </div>
    </div>
    {% if users.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not users.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('system.users', page=users.prev_num) if users.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in users.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == users.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('system.users', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not users.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('system.users', page=users.next_num) if users.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
<!-- 删除用户确认对话框 -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteUserModalLabel">确认删除用户</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除用户 <strong id="deleteUserName"></strong> 吗？此操作不可逆！</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 警告：删除用户将同时删除与该用户相关的所有数据和权限设置。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteUserForm" action="" method="post" novalidate novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除用户确认对话框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" role="dialog" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="batchDeleteModalLabel">确认批量删除用户</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除选中的 <strong id="selectedUserCount"></strong> 个用户吗？此操作不可逆！</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 警告：删除用户将同时删除与这些用户相关的所有数据和权限设置。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="batchDeleteForm" action="{{ url_for('system.batch_delete_users') }}" method="post" novalidate novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="user_ids" id="batchDeleteUserIds">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 批量分配角色对话框 -->
<div class="modal fade" id="batchAssignRolesModal" tabindex="-1" role="dialog" aria-labelledby="batchAssignRolesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="batchAssignRolesModalLabel">批量分配角色</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您正在为 <strong id="selectedUserCountForRoles"></strong> 个用户分配角色</p>

                <div class="mb-3">
                    <label>选择角色：</label>
                    <div class="row">
                        {% for role in roles %}
                        <div class="col-md-4">
                            <div class="form-check">
                                <input type="checkbox" class="custom-control-input role-checkbox" id="role-{{ role.id }}" value="{{ role.id }}">
                                <label class="form-check-label" for="role-{{ role.id }}">{{ role.name }}</label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <div class="mb-3">
                    <label>操作方式：</label>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="roleActionAdd" name="roleAction" class="form-check-input" value="add" checked>
                        <label class="form-check-label" for="roleActionAdd">添加所选角色（保留用户现有角色）</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="roleActionReplace" name="roleAction" class="form-check-input" value="replace">
                        <label class="form-check-label" for="roleActionReplace">替换为所选角色（删除用户现有角色）</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="batchAssignRolesForm" action="{{ url_for('system.batch_assign_roles') }}" method="post" novalidate novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="user_ids" id="batchAssignRolesUserIds">
                    <input type="hidden" name="role_ids" id="batchAssignRolesRoleIds">
                    <input type="hidden" name="role_action" id="batchAssignRolesAction">
                    <button type="submit" class="btn btn-primary">确认分配</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // 全选/取消全选功能
        const selectAllCheckbox = document.getElementById('selectAll');
        const userCheckboxes = document.querySelectorAll('.user-checkbox');
        const applyBatchActionBtn = document.getElementById('applyBatchAction');
        const batchActionSelect = document.getElementById('batchAction');

        // 全选按钮
        document.getElementById('selectAllBtn').addEventListener('click', function() {
            selectAllCheckbox.checked = true;
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateApplyButtonState();
        });

        // 取消全选按钮
        document.getElementById('deselectAllBtn').addEventListener('click', function() {
            selectAllCheckbox.checked = false;
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateApplyButtonState();
        });

        // 表头复选框
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateApplyButtonState();
        });

        // 单个用户复选框
        userCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectAllCheckboxState();
                updateApplyButtonState();
            });
        });

        // 批量操作下拉框
        batchActionSelect.addEventListener('change', function() {
            updateApplyButtonState();
        });

        // 应用批量操作按钮
        applyBatchActionBtn.addEventListener('click', function() {
            const action = batchActionSelect.value;
            const selectedUserIds = getSelectedUserIds();

            if (action === 'delete') {
                // 批量删除
                document.getElementById('selectedUserCount').textContent = selectedUserIds.length;
                document.getElementById('batchDeleteUserIds').value = selectedUserIds.join(',');
                $('#batchDeleteModal').modal('show');
            } else if (action === 'assign_roles') {
                // 批量分配角色
                document.getElementById('selectedUserCountForRoles').textContent = selectedUserIds.length;
                document.getElementById('batchAssignRolesUserIds').value = selectedUserIds.join(',');
                $('#batchAssignRolesModal').modal('show');
            }
        });

        // 批量分配角色对话框中的提交按钮
        document.getElementById('batchAssignRolesForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取选中的角色
            const selectedRoleIds = [];
            document.querySelectorAll('.role-checkbox:checked').forEach(checkbox => {
                selectedRoleIds.push(checkbox.value);
            });

            if (selectedRoleIds.length === 0) {
                alert('请至少选择一个角色');
                return;
            }

            // 获取操作方式
            const roleAction = document.querySelector('input[name="roleAction"]:checked').value;

            // 设置表单值
            document.getElementById('batchAssignRolesRoleIds').value = selectedRoleIds.join(',');
            document.getElementById('batchAssignRolesAction').value = roleAction;

            // 提交表单
            this.submit();
        });

        // 更新全选复选框状态
        function updateSelectAllCheckboxState() {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === userCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
        }

        // 更新应用按钮状态
        function updateApplyButtonState() {
            const hasSelectedUsers = document.querySelectorAll('.user-checkbox:checked').length > 0;
            const hasSelectedAction = batchActionSelect.value !== '';
            applyBatchActionBtn.disabled = !(hasSelectedUsers && hasSelectedAction);
        }

        // 获取选中的用户ID
        function getSelectedUserIds() {
            const selectedIds = [];
            document.querySelectorAll('.user-checkbox:checked').forEach(checkbox => {
                selectedIds.push(checkbox.value);
            });
            return selectedIds;
        }

        // 单个用户删除确认
        window.confirmDeleteUser = function(userId, username) {
            document.getElementById('deleteUserName').textContent = username;
            document.getElementById('deleteUserForm').action = "{{ url_for('system.delete_user', id=0) }}".replace('0', userId);
            $('#deleteUserModal').modal('show');
        };
    });
</script>

{% endblock %}