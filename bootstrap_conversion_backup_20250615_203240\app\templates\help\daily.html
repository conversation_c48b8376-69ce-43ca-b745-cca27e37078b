{% extends "base.html" %}

{% block title %}日常管理帮助{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('help.index') }}">帮助中心</a></li>
            <li class="breadcrumb-item active">日常管理</li>
        </ol>
    </nav>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h1><i class="fas fa-calendar-day"></i> 日常管理帮助</h1>
                    <p class="lead">食堂日常运营管理功能指南</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-utensils"></i> 每日菜单管理</h5>
                </div>
                <div class="card-body">
                    <p>管理每日菜单安排和营养搭配。</p>
                    <ul>
                        <li>菜单计划制定</li>
                        <li>营养成分分析</li>
                        <li>成本核算</li>
                        <li>菜单发布</li>
                    </ul>
                    <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 菜单管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-search"></i> 食品安全检查</h5>
                </div>
                <div class="card-body">
                    <p>食品安全检查和质量监控。</p>
                    <ul>
                        <li>入库检查</li>
                        <li>加工过程监控</li>
                        <li>成品检查</li>
                        <li>检查记录管理</li>
                    </ul>
                    <a href="{{ url_for('inspection.index') }}" class="btn btn-warning">
                        <i class="fas fa-external-link-alt"></i> 检查管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-camera"></i> 留样管理</h5>
                </div>
                <div class="card-body">
                    <p>食品留样记录和管理。</p>
                    <ul>
                        <li>留样记录</li>
                        <li>样品保存</li>
                        <li>留样查询</li>
                        <li>处置记录</li>
                    </ul>
                    <a href="{{ url_for('food_sample.index') }}" class="btn btn-info">
                        <i class="fas fa-external-link-alt"></i> 留样记录
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-users"></i> 员工管理</h5>
                </div>
                <div class="card-body">
                    <p>员工信息和健康证管理。</p>
                    <ul>
                        <li>员工档案</li>
                        <li>健康证管理</li>
                        <li>培训记录</li>
                        <li>考勤管理</li>
                    </ul>
                    <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">
                        <i class="fas fa-external-link-alt"></i> 员工管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回帮助中心 -->
    <div class="row">
        <div class="col-12 text-center">
            <a href="{{ url_for('help.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> 返回帮助中心
            </a>
        </div>
    </div>
</div>
{% endblock %}
