/* 侧边栏布局系统 - 左侧固定导航+右侧内容区 */

/* === 基础布局结构 === */
.app-wrapper {
  display: flex;
  min-height: 100vh;
  position: relative;
}

/* 左侧固定导航栏 */
.sidebar {
  width: 280px;
  min-height: 100vh;
  background: var(--theme-primary, #2563eb);
  color: white;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: transform 0.3s ease;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.sidebar-brand:hover {
  color: white;
  text-decoration: none;
}

.sidebar-logo {
  height: 40px;
  max-width: 120px;
  object-fit: contain;
  margin-right: 12px;
}

.sidebar-brand-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-school-name {
  font-size: 0.85rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: inline-block;
}

/* 侧边栏导航菜单 */
.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav-item {
  margin-bottom: 2px;
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  font-size: 14px;
}

.sidebar-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  border-left-color: rgba(255, 255, 255, 0.5);
}

.sidebar-nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-left-color: white;
  font-weight: 500;
}

.sidebar-nav-link i {
  width: 20px;
  margin-right: 12px;
  text-align: center;
  font-size: 16px;
}

/* 下拉菜单 */
.sidebar-dropdown {
  position: relative;
}

.sidebar-dropdown-toggle {
  position: relative;
}

.sidebar-dropdown-toggle::after {
  content: '\f107';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 20px;
  transition: transform 0.3s ease;
}

.sidebar-dropdown.show .sidebar-dropdown-toggle::after {
  transform: rotate(180deg);
}

.sidebar-dropdown-menu {
  background: rgba(0, 0, 0, 0.2);
  margin: 0;
  padding: 0;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
}

.sidebar-dropdown.show .sidebar-dropdown-menu {
  max-height: 500px;
}

.sidebar-dropdown-item {
  display: block;
  padding: 10px 20px 10px 52px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 13px;
  transition: all 0.3s ease;
}

.sidebar-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
}

.sidebar-dropdown-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 500;
}

/* 右侧主内容区 */
.main-content {
  flex: 1;
  margin-left: 280px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

/* 顶部工具栏 */
.top-toolbar {
  background: white;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 999;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 18px;
  color: #6c757d;
  margin-right: 15px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: none; /* 默认隐藏，移动端显示 */
}

.sidebar-toggle:hover {
  background: #f8f9fa;
  color: #495057;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-x: auto;
}

/* 页脚 */
.app-footer {
  background: white;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
}

/* === 移动端适配 === */
@media (max-width: 768px) {
  /* 侧边栏在移动端默认隐藏 */
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  /* 主内容区在移动端占满宽度 */
  .main-content {
    margin-left: 0;
  }
  
  /* 显示侧边栏切换按钮 */
  .sidebar-toggle {
    display: inline-block !important;
  }
  
  /* 移动端内容区域调整 */
  .content-area {
    padding: 1rem;
  }
  
  /* 侧边栏遮罩层 */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
  }
  
  .sidebar-overlay.show {
    display: block;
  }
}

/* === 主题适配 === */
[data-theme="primary"] .sidebar {
  background: var(--theme-primary, #2563eb);
}

[data-theme="secondary"] .sidebar {
  background: var(--theme-secondary, #6c757d);
}

[data-theme="success"] .sidebar {
  background: var(--theme-success, #198754);
}

[data-theme="warning"] .sidebar {
  background: var(--theme-warning, #fd7e14);
}

[data-theme="info"] .sidebar {
  background: var(--theme-info, #0dcaf0);
}

[data-theme="danger"] .sidebar {
  background: var(--theme-danger, #dc3545);
}

/* === 动画效果 === */
.sidebar-nav-link,
.sidebar-dropdown-item {
  position: relative;
  overflow: hidden;
}

.sidebar-nav-link::before,
.sidebar-dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.sidebar-nav-link:hover::before,
.sidebar-dropdown-item:hover::before {
  left: 100%;
}

/* === 滚动条样式 === */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* === 响应式断点优化 === */
@media (max-width: 576px) {
  .sidebar {
    width: 100%;
  }
  
  .content-area {
    padding: 0.75rem;
  }
  
  .top-toolbar {
    padding: 0.75rem 1rem;
  }
}

@media (min-width: 1200px) {
  .sidebar {
    width: 320px;
  }
  
  .main-content {
    margin-left: 320px;
  }
}
