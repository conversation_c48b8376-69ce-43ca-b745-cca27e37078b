{% extends "base.html" %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 表头样式重置和强制显示 */
    #dataTable thead th {
        background-color: #e9ecef !important;
        background-image: none !important;
        color: #212529 !important;
        font-weight: 600 !important;
        font-size: 13px !important;
        text-align: center !important;
        border-bottom: 2px solid #dee2e6 !important;
        padding: 12px 8px !important;
        vertical-align: middle !important;
    }

    /* 表格按钮优化 */
    .table-actions {
        white-space: nowrap;
    }

    .table-actions a,
    .table-actions button {
        display: inline-block;
        padding: 3px 8px;
        margin: 0 2px;
        font-size: 12px;
        line-height: 1.4;
        border: none;
        border-radius: 3px;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .table-actions .btn-info {
        background-color: #17a2b8;
        color: white;
    }

    .table-actions .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .table-actions .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .table-actions .btn-success {
        background-color: #28a745;
        color: white;
    }

    .table-actions .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }

    .table-actions a:hover,
    .table-actions button:hover {
        opacity: 0.8;
        transform: translateY(-1px);
        text-decoration: none;
        color: inherit;
    }

    /* 状态列优化 */
    .status-column {
        text-align: center;
    }

    .badge {
        font-size: 11px;
        padding: 3px 6px;
    }

    /* 表格列宽优化 */
    .table th,
    .table td {
        vertical-align: middle;
        padding: 8px;
        font-size: 13px;
    }

    .table tbody td {
        background-color: #fff;
        border-top: 1px solid #dee2e6;
    }

    .table tbody tr:hover td {
        background-color: #f8f9fa;
    }

    .id-column {
        width: 60px;
        text-align: center;
    }
    .week-column {
        width: 200px;
        font-weight: 500;
    }
    .status-column {
        width: 80px;
        text-align: center;
    }
    .creator-column {
        width: 100px;
        font-size: 12px;
    }
    .time-column {
        width: 120px;
        font-size: 12px;
        color: #6c757d;
    }
    .actions-column {
        width: 300px;
        min-width: 300px;
    }

    /* 响应式优化 */
    @d-flex (max-width: 768px) {
        .table-actions a,
        .table-actions button {
            font-size: 11px;
            padding: 1px 6px;
            margin: 0 1px;
        }

        .creator-column,
        .time-column {
            font-size: 11px;
        }

        .actions-column {
            min-width: 250px;
        }
    }

    /* 表格整体优化 */
    .table-responsive {
        border-radius: 6px;
        overflow: hidden;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background-color: #e9ecef !important;
        background-image: none !important;
        border-bottom: 2px solid #dee2e6 !important;
        font-weight: 600 !important;
        font-size: 13px !important;
        color: #212529 !important;
        text-align: center !important;
        vertical-align: middle !important;
        position: relative;
        z-index: 1;
    }

    /* 确保表头文字可见 */
    .table thead th::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e9ecef;
        z-index: -1;
    }
</style>
{% endblock %}

{% block content %}
<div class=\"container-fluid'>
    <div class='d-sm-flex align-items-center justify-content-between mb-4\">
        <h1 class=\"h3 mb-0 text-gray-800'>{{ title }}</h1>
        <a href='{{ url_for('weekly_menu_v2.plan') }}\" class=\"d-none d-sm-inline-block btn btn-primary shadow-sm'>
            <i class='fas fa-plus fa-sm text-white-50\"></i> 新建周菜单
        </a>
    </div>

    <div class=\"card shadow mb-4'>
        <div class='card-header py-3\">
            <h6 class=\"m-0 fw-bold text-primary'>周菜单列表</h6>
        </div>
        <div class='card-body\">
            <!-- 筛选表单 -->
            <form method="GET" action="{{ url_for('weekly_menu_v2.index') }}" class=\"mb-4'>
                <div class='row\">
                    <div class=\"col-md-3 mb-2'>
                        <select name='status\" class=\"form-control'>
                            <option value='\">-- 状态 --</option>
                            <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
                            <option value="已发布" {% if status == '已发布' %}selected{% endif %}>已发布</option>
                        </select>
                    </div>
                    <div class=\"col-md-3 mb-2'>
                        <input type='text\" name="start_date" class=\"form-control datepicker' placeholder='开始日期\" value="{{ start_date }}">
                    </div>
                    <div class=\"col-md-3 mb-2'>
                        <input type='text\" name="end_date" class=\"form-control datepicker' placeholder='结束日期\" value="{{ end_date }}">
                    </div>
                    <div class=\"col-md-2 mb-2'>
                        <input type='text\" name="week" class=\"form-control' placeholder='周次 (如: 2023-W20)\" value="{{ week }}">
                    </div>
                    <div class=\"col-md-1 mb-2'>
                        <button type='submit\" class=\"btn btn-primary w-100'>筛选</button>
                    </div>
                </div>
            </form>

            <div class='table-responsive\">
                <table class=\"table table-bordered' id='dataTable\" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th class=\"id-column'>ID</th>
                            <th class='week-column\">周次</th>
                            <th class=\"status-column'>状态</th>
                            <th class='creator-column\">创建者</th>
                            <th class=\"time-column'>创建时间</th>
                            <th class='actions-column\">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for menu in weekly_menus.items %}
                            <tr>
                                <td class=\"id-column'>{{ menu.id }}</td>
                                <td class='week-column\">{{ menu.week_display }}</td>
                                <td class=\"status-column'>
                                    <span class='badge badge-{{ menu.status|status_class }}\">{{ menu.status }}</span>
                                </td>
                                <td class=\"creator-column'>{{ menu.creator.name if menu.creator else '未知' }}</td>
                                <td class='time-column\">{{ menu.created_at|format_datetime('%m-%d %H:%M') }}</td>
                                <td class=\"actions-column'>
                                    <div class='table-actions\">
                                        <a href="{{ url_for('weekly_menu_v2.view', id=menu.id) }}" class=\"btn-info'>查看</a>
                                        <a href='{{ url_for('weekly_menu_v2.print_menu', id=menu.id) }}\" class=\"btn-secondary' target='_blank\" title="打印菜单">打印</a>
                                        {% if menu.status == '计划中' %}
                                            <a href="{{ url_for('weekly_menu_v2.plan', area_id=menu.area_id, week_start=menu.week_start) }}" class=\"btn-primary'>编辑</a>
                                            <form method='POST\" action="{{ url_for('weekly_menu_v2.publish', id=menu.id) }}" style=\"display: inline;'>
                                                <input type='hidden\" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="button" class=\"btn-success' onclick='if(confirm('确定要发布这个周菜单吗？发布后将无法修改。')) { this.closest('form').submit(); }\">发布</button>
                                            </form>
                                        {% endif %}
                                        {% if menu.status == '已发布' %}
                                            <a href="{{ url_for('purchase_order.create_from_menu', weekly_menu_id=menu.id) }}" class=\"btn-success'>采购</a>
                                            <form method='POST\" action="{{ url_for('weekly_menu_v2.unpublish', id=menu.id) }}" style=\"display: inline;'>
                                                <input type='hidden\" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="button" class=\"btn-warning' onclick='if(confirm('确定要解除发布这个周菜单吗？解除后可以重新编辑。')) { this.closest('form').submit(); }\">解除发布</button>
                                            </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="6" class=\"text-center'>没有找到符合条件的周菜单</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <nav aria-label='Page navigation\">
                <ul class=\"pagination justify-content-center'>
                    {% if weekly_menus.has_prev %}
                        <li class='page-item\">
                            <a class=\"page-link' href='{{ url_for('weekly_menu_v2.index', page=weekly_menus.prev_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}\" style=\"cursor: pointer;'>上一页</a>
                        </li>
                    {% else %}
                        <li class='page-item disabled\">
                            <span class=\"page-link'>上一页</span>
                        </li>
                    {% endif %}

                    {% for page_num in weekly_menus.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page_num %}
                            {% if page_num == weekly_menus.page %}
                                <li class='page-item active\">
                                    <span class=\"page-link'>{{ page_num }}</span>
                                </li>
                            {% else %}
                                <li class='page-item\">
                                    <a class=\"page-link' href='{{ url_for('weekly_menu_v2.index', page=page_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}\">{{ page_num }}</a>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class=\"page-item disabled'>
                                <span class='page-link\">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if weekly_menus.has_next %}
                        <li class=\"page-item'>
                            <a class='page-link\" href="{{ url_for('weekly_menu_v2.index', page=weekly_menus.next_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">下一页</a>
                        </li>
                    {% else %}
                        <li class=\"page-item disabled'>
                            <span class='page-link\">下一页</span>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化日期选择器
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            language: 'zh-CN'
        });
    });
</script>

{% endblock %}
