{% extends 'base.html' %}

{% block title %}扫码上传入口{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .scan-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s;
        margin-bottom: 30px;
        border: none;
    }

    .scan-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }

    .scan-card .card-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        font-weight: bold;
        padding: 20px;
        border: none;
    }

    .scan-card .card-body {
        padding: 30px;
    }

    .scan-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #4e73df;
    }

    .scan-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 15px;
    }

    .scan-description {
        color: #6c757d;
        margin-bottom: 25px;
    }

    .scan-btn {
        padding: 12px 25px;
        font-weight: bold;
        border-radius: 50px;
        transition: all 0.3s;
    }

    .scan-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .qr-code-container {
        margin: 20px 0;
        text-align: center;
    }

    .qr-code-img {
        max-width: 200px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .section-title {
        border-start: 5px solid #4e73df;
        padding-left: 15px;
        margin-bottom: 30px;
        font-weight: bold;
    }

    .platform-name {
        text-align: center;
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 30px;
        color: #4e73df;
    }

    .school-name {
        text-align: center;
        font-size: 1.5rem;
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">扫码上传入口</h1>
        <a href="{{ url_for('daily_management.index') }}" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> 返回日常管理
        </a>
    </div>

    <!-- 平台名称和学校名称 -->
    <div class="platform-name">校园餐智慧食堂</div>
    <div class="school-name">{{ school.name }}</div>

    <!-- 扫码上传卡片 -->
    <div class="row">
        <div class="col-12">
            <h4 class="section-title">今日检查记录</h4>
        </div>

        <!-- 晨检 -->
        <div class="col-lg-4">
            <div class="card scan-card">
                <div class="card-header">
                    <h5 class="m-0">晨检记录</h5>
                </div>
                <div class="card-body text-center">
                    <div class="scan-icon">
                        <i class="fas fa-sun"></i>
                    </div>
                    <div class="scan-title">晨检照片上传</div>
                    <div class="scan-description">
                        上传今日晨检照片，记录食堂晨间卫生和食品安全检查情况
                    </div>

                    <div class="qr-code-container">
                        <img src="data:image/png;base64,{{ morning_qrcode }}" alt="晨检二维码" class="qr-code-img">
                    </div>

                    <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='morning') }}" class="btn btn-primary scan-btn">
                        <i class="fas fa-qrcode me-2"></i> 查看大图
                    </a>
                </div>
            </div>
        </div>

        <!-- 午检 -->
        <div class="col-lg-4">
            <div class="card scan-card">
                <div class="card-header">
                    <h5 class="m-0">午检记录</h5>
                </div>
                <div class="card-body text-center">
                    <div class="scan-icon">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                    <div class="scan-title">午检照片上传</div>
                    <div class="scan-description">
                        上传今日午检照片，记录食堂午间卫生和食品安全检查情况
                    </div>

                    <div class="qr-code-container">
                        <img src="data:image/png;base64,{{ noon_qrcode }}" alt="午检二维码" class="qr-code-img">
                    </div>

                    <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='noon') }}" class="btn btn-primary scan-btn">
                        <i class="fas fa-qrcode me-2"></i> 查看大图
                    </a>
                </div>
            </div>
        </div>

        <!-- 晚检 -->
        <div class="col-lg-4">
            <div class="card scan-card">
                <div class="card-header">
                    <h5 class="m-0">晚检记录</h5>
                </div>
                <div class="card-body text-center">
                    <div class="scan-icon">
                        <i class="fas fa-moon"></i>
                    </div>
                    <div class="scan-title">晚检照片上传</div>
                    <div class="scan-description">
                        上传今日晚检照片，记录食堂晚间卫生和食品安全检查情况
                    </div>

                    <div class="qr-code-container">
                        <img src="data:image/png;base64,{{ evening_qrcode }}" alt="晚检二维码" class="qr-code-img">
                    </div>

                    <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='evening') }}" class="btn btn-primary scan-btn">
                        <i class="fas fa-qrcode me-2"></i> 查看大图
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">扫码上传使用说明</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                                <h5>第一步：扫描二维码</h5>
                                <p>使用手机相机或微信扫描上方对应的二维码</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-camera fa-3x text-primary mb-3"></i>
                                <h5>第二步：拍摄上传照片</h5>
                                <p>直接拍摄或选择照片上传，支持多张照片</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-check-circle fa-3x text-primary mb-3"></i>
                                <h5>第三步：完成上传</h5>
                                <p>照片上传成功后，系统会自动关联到对应的检查记录</p>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> 提示：您也可以将二维码保存到手机或打印出来，方便随时使用。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
