<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>食堂日常管理日志打印</title>
    <style nonce="{{ csp_nonce }}">
        @d-flex print {
            @page {
                size: A4;
                margin: 1cm;
            }
            body {
                font-family: "SimSun", "宋体", serif;
                line-height: 1.5;
                color: #000;
                background: #fff;
                margin: 0;
                padding: 0;
                font-size: 12pt;
            }
            .container {
                width: 100%;
                max-width: 210mm;
                margin: 0 auto;
                padding: 0;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #000;
            }
            .header h1 {
                font-size: 24pt;
                font-weight: bold;
                margin: 0;
                padding: 10px 0;
            }
            .header p {
                font-size: 12pt;
                margin: 5px 0;
            }
            .section {
                margin-bottom: 20px;
            }
            .section-title {
                font-size: 16pt;
                font-weight: bold;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #000;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
            .info-item {
                margin-bottom: 10px;
            }
            .info-label {
                font-weight: bold;
                display: inline-block;
                min-width: 80px;
            }
            .info-value {
                display: inline-block;
            }
            .text-box {
                border: 1px solid #ccc;
                padding: 10px;
                margin-top: 5px;
                min-height: 60px;
                background-color: #f9f9f9;
            }
            .photo-section {
                margin-top: 20px;
            }
            .photo-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
            .photo-item {
                text-align: center;
            }
            .photo-item img {
                max-width: 100%;
                max-height: 200px;
                border: 1px solid #ccc;
                padding: 5px;
                background: white;
            }
            .photo-caption {
                margin-top: 5px;
                font-size: 10pt;
                color: #666;
            }
            .footer {
                margin-top: 30px;
                text-align: right;
                font-size: 10pt;
                color: #666;
            }
            .school-info {
                text-align: center;
                margin-bottom: 10px;
            }
            .school-name {
                font-size: 14pt;
                font-weight: bold;
            }
            .qr-code {
                text-align: center;
                margin-top: 20px;
            }
            .qr-code img {
                width: 100px;
                height: 100px;
            }
            .qr-code-caption {
                font-size: 9pt;
                color: #666;
                margin-top: 5px;
            }
            .no-print {
                display: none;
            }
            .table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            .table th, .table td {
                border: 1px solid #000;
                padding: 8px;
                text-align: left;
            }
            .table th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .signature-section {
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
            }
            .signature-item {
                flex: 1;
                margin: 0 20px;
                text-align: center;
            }
            .signature-line {
                border-bottom: 1px solid #000;
                margin-bottom: 5px;
                height: 40px;
            }
            .page-break {
                page-break-before: always;
            }
        }
        
        /* 非打印样式 */
        body {
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
            line-height: 1.5;
            color: #333;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #333;
        }
        .header h1 {
            font-size: 24pt;
            font-weight: bold;
            margin: 0;
            padding: 10px 0;
        }
        .header p {
            font-size: 12pt;
            margin: 5px 0;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #333;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 80px;
        }
        .info-value {
            display: inline-block;
        }
        .text-box {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 5px;
            min-height: 60px;
            background-color: #f9f9f9;
        }
        .photo-section {
            margin-top: 20px;
        }
        .photo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .photo-item {
            text-align: center;
        }
        .photo-item img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #ccc;
            padding: 5px;
            background: white;
        }
        .photo-caption {
            margin-top: 5px;
            font-size: 10pt;
            color: #666;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10pt;
            color: #666;
        }
        .school-info {
            text-align: center;
            margin-bottom: 10px;
        }
        .school-name {
            font-size: 14pt;
            font-weight: bold;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-item {
            flex: 1;
            margin: 0 20px;
            text-align: center;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
            height: 40px;
        }
        .no-print {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 20px 0;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        .no-print:hover {
            background-color: #45a049;
        }
        .page-break {
            border-top: 1px dashed #ccc;
            margin-top: 30px;
            padding-top: 30px;
        }
        .print-options {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .print-option {
            background-color: #f0f0f0;
            color: #333;
            padding: 10px;
            text-decoration: none;
            border-radius: 4px;
            flex: 1;
            text-align: center;
        }
        .print-option:hover {
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="school-info">
                <div class="school-name">{{ school.name }}</div>
            </div>
            <h1>食堂日常管理日志</h1>
            <p>日期：{{ log.log_date.strftime('%Y年%m月%d日') }}</p>
        </div>

        <div class="print-options no-print">
            <a href="{{ url_for('daily_management.print_inspections', log_id=log.id) }}" class="print-option">
                <i class="fas fa-tasks"></i> 仅打印检查记录
            </a>
            <a href="{{ url_for('daily_management.print_companion_by_id', log_id=log.id) }}" class="print-option">
                <i class="fas fa-utensils"></i> 仅打印陪餐记录
            </a>
            <a href="{{ url_for('daily_management.print_training_by_id', log_id=log.id) }}" class="print-option">
                <i class="fas fa-chalkboard-teacher"></i> 仅打印培训记录
            </a>
        </div>

        <div class="section">
            <div class="section-title">基本信息</div>
            <table class="table">
                <tr>
                    <th width="20%">管理员</th>
                    <td width="30%">{{ log.manager or '未填写' }}</td>
                    <th width="20%">天气</th>
                    <td width="30%">{{ log.weather or '未填写' }}</td>
                </tr>
                <tr>
                    <th>学生就餐人数</th>
                    <td>{{ log.student_count or 0 }} 人</td>
                    <th>教师就餐人数</th>
                    <td>{{ log.teacher_count or 0 }} 人</td>
                </tr>
                <tr>
                    <th>其他就餐人数</th>
                    <td>{{ log.other_count or 0 }} 人</td>
                    <th>总就餐人数</th>
                    <td>{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }} 人</td>
                </tr>
                <tr>
                    <th>食物浪费量</th>
                    <td colspan="3">{{ log.food_waste or 0 }} 千克</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">菜单信息</div>
            <table class="table">
                <tr>
                    <th width="20%">早餐菜单</th>
                    <td>{{ log.breakfast_menu or '未填写' }}</td>
                </tr>
                <tr>
                    <th>午餐菜单</th>
                    <td>{{ log.lunch_menu or '未填写' }}</td>
                </tr>
                <tr>
                    <th>晚餐菜单</th>
                    <td>{{ log.dinner_menu or '未填写' }}</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">运营总结</div>
            <div class="text-box">{{ log.operation_summary or '未填写' }}</div>
        </div>

        <!-- 检查记录部分 -->
        {% if morning_inspections or noon_inspections or evening_inspections %}
        <div class="page-break"></div>
        <div class="section">
            <div class="section-title">检查记录</div>
            
            {% if morning_inspections %}
            <h3>早餐检查记录</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th width="20%">检查项目</th>
                        <th width="15%">状态</th>
                        <th width="50%">描述</th>
                        <th width="15%">检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in morning_inspections %}
                    <tr>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time.strftime('%H:%M') if inspection.inspection_time else '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
            
            {% if noon_inspections %}
            <h3>午餐检查记录</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th width="20%">检查项目</th>
                        <th width="15%">状态</th>
                        <th width="50%">描述</th>
                        <th width="15%">检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in noon_inspections %}
                    <tr>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time.strftime('%H:%M') if inspection.inspection_time else '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
            
            {% if evening_inspections %}
            <h3>晚餐检查记录</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th width="20%">检查项目</th>
                        <th width="15%">状态</th>
                        <th width="50%">描述</th>
                        <th width="15%">检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inspection in evening_inspections %}
                    <tr>
                        <td>{{ inspection.inspection_item }}</td>
                        <td>{{ '正常' if inspection.status == 'normal' else '异常' }}</td>
                        <td>{{ inspection.description or '无' }}</td>
                        <td>{{ inspection.inspection_time.strftime('%H:%M') if inspection.inspection_time else '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        <!-- 陪餐记录部分 -->
        {% if companions %}
        <div class="page-break"></div>
        <div class="section">
            <div class="section-title">陪餐记录</div>
            <table class="table">
                <thead>
                    <tr>
                        <th width="15%">陪餐人</th>
                        <th width="15%">角色</th>
                        <th width="15%">餐次</th>
                        <th width="15%">陪餐时间</th>
                        <th width="10%">口味评分</th>
                        <th width="10%">卫生评分</th>
                        <th width="10%">服务评分</th>
                    </tr>
                </thead>
                <tbody>
                    {% for companion in companions %}
                    <tr>
                        <td>{{ companion.companion_name }}</td>
                        <td>{{ companion.companion_role }}</td>
                        <td>{{ companion.meal_type|replace('breakfast', '早餐')|replace('lunch', '午餐')|replace('dinner', '晚餐') }}</td>
                        <td>{{ companion.dining_time.strftime('%H:%M') if companion.dining_time else '' }}</td>
                        <td>{{ companion.taste_rating or 0 }}</td>
                        <td>{{ companion.hygiene_rating or 0 }}</td>
                        <td>{{ companion.service_rating or 0 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            {% for companion in companions %}
            <div style="margin-top: 20px;">
                <h3>{{ companion.companion_name }} - {{ companion.meal_type|replace('breakfast', '早餐')|replace('lunch', '午餐')|replace('dinner', '晚餐') }}</h3>
                <table class="table">
                    <tr>
                        <th width="20%">评价意见</th>
                        <td>{{ companion.comments or '无' }}</td>
                    </tr>
                    <tr>
                        <th>改进建议</th>
                        <td>{{ companion.suggestions or '无' }}</td>
                    </tr>
                </table>
                
                {% if companion.photo_paths %}
                <div class="photo-section">
                    <h4>陪餐照片</h4>
                    <div class="photo-grid">
                        {% for path in companion.photo_paths.split(';') %}
                        <div class="photo-item">
                            <img src="{{ path }}" alt="陪餐照片">
                            <div class="photo-caption">陪餐照片 #{{ loop.index }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 培训记录部分 -->
        {% if trainings %}
        <div class="page-break"></div>
        <div class="section">
            <div class="section-title">培训记录</div>
            <table class="table">
                <thead>
                    <tr>
                        <th width="30%">培训主题</th>
                        <th width="15%">培训讲师</th>
                        <th width="15%">培训时间</th>
                        <th width="10%">培训时长</th>
                        <th width="10%">参训人数</th>
                    </tr>
                </thead>
                <tbody>
                    {% for training in trainings %}
                    <tr>
                        <td>{{ training.training_topic }}</td>
                        <td>{{ training.trainer }}</td>
                        <td>{{ training.training_time.strftime('%H:%M') if training.training_time else '' }}</td>
                        <td>{{ training.duration or 0 }} 分钟</td>
                        <td>{{ training.attendees_count or 0 }} 人</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            {% for training in trainings %}
            <div style="margin-top: 20px;">
                <h3>{{ training.training_topic }}</h3>
                <table class="table">
                    <tr>
                        <th width="20%">培训内容摘要</th>
                        <td>{{ training.content_summary or '无' }}</td>
                    </tr>
                    <tr>
                        <th>效果评估</th>
                        <td>{{ training.effectiveness_evaluation or '无' }}</td>
                    </tr>
                </table>
                
                {% if training.photo_paths %}
                <div class="photo-section">
                    <h4>培训照片</h4>
                    <div class="photo-grid">
                        {% for path in training.photo_paths.split(';') %}
                        <div class="photo-item">
                            <img src="{{ path }}" alt="培训照片">
                            <div class="photo-caption">培训照片 #{{ loop.index }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 特殊事件部分 -->
        {% if events %}
        <div class="page-break"></div>
        <div class="section">
            <div class="section-title">特殊事件</div>
            <table class="table">
                <thead>
                    <tr>
                        <th width="30%">事件标题</th>
                        <th width="15%">事件类型</th>
                        <th width="15%">发生时间</th>
                        <th width="40%">事件描述</th>
                    </tr>
                </thead>
                <tbody>
                    {% for event in events %}
                    <tr>
                        <td>{{ event.event_title }}</td>
                        <td>{{ event.event_type }}</td>
                        <td>{{ event.event_time.strftime('%H:%M') if event.event_time else '' }}</td>
                        <td>{{ event.event_description or '无' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            {% for event in events %}
            <div style="margin-top: 20px;">
                <h3>{{ event.event_title }}</h3>
                <table class="table">
                    <tr>
                        <th width="20%">事件描述</th>
                        <td>{{ event.event_description or '无' }}</td>
                    </tr>
                    <tr>
                        <th>处理措施</th>
                        <td>{{ event.handling_measures or '无' }}</td>
                    </tr>
                </table>
                
                {% if event.photo_paths %}
                <div class="photo-section">
                    <h4>事件照片</h4>
                    <div class="photo-grid">
                        {% for path in event.photo_paths.split(';') %}
                        <div class="photo-item">
                            <img src="{{ path }}" alt="事件照片">
                            <div class="photo-caption">事件照片 #{{ loop.index }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 问题记录部分 -->
        {% if issues %}
        <div class="page-break"></div>
        <div class="section">
            <div class="section-title">问题记录</div>
            <table class="table">
                <thead>
                    <tr>
                        <th width="30%">问题标题</th>
                        <th width="15%">问题类型</th>
                        <th width="15%">发现时间</th>
                        <th width="15%">状态</th>
                        <th width="25%">问题描述</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in issues %}
                    <tr>
                        <td>{{ issue.issue_title }}</td>
                        <td>{{ issue.issue_type }}</td>
                        <td>{{ issue.found_time.strftime('%H:%M') if issue.found_time else '' }}</td>
                        <td>{{ issue.status }}</td>
                        <td>{{ issue.issue_description or '无' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            {% for issue in issues %}
            <div style="margin-top: 20px;">
                <h3>{{ issue.issue_title }}</h3>
                <table class="table">
                    <tr>
                        <th width="20%">问题描述</th>
                        <td>{{ issue.issue_description or '无' }}</td>
                    </tr>
                    <tr>
                        <th>解决方案</th>
                        <td>{{ issue.solution or '无' }}</td>
                    </tr>
                </table>
                
                {% if issue.photo_paths %}
                <div class="photo-section">
                    <h4>问题照片</h4>
                    <div class="photo-grid">
                        {% for path in issue.photo_paths.split(';') %}
                        <div class="photo-item">
                            <img src="{{ path }}" alt="问题照片">
                            <div class="photo-caption">问题照片 #{{ loop.index }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="page-break"></div>
        <div class="signature-section">
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>食堂管理员签名</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>学校负责人签名</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>监督人签名</div>
            </div>
        </div>

        <div class="footer">
            <p>打印时间：{{ now.strftime('%Y年%m月%d日 %H:%M') }}</p>
            <p>（本文档由系统自动生成，打印后有效）</p>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <button class="no-print" class="print-button">打印此页</button>
        <a class="no-print" href="{{ url_for('daily_management.logs') }}" style="margin-left: 10px; text-decoration: none; color: #333;">返回日志列表</a>
    </div>
</body>
</html>
