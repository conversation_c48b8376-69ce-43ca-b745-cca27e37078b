#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 4 到 Bootstrap 5 超级转换工具
自动化批量转换项目中的Bootstrap类名、属性和结构
"""

import os
import re
import json
import shutil
from datetime import datetime
from pathlib import Path

class Bootstrap4To5SuperConverter:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / f"bootstrap_conversion_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.report = {
            "conversion_time": datetime.now().isoformat(),
            "files_processed": 0,
            "files_modified": 0,
            "total_replacements": 0,
            "replacements_by_type": {},
            "files_with_changes": [],
            "errors": []
        }
        
        # Bootstrap 4 到 5 的类名映射
        self.class_mappings = {
            # 表单控件
            r'\bform-control-file\b': 'form-control',
            r'\bform-control-range\b': 'form-range',
            r'\bform-control-plaintext\b': 'form-control-plaintext',
            r'\bcustom-select\b': 'form-select',
            r'\bcustom-file\b': 'form-control',
            r'\bcustom-file-input\b': 'form-control',
            r'\bcustom-file-label\b': 'form-label',
            r'\bcustom-checkbox\b': 'form-check',
            r'\bcustom-radio\b': 'form-check',
            r'\bcustom-switch\b': 'form-switch',
            r'\bcustom-control\b': 'form-check',
            r'\bcustom-control-input\b': 'form-check-input',
            r'\bcustom-control-label\b': 'form-check-label',
            r'\binput-group-prepend\b': '',  # 移除，Bootstrap 5不需要
            r'\binput-group-append\b': '',   # 移除，Bootstrap 5不需要

            # 按钮变体
            r'\bbtn-outline-default\b': 'btn-outline-secondary',
            r'\bbtn-default\b': 'btn-secondary',
            
            # 按钮和徽章
            r'\bbadge-primary\b': 'bg-primary',
            r'\bbadge-secondary\b': 'bg-secondary',
            r'\bbadge-success\b': 'bg-success',
            r'\bbadge-danger\b': 'bg-danger',
            r'\bbadge-warning\b': 'bg-warning',
            r'\bbadge-info\b': 'bg-info',
            r'\bbadge-light\b': 'bg-light',
            r'\bbadge-dark\b': 'bg-dark',
            r'\bbadge-pill\b': 'rounded-pill',
            
            # 按钮样式
            r'\bbtn-outline-primary\b': 'btn-outline-primary',
            r'\bbtn-outline-secondary\b': 'btn-outline-secondary',
            r'\bbtn-outline-success\b': 'btn-outline-success',
            r'\bbtn-outline-danger\b': 'btn-outline-danger',
            r'\bbtn-outline-warning\b': 'btn-outline-warning',
            r'\bbtn-outline-info\b': 'btn-outline-info',
            r'\bbtn-outline-light\b': 'btn-outline-light',
            r'\bbtn-outline-dark\b': 'btn-outline-dark',
            
            # 文本和背景
            r'\btext-left\b': 'text-start',
            r'\btext-right\b': 'text-end',
            r'\bfloat-left\b': 'float-start',
            r'\bfloat-right\b': 'float-end',
            r'\bborder-left\b': 'border-start',
            r'\bborder-right\b': 'border-end',
            r'\brounded-left\b': 'rounded-start',
            r'\brounded-right\b': 'rounded-end',
            
            # 间距
            r'\bml-(\d+)\b': r'ms-\1',
            r'\bmr-(\d+)\b': r'me-\1',
            r'\bpl-(\d+)\b': r'ps-\1',
            r'\bpr-(\d+)\b': r'pe-\1',
            
            # 网格系统
            r'\bno-gutters\b': 'g-0',
            
            # 模态框
            r'\bmodal-dialog-centered\b': 'modal-dialog modal-dialog-centered',
            
            # 导航
            r'\bnav-pills\b': 'nav-pills',
            r'\bnav-tabs\b': 'nav-tabs',
            
            # 卡片
            r'\bcard-deck\b': 'row row-cols-1 row-cols-md-2 row-cols-lg-3',
            r'\bcard-columns\b': 'row row-cols-1 row-cols-md-2 row-cols-lg-3',
            
            # 工具类
            r'\bsr-only\b': 'visually-hidden',
            r'\bsr-only-focusable\b': 'visually-hidden-focusable',
            
            # 字体权重
            r'\bfont-weight-bold\b': 'fw-bold',
            r'\bfont-weight-bolder\b': 'fw-bolder',
            r'\bfont-weight-normal\b': 'fw-normal',
            r'\bfont-weight-light\b': 'fw-light',
            r'\bfont-weight-lighter\b': 'fw-lighter',
            
            # 字体样式
            r'\bfont-italic\b': 'fst-italic',
            r'\bfont-style-normal\b': 'fst-normal',

            # 显示工具类
            r'\bd-sm-none\b': 'd-sm-none',
            r'\bd-md-none\b': 'd-md-none',
            r'\bd-lg-none\b': 'd-lg-none',
            r'\bd-xl-none\b': 'd-xl-none',

            # 弹性布局
            r'\bjustify-content-around\b': 'justify-content-around',
            r'\bjustify-content-evenly\b': 'justify-content-evenly',

            # 边框工具类
            r'\bborder-top-0\b': 'border-top-0',
            r'\bborder-end-0\b': 'border-end-0',
            r'\bborder-bottom-0\b': 'border-bottom-0',
            r'\bborder-start-0\b': 'border-start-0',

            # 位置工具类
            r'\bfixed-top\b': 'fixed-top',
            r'\bfixed-bottom\b': 'fixed-bottom',
            r'\bsticky-top\b': 'sticky-top',

            # 阴影
            r'\bshadow-sm\b': 'shadow-sm',
            r'\bshadow\b': 'shadow',
            r'\bshadow-lg\b': 'shadow-lg',

            # 溢出
            r'\boverflow-auto\b': 'overflow-auto',
            r'\boverflow-hidden\b': 'overflow-hidden',
        }
        
        # HTML属性映射
        self.attribute_mappings = {
            # 模态框
            r'data-toggle="modal"': 'data-bs-toggle="modal"',
            r'data-target="([^"]*)"': r'data-bs-target="\1"',
            r'data-dismiss="modal"': 'data-bs-dismiss="modal"',
            
            # 下拉菜单
            r'data-toggle="dropdown"': 'data-bs-toggle="dropdown"',
            
            # 折叠
            r'data-toggle="collapse"': 'data-bs-toggle="collapse"',
            
            # 工具提示
            r'data-toggle="tooltip"': 'data-bs-toggle="tooltip"',
            r'data-placement="([^"]*)"': r'data-bs-placement="\1"',
            
            # 弹出框
            r'data-toggle="popover"': 'data-bs-toggle="popover"',
            
            # 标签页
            r'data-toggle="tab"': 'data-bs-toggle="tab"',
            r'data-toggle="pill"': 'data-bs-toggle="pill"',
        }
        
        # 需要结构性修改的模式
        self.structural_patterns = {
            # 关闭按钮 - 多种变体
            r'<button[^>]*class="[^"]*close[^"]*"[^>]*>\s*<span[^>]*>&times;</span>\s*</button>':
                '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>',

            r'<button[^>]*class="[^"]*close[^"]*"[^>]*>\s*<span[^>]*aria-hidden="true"[^>]*>&times;</span>\s*</button>':
                '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>',

            # 输入组结构调整
            r'<div class="input-group-prepend">\s*<span class="input-group-text">([^<]*)</span>\s*</div>':
                r'<span class="input-group-text">\1</span>',

            r'<div class="input-group-append">\s*<span class="input-group-text">([^<]*)</span>\s*</div>':
                r'<span class="input-group-text">\1</span>',

            r'<div class="input-group-prepend">\s*<button([^>]*)class="([^"]*)"([^>]*)>([^<]*)</button>\s*</div>':
                r'<button\1class="\2"\3>\4</button>',

            r'<div class="input-group-append">\s*<button([^>]*)class="([^"]*)"([^>]*)>([^<]*)</button>\s*</div>':
                r'<button\1class="\2"\3>\4</button>',

            # 卡片组结构
            r'<div class="card-deck">':
                '<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">',

            r'<div class="card-columns">':
                '<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">',

            # 媒体对象
            r'<div class="media">':
                '<div class="d-flex">',

            r'<div class="media-object">':
                '<div class="flex-shrink-0">',

            r'<div class="media-body">':
                '<div class="flex-grow-1 ms-3">',
        }

        # JavaScript/jQuery 相关的转换
        self.js_patterns = {
            # Bootstrap JavaScript 方法调用
            r"\.modal\('show'\)": ".modal('show')",
            r"\.modal\('hide'\)": ".modal('hide')",
            r"\.dropdown\('toggle'\)": ".dropdown('toggle')",
            r"\.collapse\('show'\)": ".collapse('show')",
            r"\.collapse\('hide'\)": ".collapse('hide')",

            # 事件名称
            r"'show\.bs\.modal'": "'show.bs.modal'",
            r"'shown\.bs\.modal'": "'shown.bs.modal'",
            r"'hide\.bs\.modal'": "'hide.bs.modal'",
            r"'hidden\.bs\.modal'": "'hidden.bs.modal'",
        }

    def create_backup(self):
        """创建项目备份"""
        print(f"📦 创建备份到: {self.backup_dir}")
        
        # 只备份重要的文件和目录
        important_paths = [
            "app/templates",
            "app/static/css",
            "app/static/js"
        ]
        
        self.backup_dir.mkdir(exist_ok=True)
        
        for path in important_paths:
            src = self.project_root / path
            if src.exists():
                if src.is_dir():
                    dst = self.backup_dir / path
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copytree(src, dst)
                else:
                    dst = self.backup_dir / path
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src, dst)

    def should_process_file(self, file_path):
        """判断是否应该处理该文件"""
        # 只处理模板文件和CSS文件
        extensions = {'.html', '.css', '.js'}
        if file_path.suffix.lower() not in extensions:
            return False
            
        # 排除备份目录、虚拟环境等
        exclude_dirs = {'venv', '__pycache__', '.git', 'node_modules', 'backup'}
        for part in file_path.parts:
            if part.startswith('.') or part in exclude_dirs:
                return False
                
        return True

    def convert_classes(self, content):
        """转换Bootstrap类名"""
        replacements = 0
        
        for old_pattern, new_class in self.class_mappings.items():
            old_content = content
            content = re.sub(old_pattern, new_class, content)
            count = len(re.findall(old_pattern, old_content))
            if count > 0:
                replacements += count
                pattern_name = old_pattern.replace('\\b', '').replace('\\', '')
                self.report["replacements_by_type"][f"class_{pattern_name}"] = count
                
        return content, replacements

    def convert_attributes(self, content):
        """转换HTML属性"""
        replacements = 0
        
        for old_pattern, new_attr in self.attribute_mappings.items():
            old_content = content
            content = re.sub(old_pattern, new_attr, content)
            count = len(re.findall(old_pattern, old_content))
            if count > 0:
                replacements += count
                pattern_name = old_pattern.split('"')[0].replace('data-', '')
                self.report["replacements_by_type"][f"attr_{pattern_name}"] = count
                
        return content, replacements

    def convert_structures(self, content):
        """转换结构性模式"""
        replacements = 0

        for old_pattern, new_structure in self.structural_patterns.items():
            old_content = content
            content = re.sub(old_pattern, new_structure, content, flags=re.MULTILINE | re.DOTALL)
            count = len(re.findall(old_pattern, old_content, flags=re.MULTILINE | re.DOTALL))
            if count > 0:
                replacements += count
                self.report["replacements_by_type"][f"structure_{count}"] = count

        return content, replacements

    def convert_javascript(self, content):
        """转换JavaScript相关模式"""
        replacements = 0

        for old_pattern, new_js in self.js_patterns.items():
            old_content = content
            content = re.sub(old_pattern, new_js, content)
            count = len(re.findall(old_pattern, old_content))
            if count > 0:
                replacements += count
                pattern_name = old_pattern.replace('\\', '').replace('.', '_')
                self.report["replacements_by_type"][f"js_{pattern_name}"] = count

        return content, replacements

    def smart_class_cleanup(self, content):
        """智能清理重复或冲突的类名"""
        replacements = 0

        # 清理重复的类名
        def clean_duplicate_classes(match):
            classes = match.group(1).split()
            unique_classes = []
            seen = set()
            for cls in classes:
                if cls not in seen:
                    unique_classes.append(cls)
                    seen.add(cls)
            return f'class="{" ".join(unique_classes)}"'

        old_content = content
        content = re.sub(r'class="([^"]*)"', clean_duplicate_classes, content)
        if content != old_content:
            replacements += 1
            self.report["replacements_by_type"]["class_cleanup"] = 1

        # 移除空的class属性
        content = re.sub(r'\s*class=""\s*', ' ', content)
        content = re.sub(r'\s*class="\s*"\s*', ' ', content)

        return content, replacements

    def process_file(self, file_path):
        """处理单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            content = original_content
            total_replacements = 0

            # 转换类名
            content, class_replacements = self.convert_classes(content)
            total_replacements += class_replacements

            # 转换属性
            content, attr_replacements = self.convert_attributes(content)
            total_replacements += attr_replacements

            # 转换结构
            content, struct_replacements = self.convert_structures(content)
            total_replacements += struct_replacements

            # 转换JavaScript（如果是JS文件或包含script标签）
            if file_path.suffix.lower() in {'.js'} or '<script' in content:
                content, js_replacements = self.convert_javascript(content)
                total_replacements += js_replacements

            # 智能类名清理
            content, cleanup_replacements = self.smart_class_cleanup(content)
            total_replacements += cleanup_replacements

            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.report["files_modified"] += 1
                self.report["total_replacements"] += total_replacements
                self.report["files_with_changes"].append({
                    "file": str(file_path.relative_to(self.project_root)),
                    "replacements": total_replacements,
                    "details": {
                        "classes": class_replacements,
                        "attributes": attr_replacements,
                        "structures": struct_replacements,
                        "javascript": js_replacements if 'js_replacements' in locals() else 0,
                        "cleanup": cleanup_replacements
                    }
                })

                print(f"✅ {file_path.relative_to(self.project_root)} - {total_replacements} 处修改")

            self.report["files_processed"] += 1

        except Exception as e:
            error_msg = f"处理文件 {file_path} 时出错: {str(e)}"
            print(f"❌ {error_msg}")
            self.report["errors"].append(error_msg)

    def scan_and_convert(self):
        """扫描并转换所有文件"""
        print("🔍 扫描项目文件...")
        
        # 主要处理目录
        target_dirs = [
            self.project_root / "app" / "templates",
            self.project_root / "app" / "static" / "css",
            self.project_root / "app" / "static" / "js"
        ]
        
        for target_dir in target_dirs:
            if not target_dir.exists():
                continue
                
            print(f"📁 处理目录: {target_dir.relative_to(self.project_root)}")
            
            for file_path in target_dir.rglob("*"):
                if file_path.is_file() and self.should_process_file(file_path):
                    self.process_file(file_path)

    def generate_report(self):
        """生成转换报告"""
        report_file = self.project_root / f"bootstrap_conversion_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, indent=2, ensure_ascii=False)
            
        # 生成文本摘要
        summary_file = report_file.with_suffix('.txt')
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("Bootstrap 4 到 5 转换报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"转换时间: {self.report['conversion_time']}\n")
            f.write(f"处理文件数: {self.report['files_processed']}\n")
            f.write(f"修改文件数: {self.report['files_modified']}\n")
            f.write(f"总替换次数: {self.report['total_replacements']}\n\n")
            
            if self.report['replacements_by_type']:
                f.write("替换类型统计:\n")
                for replacement_type, count in self.report['replacements_by_type'].items():
                    f.write(f"  {replacement_type}: {count}\n")
                f.write("\n")
            
            if self.report['files_with_changes']:
                f.write("修改的文件:\n")
                for file_info in self.report['files_with_changes']:
                    f.write(f"  {file_info['file']}: {file_info['replacements']} 处修改\n")
                f.write("\n")
            
            if self.report['errors']:
                f.write("错误信息:\n")
                for error in self.report['errors']:
                    f.write(f"  {error}\n")
        
        print(f"\n📊 报告已生成:")
        print(f"   详细报告: {report_file}")
        print(f"   摘要报告: {summary_file}")

    def run(self):
        """运行转换工具"""
        print("🚀 Bootstrap 4 到 5 超级转换工具启动")
        print("=" * 50)
        
        # 创建备份
        self.create_backup()
        
        # 扫描并转换
        self.scan_and_convert()
        
        # 生成报告
        self.generate_report()
        
        print("\n🎉 转换完成!")
        print(f"📦 备份位置: {self.backup_dir}")
        print(f"📊 处理了 {self.report['files_processed']} 个文件")
        print(f"✏️  修改了 {self.report['files_modified']} 个文件")
        print(f"🔄 总共 {self.report['total_replacements']} 处替换")

if __name__ == "__main__":
    converter = Bootstrap4To5SuperConverter()
    converter.run()
