{% extends 'base.html' %}

{% block title %}食材管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>食材管理</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="#" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加食材
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>食材名称</th>
                        <th>分类</th>
                        <th>计量单位</th>
                        <th>存储温度</th>
                        <th>保质期(天)</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ingredient in ingredients.items %}
                    <tr>
                        <td>{{ ingredient.id }}</td>
                        <td>{{ ingredient.name }}</td>
                        <td>{{ ingredient.category }}</td>
                        <td>{{ ingredient.unit }}</td>
                        <td>{{ ingredient.storage_temp or '常温' }}</td>
                        <td>{{ ingredient.shelf_life or '未设置' }}</td>
                        <td>
                            <a href="#" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">暂无食材数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% if ingredients.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not ingredients.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.ingredients', page=ingredients.prev_num) if ingredients.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in ingredients.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == ingredients.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('main.ingredients', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not ingredients.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.ingredients', page=ingredients.next_num) if ingredients.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}
