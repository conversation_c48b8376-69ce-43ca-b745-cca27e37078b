/* 打印样式 */
@media print {
    /* 基本设置 */
    body {
        font-family: "SimSun", "宋体", serif;
        font-size: 12pt;
        line-height: 1.5;
        background: #fff;
        color: #000;
        margin: 0;
        padding: 0;
    }
    
    /* 隐藏不需要打印的元素 */
    .no-print, .no-print * {
        display: none !important;
    }
    
    /* 页面设置 */
    @page {
        size: A4;
        margin: 1.5cm;
    }
    
    /* 标题样式 */
    h1 {
        font-size: 18pt;
        text-align: center;
        margin-bottom: 20px;
    }
    
    h2 {
        font-size: 16pt;
        margin-top: 15px;
        margin-bottom: 10px;
        border-bottom: 1px solid #000;
        padding-bottom: 5px;
    }
    
    h3 {
        font-size: 14pt;
        margin-top: 10px;
        margin-bottom: 5px;
    }
    
    /* 表格样式 */
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    
    table, th, td {
        border: 1px solid #000;
    }
    
    th, td {
        padding: 8px;
        text-align: left;
    }
    
    th {
        background-color: #f0f0f0;
        font-weight: bold;
    }
    
    /* 分页控制 */
    .page-break {
        page-break-after: always;
    }
    
    .avoid-break {
        page-break-inside: avoid;
    }
    
    /* 页眉页脚 */
    .header {
        position: running(header);
        text-align: center;
    }
    
    .footer {
        position: running(footer);
        text-align: center;
    }
    
    @page {
        @top-center { content: element(header) }
        @bottom-center { content: element(footer) }
    }
    
    /* 学校信息 */
    .school-info {
        text-align: center;
        margin-bottom: 20px;
    }
    
    /* 日期信息 */
    .date-info {
        text-align: right;
        margin-bottom: 20px;
    }
    
    /* 签名区域 */
    .signature-area {
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
    }
    
    .signature-line {
        border-bottom: 1px solid #000;
        width: 150px;
        display: inline-block;
        margin-top: 20px;
    }
    
    /* 图片大小控制 */
    img {
        max-width: 100%;
        max-height: 200px;
    }
    
    /* 打印预览中的分页指示器 */
    .print-page-indicator {
        display: none;
    }
    
    /* 打印按钮 */
    .print-controls {
        display: none;
    }
}

/* 打印预览样式 */
.print-preview {
    max-width: 210mm;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.print-page {
    border: 1px solid #ddd;
    padding: 20mm;
    margin-bottom: 20px;
    background: #fff;
    min-height: 297mm;
    box-sizing: border-box;
    position: relative;
}

.print-page-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #f8f9fc;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    color: #666;
}

.print-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #4e73df;
    color: #fff;
    padding: 10px 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1000;
}

.print-controls button {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    margin: 0 5px;
    padding: 5px 10px;
}

.print-controls button:hover {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}
