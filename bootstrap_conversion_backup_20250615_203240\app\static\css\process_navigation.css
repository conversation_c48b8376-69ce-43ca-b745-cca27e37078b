/* 流程导航样式 */
.process-navigation-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.process-flow-chart {
    overflow-x: auto;
    padding: 10px 0;
}

/* 步骤容器样式 */
.process-steps {
    display: flex;
    align-items: center;
    min-width: 1800px; /* 进一步增加最小宽度，适应更宽的步骤 */
    justify-content: space-between; /* 均匀分布各步骤 */
}

/* 步骤样式 */
.process-step {
    flex: 0 0 auto; /* 不再使用flex:1自动分配空间 */
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 15px; /* 增加内边距 */
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    margin: 0 8px; /* 进一步增加左右间距 */
    width: 180px; /* 增加宽度约30% (从140px增加到180px) */
}

.process-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    z-index: 10;
}

.step-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transition: all 0.3s;
}

.step-icon i {
    font-size: 28px;
    transition: all 0.3s;
}

.process-step:hover .step-icon {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

.process-step:hover .step-icon i {
    transform: scale(1.1);
}

.step-content {
    text-align: center;
    width: 100%;
}

.step-content h5 {
    margin-bottom: 8px;
    font-size: 0.9rem;
    font-weight: 600;
}

.step-status {
    font-size: 0.75rem;
    margin-bottom: 10px;
}

/* 连接器样式 */
.process-connector {
    display: flex;
    align-items: center;
    padding: 0 8px;
    min-width: 40px;
}

.process-connector i {
    color: #6c757d;
    font-size: 1.4rem;
}

/* 状态样式 */
.process-step.not-started {
    background-color: #f5f5f5;
    color: #6c757d;
}

.process-step.not-started .step-icon {
    color: #6c757d;
}

.process-step.in-progress {
    background-color: #e3f2fd;
    color: #0d6efd;
}

.process-step.in-progress .step-icon {
    color: #0d6efd;
}

.process-step.completed {
    background-color: #d1e7dd;
    color: #198754;
}

.process-step.completed .step-icon {
    color: #198754;
}

.process-step.warning {
    background-color: #fff3cd;
    color: #ffc107;
}

.process-step.warning .step-icon {
    color: #ffc107;
}

.process-step.danger {
    background-color: #f8d7da;
    color: #dc3545;
}

.process-step.danger .step-icon {
    color: #dc3545;
}

/* 按钮样式 */
.step-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    width: 100%;
    margin-top: 5px;
}

.step-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s;
}

.step-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.15);
}

/* 过滤按钮样式 */
.process-filter {
    display: flex;
    gap: 5px;
}

/* 操作指引样式 */
.step-guidance {
    width: 100%;
    text-align: left;
}

.guidance-content {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    padding: 10px;
    font-size: 0.75rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-left: 3px solid #17a2b8;
}

.guidance-content p {
    color: #495057;
}

.guidance-content strong {
    color: #17a2b8;
}

.guidance-tips {
    padding-left: 15px;
    margin-bottom: 0;
    color: #6c757d;
}

.guidance-tips li {
    margin-bottom: 3px;
    line-height: 1.3;
}

.guidance-tips li:last-child {
    margin-bottom: 0;
}

/* 今日任务提示样式 */
.today-tasks .alert {
    margin-bottom: 0;
}

/* 响应式调整 */
@media (max-width: 1920px) {
    .process-steps {
        min-width: 1700px;
    }

    .process-step {
        width: 170px;
        padding: 14px 12px;
    }

    .step-icon {
        width: 58px;
        height: 58px;
    }

    .step-icon i {
        font-size: 24px;
    }

    .step-content h5 {
        font-size: 0.95rem;
    }

    .step-status {
        font-size: 0.8rem;
    }

    .step-actions .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }

    .guidance-content {
        font-size: 0.8rem;
        padding: 12px;
    }

    .guidance-tips {
        padding-left: 16px;
    }
}

@media (max-width: 1600px) {
    .process-steps {
        min-width: 1600px;
    }

    .process-step {
        width: 160px;
        padding: 12px 10px;
    }

    .step-icon {
        width: 55px;
        height: 55px;
    }

    .step-icon i {
        font-size: 22px;
    }

    .step-content h5 {
        font-size: 0.9rem;
    }

    .step-status {
        font-size: 0.75rem;
    }

    .step-actions .btn {
        padding: 0.18rem 0.35rem;
        font-size: 0.7rem;
    }

    .guidance-content {
        font-size: 0.75rem;
        padding: 10px;
    }

    .guidance-tips {
        padding-left: 15px;
    }
}

@media (max-width: 1400px) {
    .process-steps {
        min-width: 1500px;
    }

    .process-step {
        width: 150px;
        padding: 10px 8px;
    }

    .step-icon {
        width: 50px;
        height: 50px;
    }

    .step-icon i {
        font-size: 20px;
    }

    .step-content h5 {
        font-size: 0.85rem;
    }

    .step-status {
        font-size: 0.7rem;
    }

    .step-actions .btn {
        padding: 0.15rem 0.3rem;
        font-size: 0.65rem;
    }

    .guidance-content {
        font-size: 0.7rem;
        padding: 8px;
    }

    .guidance-tips {
        padding-left: 12px;
    }
}

@media (max-width: 768px) {
    .process-steps {
        flex-direction: column;
        min-width: auto;
    }

    .process-step {
        width: 100%;
        max-width: 100%;
        margin-bottom: 10px;
    }

    .process-connector {
        transform: rotate(90deg);
        margin: 5px 0;
    }
}
