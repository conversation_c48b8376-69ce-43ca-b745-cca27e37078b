{% extends 'daily_management/print/base_print.html' %}

{% block title %}食堂培训记录 - {{ log.log_date.strftime('%Y-%m-%d') }}{% endblock %}

{% block document_title %}食堂培训记录{% endblock %}

{% block document_subtitle %}{{ log.log_date.strftime('%Y年%m月%d日') }}{% endblock %}

{% block document_info %}
<div class="info-row">
    <div class="info-label">日期：</div>
    <div class="info-value">{{ log.log_date.strftime('%Y-%m-%d') }}</div>
</div>
<div class="info-row">
    <div class="info-label">管理员：</div>
    <div class="info-value">{{ log.manager or '未设置' }}</div>
</div>
<div class="info-row">
    <div class="info-label">培训次数：</div>
    <div class="info-value">{{ trainings|length }}次</div>
</div>
{% endblock %}

{% block content %}
<!-- 培训记录 -->
<div class="section-title">培训记录列表</div>
{% if trainings %}
<table>
    <thead>
        <tr>
            <th width="20%">培训主题</th>
            <th width="15%">培训时间</th>
            <th width="15%">培训人员</th>
            <th width="15%">参与人数</th>
            <th width="35%">培训内容</th>
        </tr>
    </thead>
    <tbody>
        {% for training in trainings %}
        <tr>
            <td>{{ training.topic }}</td>
            <td>
                {% if training.training_time %}
                    {% if training.training_time is string %}
                        {{ training.training_time }}
                    {% else %}
                        {{ training.training_time.strftime('%H:%M') }}
                    {% endif %}
                {% else %}
                    未记录
                {% endif %}
            </td>
            <td>{{ training.trainer_name }}</td>
            <td>{{ training.participant_count }}</td>
            <td>{{ training.content }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- 培训详情 -->
<div class="section-title">培训详情</div>
{% for training in trainings %}
<div style="margin-bottom: 20px; border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
    <h3 style="margin-top: 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">
        {{ training.topic }}
    </h3>
    
    <div style="margin-bottom: 10px;">
        <strong>培训时间：</strong>
        {% if training.training_time %}
            {% if training.training_time is string %}
                {{ training.training_time }}
            {% else %}
                {{ training.training_time.strftime('%Y-%m-%d %H:%M') }}
            {% endif %}
        {% else %}
            未记录
        {% endif %}
        <strong class="ms-3">培训人员：</strong> {{ training.trainer_name }}
        <strong class="ms-3">参与人数：</strong> {{ training.participant_count }}人
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>培训内容：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ training.content|default('无内容')|nl2br }}
        </div>
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>培训目标：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ training.objectives|default('无培训目标')|nl2br }}
        </div>
    </div>
    
    <div style="margin-bottom: 10px;">
        <strong>培训效果：</strong>
        <div style="margin-top: 5px; padding: 10px; background-color: #f9f9f9; border-radius: 3px;">
            {{ training.results|default('无培训效果记录')|nl2br }}
        </div>
    </div>
    
    <!-- 培训照片 -->
    {% if training.photos and training.photos|length > 0 %}
    <div style="margin-top: 15px;">
        <strong>培训照片：</strong>
        <div class="photo-container">
            {% for photo in training.photos %}
            <div class="photo-item">
                <img src="{{ url_for('static', filename=photo.file_path) }}" alt="培训照片">
                <div class="photo-caption">培训照片 {{ loop.index }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endfor %}
{% else %}
<p>暂无培训记录</p>
{% endif %}

<!-- 参与人员签名 -->
<div class="section-title">参与人员签名</div>
<div style="border: 1px solid #000; padding: 10px; min-height: 200px;">
    <!-- 这里留空，用于手写签名 -->
</div>
{% endblock %}

{% block signature %}
<div class="signature-item">
    <div class="signature-line"></div>
    <div>培训人员</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>食堂负责人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div>学校负责人</div>
</div>
{% endblock %}
