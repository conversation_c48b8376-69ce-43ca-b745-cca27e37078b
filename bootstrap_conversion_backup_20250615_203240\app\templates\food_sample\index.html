{% extends 'base.html' %}

{% block title %}留样管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">留样记录列表</h3>
                            <small class="text-muted">查看和管理食品留样记录，支持按条件筛选和打印</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('food_sample.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 新建留样记录
                            </a>
                            <a href="{{ url_for('food_sample.print_daily') }}" class="btn btn-secondary btn-sm" target="_blank">
                                <i class="fas fa-print"></i> 打印当日留样记录
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('food_sample.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>区域</label>
                                    <select name="area_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label>状态</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部</option>
                                        <option value="已留样" {% if status == '已留样' %}selected{% endif %}>已留样</option>
                                        <option value="已销毁" {% if status == '已销毁' %}selected{% endif %}>已销毁</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label>餐次</label>
                                    <select name="meal_type" class="form-control">
                                        <option value="">全部</option>
                                        <option value="早餐" {% if meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                        <option value="午餐" {% if meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                        <option value="晚餐" {% if meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label>开始日期</label>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label>结束日期</label>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary form-control">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>留样编号</th>
                                    <th>区域</th>
                                    <th>食谱名称</th>
                                    <th>用餐日期</th>
                                    <th>餐次</th>
                                    <th>留样图片</th>
                                    <th>留样时间</th>
                                    <th>销毁时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for food_sample in food_samples %}
                                <tr>
                                    <td>{{ food_sample.sample_number }}</td>
                                    <td>{{ food_sample.area.name }}</td>
                                    <td>{{ food_sample.recipe.name }}</td>
                                    <td>{{ food_sample.meal_date }}</td>
                                    <td>{{ food_sample.meal_type }}</td>
                                    <td>
                                        {% if food_sample.sample_image %}
                                        <a href="{{ url_for('static', filename=food_sample.sample_image) }}" target="_blank">
                                            <img src="{{ url_for('static', filename=food_sample.sample_image) }}" alt="留样图片" style="max-height: 50px;">
                                        </a>
                                        {% else %}
                                        无图片
                                        {% endif %}
                                    </td>
                                    <td>{{  food_sample.start_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                                    <td>
                                        {% if food_sample.destruction_time %}
                                        {{  food_sample.destruction_time|format_datetime('%Y-%m-%d %H:%M')  }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if food_sample.status == '已留样' %}
                                        <span class="badge badge-info">已留样</span>
                                        {% elif food_sample.status == '已销毁' %}
                                        <span class="badge badge-secondary">已销毁</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('food_sample.view', id=food_sample.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        {% if food_sample.status == '已留样' %}
                                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#destroyModal{{ food_sample.id }}">
                                            <i class="fas fa-trash"></i> 销毁
                                        </button>
                                        {% endif %}
                                        <a href="{{ url_for('food_sample.print_sample', id=food_sample.id) }}" class="btn btn-secondary btn-sm" target="_blank">
                                            <i class="fas fa-print"></i> 打印
                                        </a>
                                    </td>
                                </tr>

                                <!-- 销毁确认模态框 -->
                                <div class="modal fade" id="destroyModal{{ food_sample.id }}" tabindex="-1" role="dialog" aria-labelledby="destroyModalLabel{{ food_sample.id }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="destroyModalLabel{{ food_sample.id }}">确认销毁</h5>
                                                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                确定要销毁留样记录 <strong>{{ food_sample.sample_number }}</strong> 吗？此操作不可撤销。
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <form action="{{ url_for('food_sample.destroy', id=food_sample.id) }}" method="post" novalidate novalidate><button type="submit" class="btn btn-danger">确认销毁</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <tr>
                                    <td colspan="10" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('food_sample.index', page=pagination.prev_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, meal_type=meal_type) }}">
                                    上一页
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">上一页</span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('food_sample.index', page=page, area_id=area_id, status=status, start_date=start_date, end_date=end_date, meal_type=meal_type) }}">
                                            {{ page }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('food_sample.index', page=pagination.next_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, meal_type=meal_type) }}">
                                    下一页
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">下一页</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
