/**
 * 全局Z-Index层级管理系统
 * 
 * 目的：无需修改每个HTML页面，通过CSS规则统一管理所有元素的层级关系
 * 解决：内容区域中的高z-index元素遮挡顶部下拉菜单的问题
 * 
 * 层级体系：
 * 1100+: 模态框和对话框
 * 1090:  左侧导航栏 (最高优先级)
 * 1085:  移动端遮罩层
 * 1080:  下拉菜单和弹出层
 * 1070:  顶部工具栏
 * 1060:  移动端底部操作栏
 * 1030:  其他导航栏
 * 1-100: 普通内容层级
 */

/* === 核心层级定义 === */
:root {
  --z-modal: 1100;
  --z-sidebar: 1090;
  --z-overlay: 1085;
  --z-dropdown: 1080;
  --z-toolbar: 1070;
  --z-bottom-actions: 1060;
  --z-navbar: 1030;
  --z-content-high: 100;
  --z-content-medium: 50;
  --z-content-low: 10;
  --z-content-base: 1;
}

/* === 全局强制层级控制 === */

/* 1. 内容区域基础层级 */
.content-area {
  z-index: var(--z-content-base) !important;
  position: relative !important;
}

/* 2. 强制重置内容区域中的高z-index元素 */
.content-area [style*="z-index: 1000"],
.content-area [style*="z-index:1000"],
.content-area [style*="z-index: 999"],
.content-area [style*="z-index:999"],
.content-area [style*="z-index: 998"],
.content-area [style*="z-index:998"] {
  z-index: var(--z-content-medium) !important;
}

/* 3. 常见高z-index CSS类重置 */
.content-area .z-1000,
.content-area .z-999,
.content-area .z-998 {
  z-index: var(--z-content-medium) !important;
}

/* 4. 常见组件层级统一管理 */
.content-area .loading-overlay,
.content-area .overlay,
.content-area .spinner-overlay,
.content-area .progress-overlay {
  z-index: var(--z-content-low) !important;
}

.content-area .alert,
.content-area .notification,
.content-area .toast {
  z-index: var(--z-content-medium) !important;
}

.content-area .tooltip,
.content-area .popover {
  z-index: var(--z-content-high) !important;
}

/* 5. 第三方组件层级控制 */
.content-area .dataTables_wrapper,
.content-area .select2-container,
.content-area .bootstrap-table,
.content-area .chart-container,
.content-area .calendar-widget,
.content-area .file-upload-area {
  z-index: auto !important;
}

/* 6. 表格和卡片组件 */
.content-area .table-responsive,
.content-area .card,
.content-area .card-header,
.content-area .card-body,
.content-area .card-footer {
  z-index: auto !important;
}

/* 7. 表单组件 */
.content-area .form-control,
.content-area .form-select,
.content-area .input-group,
.content-area .form-floating {
  z-index: auto !important;
}

/* 8. 按钮和链接 */
.content-area .btn,
.content-area .btn-group,
.content-area .dropdown-toggle {
  z-index: auto !important;
}

/* 9. 内容区域的下拉菜单 - 特殊处理 */
.content-area .dropdown-menu {
  z-index: var(--z-content-high) !important; /* 相对较高但仍在安全范围 */
}

/* 10. 防止任何内联样式覆盖 - 最高优先级 */
.content-area *[style*="z-index"] {
  z-index: var(--z-content-medium) !important;
}

/* === 移动端特殊处理 === */
@media (max-width: 768px) {
  .content-area .mobile-fixed,
  .content-area .mobile-overlay,
  .content-area .mobile-popup,
  .content-area .mobile-bottom-sheet {
    z-index: var(--z-content-medium) !important;
  }
}

/* === 特定页面组件处理 === */

/* 仪表盘页面 */
.content-area .dashboard-widget,
.content-area .chart-widget,
.content-area .stats-card {
  z-index: auto !important;
}

/* 表格页面 */
.content-area .table-container,
.content-area .table-toolbar,
.content-area .table-pagination {
  z-index: auto !important;
}

/* 表单页面 */
.content-area .form-container,
.content-area .form-section,
.content-area .form-actions {
  z-index: auto !important;
}

/* === 调试辅助 === */
/* 开发环境下可以启用这个规则来检查层级问题 */
/*
.content-area *[style*="z-index"] {
  outline: 2px solid red !important;
  position: relative !important;
}
.content-area *[style*="z-index"]::before {
  content: "Z-INDEX DETECTED";
  position: absolute;
  top: 0;
  left: 0;
  background: red;
  color: white;
  font-size: 10px;
  padding: 2px;
  z-index: 9999;
}
*/

/* === 兼容性保证 === */
/* 确保旧版本浏览器的兼容性 */
.content-area .legacy-overlay {
  z-index: 50 !important;
}

.content-area .ie-compat {
  z-index: auto !important;
}
