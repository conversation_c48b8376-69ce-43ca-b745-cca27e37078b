{% extends 'base.html' %}

{% block content %}
<div class="content-area">
    <!-- 顶部工具栏 -->
    <div class="top-toolbar">
        <div class="toolbar-left">
            <h5 class="page-title mb-0">{% block page_title %}{% endblock %}</h5>
        </div>
        <div class="toolbar-right">
            {% block toolbar_actions %}{% endblock %}
        </div>
    </div>

    <!-- 主要内容区 -->
    <div class="content-body p-3">
        {% block content_body %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 管理后台通用样式 */
.content-body {
    background-color: #f8f9fa;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1rem;
}

.card-body {
    padding: 1rem;
}

.table th {
    font-weight: 500;
    background-color: #f8f9fa;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.form-control-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .content-body {
        padding: 0.75rem !important;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .table-responsive {
        margin: 0 -0.75rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 通用确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 通用表单验证
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return true;
    
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return false;
    }
    return true;
}

// 通用AJAX请求
async function makeRequest(url, method = 'GET', data = null) {
    try {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
}
</script>
{% endblock %} 