{% extends "financial/base.html" %}

{% block page_title %}会计科目文本树{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></span>
<span class="uf-breadcrumb-item active">文本树视图</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn">
    <i class="fas fa-arrow-left uf-icon"></i> 返回列表
</a>
<button class="uf-btn uf-btn-success" onclick="downloadText()">
    <i class="fas fa-download uf-icon"></i> 下载文本
</button>
<button class="uf-btn uf-btn-info" onclick="printView()">
    <i class="fas fa-print uf-icon"></i> 打印
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格统计信息 -->
<div class="uf-card" style="margin-bottom: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-chart-bar uf-icon"></i> 科目统计信息
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
            <div style="text-align: center; padding: 16px; background: #e6f2ff; border: 1px solid #b3d9ff; border-radius: 2px;">
                <div style="font-size: 24px; font-weight: 600; color: var(--uf-primary); margin-bottom: 4px;">{{ subjects_count }}</div>
                <div style="font-size: 11px; color: #666;">总科目数量</div>
            </div>
            <div style="text-align: center; padding: 16px; background: #f0f9ff; border: 1px solid #c3dafe; border-radius: 2px;">
                <div style="font-size: 24px; font-weight: 600; color: var(--uf-info); margin-bottom: 4px;">6</div>
                <div style="font-size: 11px; color: #666;">科目类型</div>
            </div>
            <div style="text-align: center; padding: 16px; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 2px;">
                <div style="font-size: 24px; font-weight: 600; color: var(--uf-success); margin-bottom: 4px;">✓</div>
                <div style="font-size: 11px; color: #666;">结构完整</div>
            </div>
            <div style="text-align: center; padding: 16px; background: #fefce8; border: 1px solid #fde047; border-radius: 2px;">
                <div style="font-size: 14px; font-weight: 600; color: var(--uf-warning); margin-bottom: 4px;">{{ current_time.strftime('%H:%M') }}</div>
                <div style="font-size: 11px; color: #666;">{{ current_time.strftime('%Y-%m-%d') }}</div>
            </div>
        </div>
    </div>
</div>

<!-- 用友风格文本控制工具栏 -->
<div class="uf-card" style="margin-bottom: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-cogs uf-icon"></i> 显示控制
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; align-items: end;">
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 4px;">字体大小：</label>
                <select id="fontSize" class="uf-form-control" style="font-size: 11px;">
                    <option value="12">12px</option>
                    <option value="14" selected>14px</option>
                    <option value="16">16px</option>
                    <option value="18">18px</option>
                    <option value="20">20px</option>
                </select>
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 4px;">主题：</label>
                <div style="display: flex; gap: 4px;">
                    <button type="button" class="uf-btn uf-btn-sm uf-btn-primary" id="lightTheme">浅色</button>
                    <button type="button" class="uf-btn uf-btn-sm" id="darkTheme">深色</button>
                </div>
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 4px;">搜索科目：</label>
                <div style="display: flex; gap: 4px;">
                    <input type="text" class="uf-form-control" id="searchText" placeholder="输入科目编码或名称..." style="flex: 1; font-size: 11px;">
                    <button class="uf-btn uf-btn-sm uf-btn-primary" onclick="searchInText()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="uf-btn uf-btn-sm" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 4px;">操作：</label>
                <div style="display: flex; gap: 4px;">
                    <button class="uf-btn uf-btn-sm uf-btn-success" onclick="copyToClipboard()">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <button class="uf-btn uf-btn-sm uf-btn-info" onclick="downloadText()">
                        <i class="fas fa-download"></i> 下载
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用友风格文本内容显示 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-sitemap uf-icon"></i> 会计科目文本树
    </div>
    <div class="uf-card-body" style="padding: 0;">
        <div class="uf-text-display" id="subjectsText">{{ subjects_text }}</div>
    </div>
</div>
{% endblock %}

{% block financial_css %}
<style>
/* 用友风格文本显示样式 */
.uf-text-display {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    line-height: 1.6;
    background-color: #fafafa;
    border: 1px solid var(--uf-border);
    padding: 16px;
    white-space: pre-wrap;
    overflow-x: auto;
    min-height: 500px;
    color: #333;
}

.uf-text-display.dark-theme {
    background-color: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
}

.search-highlight {
    background-color: #fff3cd;
    color: #856404;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 600;
}

/* 打印样式 */
@d-flex print {
    .uf-card-header,
    .uf-btn,
    .uf-toolbar {
        display: none !important;
    }

    .uf-text-display {
        border: none;
        background: white !important;
        color: black !important;
        font-size: 12px;
        padding: 0;
        min-height: auto;
    }

    .uf-card {
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
    }

    .uf-card-body {
        padding: 0 !important;
    }
}

/* 响应式设计 */
@d-flex (max-width: 768px) {
    .uf-text-display {
        font-size: 12px;
        padding: 12px;
    }
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
  // 字体大小控制
  $('#fontSize').on('change', function() {
    const fontSize = $(this).val() + 'px';
    $('.text-display').css('font-size', fontSize);
  });
  
  // 主题切换
  $('#lightTheme').on('click', function() {
    $('.text-display').removeClass('dark-theme');
    $('#lightTheme').addClass('active');
    $('#darkTheme').removeClass('active');
  });
  
  $('#darkTheme').on('click', function() {
    $('.text-display').addClass('dark-theme');
    $('#darkTheme').addClass('active');
    $('#lightTheme').removeClass('active');
  });
  
  // 搜索功能
  $('#searchText').on('keyup', function(e) {
    if (e.key === 'Enter') {
      searchInText();
    }
  });
});

function copyToClipboard() {
  const text = $('#subjectsText').text();
  
  navigator.clipboard.writeText(text).then(function() {
    showAlert('科目树文本已复制到剪贴板', 'success');
  }).catch(function(err) {
    console.error('复制失败:', err);
    showAlert('复制失败，请手动选择文本复制', 'danger');
  });
}

function downloadText() {
  const text = $('#subjectsText').text();
  const filename = '会计科目树_' + new Date().toISOString().slice(0, 10) + '.txt';
  
  const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  showAlert('科目树文本文件下载完成', 'success');
}

function printView() {
  window.print();
}

function searchInText() {
  const searchTerm = $('#searchText').val().trim();
  const textDisplay = $('#subjectsText');
  
  if (!searchTerm) {
    clearSearch();
    return;
  }
  
  // 保存原始文本
  if (!textDisplay.attr('data-original')) {
    textDisplay.attr('data-original', textDisplay.text());
  }
  
  const originalText = textDisplay.attr('data-original');
  const regex = new RegExp(`(${searchTerm})`, 'gi');
  const highlightedText = originalText.replace(regex, '<span class="search-highlight">$1</span>');
  
  textDisplay.html(highlightedText);
  
  const matchCount = (originalText.match(regex) || []).length;
  if (matchCount > 0) {
    showAlert(`找到 ${matchCount} 个匹配项`, 'info');
  } else {
    showAlert('未找到匹配项', 'warning');
  }
}

function clearSearch() {
  const textDisplay = $('#subjectsText');
  const originalText = textDisplay.attr('data-original');
  
  if (originalText) {
    textDisplay.html(originalText);
  }
  
  $('#searchText').val('');
}

function showAlert(message, type) {
  const alertClass = type === 'danger' ? 'alert-danger' : 
                    type === 'success' ? 'alert-success' : 
                    type === 'warning' ? 'alert-warning' : 'alert-info';
  
  const alert = `
    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
      ${message}
      <button type="button" class="close" data-bs-dismiss="alert">
        <span>&times;</span>
      </button>
    </div>
  `;
  
  // 移除旧的提示
  $('.alert').remove();
  
  // 添加新提示
  $('.card-body').prepend(alert);
  
  // 3秒后自动移除
  setTimeout(function() {
    $('.alert').fadeOut();
  }, 3000);
}
</script>
{% endblock %}
