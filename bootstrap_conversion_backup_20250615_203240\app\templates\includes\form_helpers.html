{% macro render_field(field, label_class='', field_class='form-control', with_label=true) %}
  <div class="mb-3">
    {% if with_label %}
      {{ field.label(class=label_class) }}
    {% endif %}
    
    {% if field.errors %}
      {{ field(class=field_class + ' is-invalid') }}
      <div class="invalid-feedback">
        {% for error in field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% else %}
      {{ field(class=field_class) }}
    {% endif %}
    
    {% if field.description %}
      <small class="form-text text-muted">{{ field.description }}</small>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_checkbox(field, label_class='', field_class='form-check-input') %}
  <div class="mb-3 form-check">
    {% if field.errors %}
      {{ field(class=field_class + ' is-invalid') }}
      {{ field.label(class='form-check-label ' + label_class) }}
      <div class="invalid-feedback">
        {% for error in field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% else %}
      {{ field(class=field_class) }}
      {{ field.label(class='form-check-label ' + label_class) }}
    {% endif %}
    
    {% if field.description %}
      <small class="form-text text-muted">{{ field.description }}</small>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_radio_field(field, label_class='', field_class='form-check-input') %}
  <div class="mb-3">
    {{ field.label(class=label_class) }}
    {% for subfield in field %}
      <div class="form-check">
        {% if field.errors %}
          {{ subfield(class=field_class + ' is-invalid') }}
          {{ subfield.label(class='form-check-label') }}
        {% else %}
          {{ subfield(class=field_class) }}
          {{ subfield.label(class='form-check-label') }}
        {% endif %}
      </div>
    {% endfor %}
    
    {% if field.errors %}
      <div class="invalid-feedback d-block">
        {% for error in field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% endif %}
    
    {% if field.description %}
      <small class="form-text text-muted">{{ field.description }}</small>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_submit(text='提交', class='btn-primary', cancel_url=none, cancel_text='取消') %}
  <div class="mb-3 mt-4">
    <button type="submit" class="btn {{ class }}">{{ text }}</button>
    {% if cancel_url %}
      <a href="{{ cancel_url }}" class="btn btn-secondary ms-2">{{ cancel_text }}</a>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_search_form(action, method='GET', placeholder='搜索...', button_text='搜索', query_param='q') %}
  <form action="{{ action }}" method="{{ method }}" class="form-inline my-2 my-lg-0">
    <div class="input-group">
      <input class="form-control" type="search" name="{{ query_param }}" placeholder="{{ placeholder }}" aria-label="{{ placeholder }}">
      <div >
        <button class="btn btn-outline-primary" type="submit">{{ button_text }}</button>
      </div>
    </div>
  </form>
{% endmacro %}
