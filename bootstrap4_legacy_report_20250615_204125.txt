Bootstrap 4 遗留代码检查报告
==================================================

扫描时间: 2025-06-15T20:41:25.554907
扫描文件: 425
发现问题: 249

BOOTSTRAP4_CLASSES (79 个):
------------------------------
  文件: app\static\css\color-contrast-fix.css
  行号: 130
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\color-contrast-fix.css
  行号: 148
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\custom-csp-fixes.css
  行号: 77
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\dashboard-optimization.css
  行号: 216
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\dashboard-optimization.css
  行号: 250
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\dashboard-optimization.css
  行号: 533
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\elegant-navigation.css
  行号: 224
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\enhanced-image-uploader.css
  行号: 144
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\enhanced-image-uploader.css
  行号: 151
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\home.css
  行号: 90
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\homepage.css
  行号: 295
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\homepage.css
  行号: 337
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\homepage.css
  行号: 530
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\homepage.css
  行号: 553
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\homepage.css
  行号: 593
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\homepage.css
  行号: 853
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\homepage.css
  行号: 1090
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\inventory-optimization.css
  行号: 200
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\inventory-optimization.css
  行号: 224
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 4
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 11
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 18
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 54
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 104
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 256
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 301
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 400
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 517
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 638
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 698
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 763
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 798
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 872
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 957
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1008
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1048
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1070
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1102
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1139
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1167
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1209
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1287
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1419
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1461
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1481
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1499
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1535
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1576
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1614
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1643
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\mobile-optimization.css
  行号: 1672
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\print-styles.css
  行号: 2
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\process_navigation.css
  行号: 217
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\process_navigation.css
  行号: 259
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\process_navigation.css
  行号: 301
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\process_navigation.css
  行号: 343
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\progress-steps.css
  行号: 51
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\progress-steps.css
  行号: 78
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\progress-steps.css
  行号: 220
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\progress-steps.css
  行号: 238
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\sidebar-layout.css
  行号: 237
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\sidebar-layout.css
  行号: 347
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\sidebar-layout.css
  行号: 361
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\style.css
  行号: 212
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\table-headers-global.css
  行号: 56
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\table-optimization.css
  行号: 226
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\table-optimization.css
  行号: 261
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 813
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 819
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 826
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 848
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 994
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 1051
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 1301
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 1504
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\theme-colors.css
  行号: 1548
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\weekly_menu_modal.css
  行号: 90
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\weekly_menu_modal.css
  行号: 109
  模式: \bmedia\b
  匹配: media

  文件: app\static\css\weekly_menu_modal.css
  行号: 116
  模式: \bmedia\b
  匹配: media


BOOTSTRAP4_JS (170 个):
------------------------------
  文件: app\templates\admin\carousel_list.html
  行号: 248
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\templates\admin\data_management.html
  行号: 456
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\admin\data_management.html
  行号: 460
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\admin\users.html
  行号: 436
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\admin\users.html
  行号: 441
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\admin\users.html
  行号: 498
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\admin\guide_management\demo_data.html
  行号: 263
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\admin\guide_management\scenarios.html
  行号: 241
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\admin\guide_management\scenarios.html
  行号: 258
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\admin\guide_management\scenarios.html
  行号: 288
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\admin\guide_management\scenarios.html
  行号: 294
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\daily_management\fixed_inspection_qrcode.html
  行号: 209
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\inspection_templates.html
  行号: 615
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\inspection_templates.html
  行号: 502
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\daily_management\inspection_templates.html
  行号: 676
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\daily_management\optimized_dashboard.html
  行号: 761
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\optimized_dashboard.html
  行号: 816
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\daily_management\public_rate_inspection_photos.html
  行号: 401
  模式: 'show\.bs\.modal'
  匹配: 'show.bs.modal'

  文件: app\templates\daily_management\simplified_inspection.html
  行号: 558
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\simplified_inspection.html
  行号: 561
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\templates\daily_management\trainings.html
  行号: 116
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\view_training.html
  行号: 166
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\components\data_visualization.html
  行号: 215
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\components\data_visualization.html
  行号: 229
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\daily_management\components\data_visualization.html
  行号: 263
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\daily_management\widgets\image_widget.html
  行号: 482
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\financial\accounting_subjects\form.html
  行号: 778
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\financial\accounting_subjects\form.html
  行号: 826
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\financial\vouchers\index.html
  行号: 1079
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\financial\vouchers\index.html
  行号: 1137
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\financial\vouchers\index.html
  行号: 1291
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\guide\scenario_selection.html
  行号: 202
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\ingredient\categories.html
  行号: 226
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\ingredient\categories.html
  行号: 243
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\ingredient\categories.html
  行号: 247
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\ingredient\index.html
  行号: 236
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\ingredient\index.html
  行号: 253
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\ingredient\index.html
  行号: 257
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\ingredient\index_category.html
  行号: 245
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\ingredient\index_category.html
  行号: 262
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\ingredient\index_category.html
  行号: 266
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\inspection\edit.html
  行号: 335
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\inspection\view.html
  行号: 282
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\inventory\statistics.html
  行号: 625
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\inventory\statistics.html
  行号: 628
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\templates\main\canteen_dashboard_new.html
  行号: 866
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\main\index.html
  行号: 1804
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\main\index.html
  行号: 1807
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\templates\purchase_order\create_form.html
  行号: 764
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\create_form.html
  行号: 872
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\create_from_menu.html
  行号: 1057
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 860
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 898
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 946
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 989
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 1322
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 1328
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 1334
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 1340
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\index.html
  行号: 890
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\index.html
  行号: 938
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\index.html
  行号: 981
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\index.html
  行号: 1017
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\view.html
  行号: 802
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\view.html
  行号: 832
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\view.html
  行号: 870
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\view.html
  行号: 903
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\purchase_order\view.html
  行号: 816
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\view.html
  行号: 854
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\view.html
  行号: 887
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\purchase_order\view.html
  行号: 926
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\recipe\categories.html
  行号: 82
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\recipe\categories.html
  行号: 99
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\recipe\categories.html
  行号: 103
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\recipe\form_simplified.html
  行号: 368
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\recipe\form_simplified.html
  行号: 269
  模式: 'show\.bs\.modal'
  匹配: 'show.bs.modal'

  文件: app\templates\recipe\index.html
  行号: 535
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\recipe\index.html
  行号: 568
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\recipe\index.html
  行号: 583
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\recipe\view.html
  行号: 395
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\recipe\view.html
  行号: 439
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\batch_editor_simplified_scripts.html
  行号: 534
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\edit.html
  行号: 1008
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\edit.html
  行号: 1361
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\edit.html
  行号: 1384
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\edit.html
  行号: 1453
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\edit.html
  行号: 1105
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\edit.html
  行号: 1380
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\edit.html
  行号: 1417
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\form.html
  行号: 755
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\view.html
  行号: 1051
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\view.html
  行号: 1050
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\view.html
  行号: 1076
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\view.html
  行号: 1105
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\wizard.html
  行号: 741
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\wizard.html
  行号: 958
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\wizard.html
  行号: 761
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\wizard.html
  行号: 1163
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\stock_in\wizard_simple.html
  行号: 107
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\stock_in\wizard_simple.html
  行号: 184
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\category_index.html
  行号: 103
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\category_index.html
  行号: 120
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\category_index.html
  行号: 124
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\certificate_index.html
  行号: 216
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\certificate_index.html
  行号: 233
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\certificate_index.html
  行号: 237
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\certificate_view.html
  行号: 163
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\certificate_view.html
  行号: 195
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\certificate_view.html
  行号: 180
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\certificate_view.html
  行号: 184
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\index.html
  行号: 347
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\index.html
  行号: 364
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\index.html
  行号: 368
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_index.html
  行号: 416
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_index.html
  行号: 527
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_index.html
  行号: 547
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_index.html
  行号: 567
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_index.html
  行号: 587
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_index.html
  行号: 604
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_index.html
  行号: 612
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_index.html
  行号: 433
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_index.html
  行号: 437
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_index.html
  行号: 638
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_index.html
  行号: 642
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_index.html
  行号: 690
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_index.html
  行号: 694
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_index.html
  行号: 700
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\templates\supplier\product_view.html
  行号: 325
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_view.html
  行号: 357
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_view.html
  行号: 455
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\product_view.html
  行号: 342
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_view.html
  行号: 346
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_view.html
  行号: 374
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_view.html
  行号: 378
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_view.html
  行号: 481
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\product_view.html
  行号: 485
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\school_index.html
  行号: 284
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\school_index.html
  行号: 320
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\school_index.html
  行号: 356
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\supplier\school_index.html
  行号: 301
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\school_index.html
  行号: 309
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\school_index.html
  行号: 337
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\school_index.html
  行号: 345
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\school_index.html
  行号: 373
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\supplier\school_index.html
  行号: 381
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\templates\weekly_menu\plan.html
  行号: 1678
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\templates\weekly_menu\plan_v2.html
  行号: 792
  模式: 'shown\.bs\.modal'
  匹配: 'shown.bs.modal'

  文件: app\static\js\image_uploader.js
  行号: 669
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\static\js\mobile-enhancements.js
  行号: 687
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\stock-in-detail-enhancement.js
  行号: 159
  模式: 'show\.bs\.modal'
  匹配: 'show.bs.modal'

  文件: app\static\js\stock-in-detail-enhancement.js
  行号: 163
  模式: 'hide\.bs\.modal'
  匹配: 'hide.bs.modal'

  文件: app\static\js\user_guide.js
  行号: 111
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\static\js\user_guide.js
  行号: 134
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\static\js\user_guide.js
  行号: 386
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\user_guide.js
  行号: 391
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\user_guide.js
  行号: 402
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\user_guide.js
  行号: 406
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\user_guide.js
  行号: 42
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\static\js\user_guide.js
  行号: 114
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\static\js\video-management.js
  行号: 10
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\static\js\video-management.js
  行号: 27
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\static\js\video-management.js
  行号: 77
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\weekly_menu_modal.js
  行号: 955
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\static\js\weekly_menu_modal.js
  行号: 1218
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\weekly_menu_modal.js
  行号: 364
  模式: 'show\.bs\.modal'
  匹配: 'show.bs.modal'

  文件: app\static\js\weekly_menu_modal.js
  行号: 369
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'

  文件: app\static\js\weekly_menu_v2.js
  行号: 958
  模式: \.modal\('show'\)
  匹配: .modal('show')

  文件: app\static\js\weekly_menu_v2.js
  行号: 1227
  模式: \.modal\('hide'\)
  匹配: .modal('hide')

  文件: app\static\js\weekly_menu_v2.js
  行号: 364
  模式: 'show\.bs\.modal'
  匹配: 'show.bs.modal'

  文件: app\static\js\weekly_menu_v2.js
  行号: 369
  模式: 'hidden\.bs\.modal'
  匹配: 'hidden.bs.modal'


